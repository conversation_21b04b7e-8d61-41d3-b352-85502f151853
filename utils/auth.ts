import Cookies from 'js-cookie';
import { signIn, signOut as firebaseSignOut, getCurrentUser, getAuth } from './firebase';
import { User, UserRole } from '@/models/auth';
import { apiClient, fetchJson } from './api';
import { UserCredential } from 'firebase/auth';

// Token management - Now using Firebase ID tokens
export const setToken = async (user: UserCredential['user']) => {
  const token = await user.getIdToken();
  Cookies.set('auth_token', token, { expires: 7 }); // Expires in 7 days
};

export const getToken = () => {
  return Cookies.get('auth_token');
};

export const removeToken = () => {
  Cookies.remove('auth_token');
};

export const isAuthenticated = () => {
  return !!getAuth().currentUser || !!getToken();
};

// Login with email and password
export const loginWithEmailPassword = async (
  email: string,
  password: string,
): Promise<{ success: boolean; user?: User }> => {
  try {
    const userCredential = await signIn(email, password);

    if (!userCredential) {
      return { success: false };
    }

    // Set the Firebase ID token in cookies
    await setToken(userCredential.user);

    // Get user data from Firestore
    const user = await getCurrentUser();

    return {
      success: true,
      user: user || undefined,
    };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false };
  }
};

// Sign out
export const logout = async (): Promise<boolean> => {
  try {
    await firebaseSignOut();
    removeToken();
    return true;
  } catch (error) {
    console.error('Logout error:', error);
    return false;
  }
};

// Staff signup with invite code
export const staffSignupWithCode = async (
  code: string,
  email: string,
  password: string,
  name: string,
): Promise<{ success: boolean; message: string }> => {
  try {
    // Verify the invite code using the API endpoint
    const verifyResponse = await fetch('/api/auth/verify-invite-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code }),
    });

    const verification = await verifyResponse.json();

    if (!verification.valid || !verification.clinicId) {
      return {
        success: false,
        message: verification.message || 'Invalid or expired invite code',
      };
    }

    // Create the user using the server-side API endpoint
    const createUserResponse = await fetch('/api/auth/create-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        name,
        role: UserRole.STAFF,
        clinicId: verification.clinicId,
      }),
    });

    const userData = await createUserResponse.json();

    if (!createUserResponse.ok) {
      throw new Error(userData.message || 'Failed to create user');
    }

    // Finalize invite: create MySQL user, assign role/locations, and mark code used
    await fetch('/api/staff/accept-invite', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code, email }),
    });

    // Automatically sign in the user
    try {
      const userCredential = await signIn(email, password);
      if (userCredential) {
        // Set the Firebase ID token in cookies
        await setToken(userCredential.user);
      }
    } catch (loginError) {
      console.error('Auto-login after signup failed:', loginError);
      // Continue despite login error - user is still created
    }

    return {
      success: true,
      message: 'Account created successfully',
    };
  } catch (error: unknown) {
    console.error('Signup error:', error);

    if (error instanceof Error) {
      // Handle specific error messages
      if (
        error.message.includes('auth/email-already-in-use') ||
        error.message.includes('Email already in use')
      ) {
        return {
          success: false,
          message: 'Email already in use. Please use a different email or login.',
        };
      } else if (
        error.message.includes('auth/invalid-email') ||
        error.message.includes('Invalid email')
      ) {
        return {
          success: false,
          message: 'Invalid email format.',
        };
      } else if (
        error.message.includes('auth/weak-password') ||
        error.message.includes('Password is too weak')
      ) {
        return {
          success: false,
          message: 'Password is too weak. Please use a stronger password.',
        };
      }
    }

    return {
      success: false,
      message: 'An error occurred during signup. Please try again.',
    };
  }
};

// Fetch current user data from API
export const fetchCurrentUser = async () => {
  try {
    if (!getToken()) {
      throw new Error('Not authenticated');
    }

    const response = await apiClient.get('/api/staff/me');
    return await fetchJson(response);
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

// Set up token refresh
export const setupTokenRefresh = () => {
  // Refresh token when user state changes
  getAuth().onIdTokenChanged(async user => {
    if (user) {
      await setToken(user);
    } else {
      removeToken();
    }
  });

  // Force token refresh every 30 minutes to prevent expiration
  // Firebase tokens typically expire after 1 hour
  const REFRESH_INTERVAL = 30 * 60 * 1000; // 30 minutes in milliseconds

  setInterval(async () => {
    const user = getAuth().currentUser;
    if (user) {
      try {
        // Force token refresh
        const newToken = await user.getIdToken(true);
        Cookies.set('auth_token', newToken, { expires: 7 });
        console.log('Firebase token refreshed successfully');
      } catch (error) {
        console.error('Error refreshing Firebase token:', error);
      }
    }
  }, REFRESH_INTERVAL);
};

// Helper function to handle expired token errors in API calls
export const handleApiCall = async (apiCall: () => Promise<Response>): Promise<Response> => {
  try {
    const response = await apiCall();

    // If we get an unauthorized or token expired error
    if (response.status === 401) {
      const responseBody = await response.json();

      // If token expired error, try to refresh token and retry
      if (
        responseBody.message?.includes('token-expired') ||
        responseBody.message?.includes('id-token-expired')
      ) {
        const user = getAuth().currentUser;
        if (user) {
          // Force token refresh
          const newToken = await user.getIdToken(true);
          Cookies.set('auth_token', newToken, { expires: 7 });

          // Retry the API call with the new token
          return await apiCall();
        }
      }

      throw new Error(responseBody.message || 'Unauthorized');
    }

    return response;
  } catch (error) {
    console.error('API call error:', error);
    throw error;
  }
};
