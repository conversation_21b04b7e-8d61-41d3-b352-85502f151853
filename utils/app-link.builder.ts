const {
  API_URL = 'https://api.frontdesk.doctor',
  DASHBOARD_URL = 'https://uretina-lmb.frontdesk.doctor',
} = process.env;

/**
 * Builds links to the app
 */
export class AppLinkBuilder {
  private static instance: AppLinkBuilder;

  private constructor(
    private readonly dashboardUrl: string,
    private readonly apiUrl: string,
  ) {}

  static getInstance(): AppLinkBuilder {
    if (!AppLinkBuilder.instance) {
      AppLinkBuilder.instance = new AppLinkBuilder(DASHBOARD_URL, API_URL);
    }

    return AppLinkBuilder.instance;
  }

  getDashboardLink(): string {
    return this.dashboardUrl;
  }

  getCallDetailsLink(callId: string): string {
    return `${this.dashboardUrl}/dashboard/calls/${callId}`;
  }

  getNotShowCallReminderEndpoint(): string {
    return `${this.apiUrl}/api/external-api/v2/jobs/not-show-call-reminder`;
  }

  getUrgentAfterHoursDoctorCallReminderEndpoint(): string {
    return `${this.apiUrl}/api/external-api/v2/jobs/urgent-after-hours-doctor-call-reminder`;
  }

  getCallClassificationUpdateEndpoint(): string {
    return `${this.apiUrl}/api/external-api/v2/jobs/call-classification-update`;
  }

  getCallHangUpEndpoint(): string {
    return `${this.apiUrl}/api/external-api/v2/calls/hang-up-handler`;
  }

  getEndConversationNoAuthEndpoint(): string {
    return `${this.apiUrl}/api/external-api/v2/calls/end-conversation-no-auth`;
  }

  getAfterHoursCallViewLink(afterHoursCallId: string, doctorId: string): string {
    return `${this.apiUrl}/api/calls/after-hours/view/${afterHoursCallId}?d=${doctorId}`;
  }

  getAdkWsEndpoint(userId: string, callId: string): string {
    return `wss://fd-prd-adk-test-agent-service-747921137606.us-east1.run.app/twilio/ws/conversations/users/${userId}/calls/${callId}`;
  }

  getAdkCallHangUpEndpoint(callId: string): string {
    return `${this.apiUrl}/api/external-api/v3/calls/${callId}/hang-up`;
  }

  getSaveVoicemailEndpoint(callId: string): string {
    return `${this.apiUrl}/api/external-api/v3/calls/${callId}/save-voicemail`;
  }
}
