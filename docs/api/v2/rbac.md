# RBAC APIs (Roles, Permissions, Overrides)

status: draft
last-updated: 2025-08-07

## Overview

Role- and permission-management endpoints for the Front Desk Staff Portal. These APIs are guarded by authentication and require the `ENABLE_NEW_RBAC` feature flag to be on. Most write operations require `STAFF_MANAGEMENT` at `WRITE` or `ADMIN` level.

- Base URL: `/api`
- Auth: Firebase ID token (Bearer or `auth_token` cookie)
- Feature Flag: `ENABLE_NEW_RBAC=true`

## Enums

### FeatureKey

`BILLING`, `ANSWERING_SERVICE`, `STAFF_MANAGEMENT`, `CALL_LOGS`, `DASHBOARD`, `NO_SHOW`, `BU<PERSON><PERSON>_SMS`, `PRACTICE_MANAGEMENT`, `USER_MANAGEMENT`, `ADMIN_TOOLS`

### PermissionLevel

`NONE`, `READ`, `WRITE`, `ADMIN`

---

## Roles

### Role model

- clinic_id: number | null — When NULL, role is global (typically a template). When set, role is scoped to that clinic and assignable there.
- is_system: boolean — System-managed role (seeded/built-in). Should not be edited/deleted via UI.
- is_template: boolean — Template role used for cloning. Not directly assignable to users. Clone to create a clinic-scoped role.

### GET /roles

Returns roles visible to the current user’s clinic (includes global templates only when explicitly fetched via templates endpoint).

- Auth: required
- Permissions: `STAFF_MANAGEMENT:READ` (enforced via wrapper)

Response 200

```json
[
  {
    "id": "r1",
    "clinic_id": 12,
    "name": "Practice Manager",
    "description": "All features except billing",
    "is_system": false,
    "is_template": false,
    "created_by": "user-123",
    "created_at": "2025-08-07T00:00:00.000Z",
    "updated_at": "2025-08-07T00:00:00.000Z"
  }
]
```

### POST /roles

Create a role for the current clinic with the provided grants.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:WRITE`

Request

```json
{
  "name": "Front Desk Read",
  "description": "Read-only desktop staff",
  "permissions": [
    { "feature": "CALL_LOGS", "level": "READ" },
    { "feature": "DASHBOARD", "level": "READ" }
  ]
}
```

Response 201

```json
{ "id": "uuid", "clinic_id": 12, "name": "Front Desk Read", "is_system": false, "is_template": false }
```

### GET /roles/[id]

Fetch a role by id.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:READ`

Response 200

```json
{ "id": "r1", "clinic_id": 12, "name": "Practice Manager", "is_template": false }
```

### PUT /roles/[id]

Update role metadata and optionally replace permissions.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:WRITE`

Request

```json
{
  "name": "Practice Manager",
  "description": "All features except billing",
  "permissions": [
    { "feature": "ANSWERING_SERVICE", "level": "ADMIN" },
    { "feature": "STAFF_MANAGEMENT", "level": "ADMIN" }
  ]
}
```

Response 200

```json
{ "id": "r1", "name": "Practice Manager", "is_template": false }
```

### DELETE /roles/[id]

Delete a role.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:WRITE`

Response 204 (no body)

### POST /roles/clone

Clone an existing role (and its grants) to a new role.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:WRITE`

Request

```json
{ "sourceRoleId": "r1", "name": "PM Copy", "clinicId": 12 }
```

Response 201

```json
{ "id": "uuid", "name": "PM Copy", "clinic_id": 12 }
```

### GET /roles/templates

List global templates and their combined grants.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:READ`

Response 200

```json
[
  { "id": "t1", "name": "Account Owner", "grants": "BILLING:ADMIN,..." },
  { "id": "t2", "name": "Practice Manager", "grants": "ANSWERING_SERVICE:ADMIN,..." }
]
```

---

## Permissions

### POST /permissions/check

Return the effective level for a user and feature, with optional location scope.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:READ` or self-check use cases (configure per gateway)

Request

```json
{ "userId": "user-123", "feature": "DASHBOARD", "locationId": "118" }
```

Response 200

```json
{ "level": "READ" }
```

---

## User Overrides & Matrix

### GET /users/[id]/permissions

Return the effective matrix for the user across all features (global scope). Location-scoped checks should use `/permissions/check` with `locationId`.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:ADMIN`

Response 200

```json
{
  "userId": "user-123",
  "effective": [
    { "feature": "CALL_LOGS", "level": "READ" },
    { "feature": "DASHBOARD", "level": "READ" }
  ]
}
```

### PUT /users/[id]/permissions

Upsert per-user overrides (rare exceptions). Overrides take precedence over roles and can be global or location-scoped. Optional `expiresAt` allows temporary exceptions.

- Auth: required
- Permissions: `STAFF_MANAGEMENT:ADMIN`

Request

```json
{
  "overrides": [
    { "feature": "CALL_LOGS", "level": "READ" },
    { "feature": "DASHBOARD", "level": "WRITE", "locationId": "118", "expiresAt": null }
  ]
}
```

Response 204 (no body)

---

## Errors

- 401 Unauthorized: missing/invalid token
- 403 Forbidden: insufficient permission
- 400 Bad Request: validation failure (Zod)
- 404 Not Found: `ENABLE_NEW_RBAC` off or resource missing
- 500 Internal Server Error

---

## Notes

- Data is scoped to the current user’s clinic. Global templates are read-only and fetched via `/roles/templates`.
- Effective permission precedence: override > role@location > role@global > `NONE` (deny-by-default).
- Overrides table supports `expires_at` to automatically ignore expired exceptions.
