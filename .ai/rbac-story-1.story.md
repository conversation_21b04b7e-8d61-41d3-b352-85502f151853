# Story: RBAC Foundation — DB Schema, Repositories, Templates, Flag

status: approved
owner: Frontdesk Team
last-updated: 2025-08-07
related-docs:
- .ai/prd.md
- .ai/arch.md
- USER_ROLES_PERMISSIONS_IMPLEMENTATION_PLAN.md

## Objective
Deliver the backend foundations for the new roles and permissions system with location scoping, without affecting current users. Ship behind a feature flag and include tests, seeding, and observability hooks.

## In Scope
- MySQL schema additions for RBAC
- Repository layer for roles/permissions and user assignments
- Permission resolution service with caching
- Basic permission middleware
- Default role templates and seeding script
- Feature flag and configuration
- Unit/integration tests for the above

## Out of Scope
- UI pages/components (Role management, Permission editor, Invite screens)
- Updating existing pages to use PermissionCheck
- Invitation acceptance flow UI

## Acceptance Criteria
- New MySQL tables exist: roles, role_permissions, user_roles, user_permission_overrides (with expires_at), role_audit_logs
- Migrations are idempotent and pass locally
- Default templates seeded: Account Owner, Practice Manager, Doctor, Staff, ReadOnly
- permission-service returns correct levels for:
  - No grants -> NONE
  - Single role (global) -> level from role
  - Multiple roles -> highest level wins
  - Location-scoped vs global precedence
  - User override beats roles
- permission-middleware usable as requirePermission(feature, minLevel, { locationFrom })
- Feature flag ENABLE_NEW_RBAC present and gating new checks
- Tests implemented with >= 80% coverage for new modules

## Tasks
- DB Migrations
  - Create migrations/002_rbac_schema.js to add tables and indexes
  - Include expires_at column and index in user_permission_overrides for temporary exceptions
- Types & Enums
  - Update models/auth.ts: add FeatureKey and PermissionLevel; extend StaffInviteCode with assignedRoleId?, assignedLocationIds?
- Repositories
  - lib/repositories/roles-repository.ts
  - lib/repositories/permissions-repository.ts
  - lib/repositories/user-permissions-repository.ts
- Templates & Seeding
  - utils/role-templates.ts with default templates
  - scripts/seed-rbac-templates.ts to seed system roles and permissions
- Services & Middleware
  - lib/services/permission-service.ts with algorithm + LRU cache
  - lib/middleware/permission-middleware.ts providing requirePermission
- Config & Flags
  - Add ENABLE_NEW_RBAC to app-config.ts (default: false)
- Observability
  - Structured logging on denials
- Tests
  - Unit tests for utils and permission-service
  - Repository integration tests
  - Middleware tests

## Test Plan
- Unit: precedence and overrides
- Integration: seeded templates with various user assignments
- Middleware: 403/200 on different minLevels and scoping
- Lint: no new lint errors; Typescript strict, no any
- Coverage: >= 80% for new modules

## Rollout & Migration
- Run migration 002_rbac_schema.js
- Seed templates (idempotent) via script
- Keep flag OFF initially; enable internally first

## Risks & Mitigation
- Performance: caching + indexes + batch queries
- Backward compatibility: gate behind flag
- Data integrity: constraints + validation in repositories

## Dependencies
- Existing MySQL connection and migration tooling
- Existing models/auth.ts and lib/database utilities

## Chat Log
- 2025-08-07: PRD approved by user
- 2025-08-07: Architecture approved by user

---

Please review and approve this story. After approval, I will implement the tasks above in a single PR behind ENABLE_NEW_RBAC and include tests and seeding.
