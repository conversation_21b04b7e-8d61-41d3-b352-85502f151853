# PRD: Roles and Permissions for Front Desk Staff Portal

status: approved
owner: Frontdesk Team
last-updated: 2025-08-07

## 1. Purpose

Implement a robust, location-scoped roles and permissions system for the Front Desk Staff Portal. The system must enable account owners and admins to manage users, assign roles, and control access to features and data per location, while providing an intuitive invite flow and administration UI.

## 2. Goals

- Fine-grained feature access control with location scoping
- Default role templates with the ability to clone/customize
- Invitation flow: assign role and locations at invite time
- Admin pages to manage users and account owners. Super admin can manage all users and account owners.
- Deny-by-default security with auditability and observability
- Backward-compatible rollout behind a feature flag

## 3. Non-Goals

- Billing functionality implementation beyond access gating
- Cross-tenant (cross-clinic) sharing of users or roles
- OAuth provider expansion or SSO changes

## 4. Scope

### Features under access control
- Billing
- Answering Service
- Staff Management
- Call Logs
- Dashboard
- No-Show
- Bulk SMS

### Default roles (templates)
- Account Owner: All features including Billing; all locations in clinic
- Practice Manager: All features except Billing; all locations in clinic
- Doctor: Call Logs, Dashboard, No-Show (location-scoped)
- Staff: Call Logs, Dashboard, No-Show (location-scoped)
- ReadOnly: Read-only visibility across enabled features

### Data visibility rules
- Users assigned to specific locations can view Call Logs, Dashboard, and No-Show only for those locations.
- Staff with Staff Management can assign roles and locations to users (WRITE/ADMIN).

## 5. User Stories

- As an Account Owner, I can invite a teammate by email, choose a role preset (Admin/Read-only/Restricted), and select location(s), so they only access necessary features and data.
- As an Admin, I can list, edit, deactivate, and reassign users, including roles and locations, within my clinic.
- As a Staff, I only see pages, actions, and data for locations I’m assigned to.
- As a Doctor, I can view Call Logs and Dashboard for my locations, but not manage staff or billing.

## 6. UX & Flows

### Invite flow
1) Admin clicks “Invite Staff”
2) Form fields: email, role preset (Admin, Read-only, Restricted), if Restricted then role selector and multi-select locations
3) Submit -> invite code generated, invite email sent
4) Invitee opens `/invite/[code]` page: confirm details, set name/password, accept
5) System creates user, assigns role and locations, logs audit event

### Admin management
- Users list with filters (role, location)
- User detail: edit role assignments, add/remove locations, deactivate
- Account owners admin page: list/manage account owner users

## 7. Architecture Overview

```mermaid
flowchart TD
  A[Client UI] --> B[Auth (Firebase ID Token)]
  B --> C[User Context (clinic, locations)]
  C --> D[Permission Service]
  D -->|resolve| E[(MySQL: roles, role_permissions, user_roles, overrides)]
  D --> F{Has permission?}
  F -- no --> G[403]
  F -- yes --> H[API Handler/UI]
```

Key patterns
- Deny-by-default; explicit FeatureKey + PermissionLevel
- Location-scoped role assignments allowed; global within clinic when `locationId` is null
- Precedence: user overrides > role at location > role global > default NONE
- Feature flag `ENABLE_NEW_RBAC` for gradual rollout

## 8. Data Model (MySQL)

- roles(id, clinic_id, name, description, is_system, is_template, created_by, timestamps)
- role_permissions(id, role_id, feature, level)
- user_roles(id, user_id, role_id, location_id)
- user_permission_overrides(id, user_id, feature, level, location_id, expires_at)
- role_audit_logs(id, actor_user_id, action, subject_id, payload, created_at)
- staff_invite_codes(+ assigned_role_id, assigned_location_ids JSON)

Indexes: by clinic, by feature, by (user, role, location), unique constraints to prevent duplicates.

## 9. API Endpoints

- Roles
  - GET/POST `/api/roles`
  - GET/PUT/DELETE `/api/roles/[id]`
  - POST `/api/roles/clone`
  - GET `/api/roles/templates`
- Permissions
  - POST `/api/permissions/check` -> { level }
  - GET/PUT `/api/users/[id]/permissions` (admin-only overrides)
- Invitations
  - POST `/api/staff/generate-invite-code`
  - POST `/api/staff/send-invite-email`
  - POST `/api/auth/verify-invite-code`
  - POST `/api/auth/create-user`
  - POST `/api/auth/mark-code-used`
- Admin
  - GET/POST `/api/admin/users` (list/create)
  - GET/PUT `/api/admin/users/[id]` (update/assign locations/roles)
  - GET/POST `/api/admin/account-owners`

## 10. Frontend

- Components
  - `PermissionCheck` wrapper: `{ feature, minLevel, locationId?, fallback? }`
  - Role management UI: list, form, clone, permission editor grid
  - Invitation UI: invite form, invitation list/status, role+location assignment, presets
- Pages
  - `/dashboard/roles` (list/create/edit)
  - `/dashboard/admin/users`, `/dashboard/admin/account-owners`
  - `/dashboard/staff/invite`, `/invite/[code]`
  - Permission gating on Calls/Dashboard/No-Show/Bulk SMS/Practice Mgmt/Billing

## 11. Security & Compliance

- Deny-by-default for unknown features
- Enforce clinic isolation; validate `locationId` belongs to the user’s clinic
- Sensitive route protection via permission middleware; server-side checks on all APIs
- Audit log on role creation/update/assignment
- Least privilege defaults; ReadOnly for auditors

## 12. Observability

- Logs for permission denials (userId, feature, locationId, reason)
- Metrics: 403 count per feature, resolution latency, cache hit ratio
- Alerts: spikes in 403, migration anomalies

## 13. Rollout Strategy

1) Ship migrations + repositories
2) Seed system roles and templates
3) Migrate existing users to `user_roles`
4) Enable feature for internal users -> pilot clinic -> gradual rollout
5) Remove legacy guards after full adoption

## 14. Risks & Mitigations

- Role sprawl: provide templates, limit custom roles, add usage analytics
- Scope confusion: consistent location scoping in APIs and UI; validation on write
- Performance: add caching, indexes, and batch loads
- Backward compatibility: feature flag and staged rollout

## 15. Acceptance Criteria

- Default roles behave as specified; cloning works
- Invite flow assigns role and locations correctly; email delivered
- Users only see features and data for their role/locations
- Admin can manage users and account owners
- All permission checks enforced in API and UI; tests pass (unit/integration/UI)

## 16. Open Questions

- Mapping of “Invite as Admin” preset to Account Owner vs Practice Manager – confirm desired default
- Whether ReadOnly should be clinic-wide or location-scoped (proposed: clinic-wide unless specified)
- Billing page route and ownership confirmation

---

Please review and approve so we can generate the architecture doc and first implementation story. 🚀

# 1. Title: PRD for External API v2 and Nextech Integration

<version>1.0.0</version>

## Status: In Review

## Intro

This PRD outlines the integration of the Nextech Practice+ APIs with our Front Desk Portal system. The goal is to create a modular and extensible External API v2 that will initially use Nextech as a provider for clinic data, locations, patients, users, and appointment booking functionality, while being designed to easily incorporate additional providers in the future.

## Goals

- Create a modular External API v2 architecture that supports multiple providers
- Implement Nextech Practice+ API as the first provider
- Enable secure access to patient data, clinics, locations, users and appointment booking
- Design a provider-agnostic interface that abstracts away provider-specific details
- Support future expansion with minimal changes to the core architecture
- Maintain backward compatibility with v1 API where possible

## Features and Requirements

- Functional requirements
  - Access to clinic information via Nextech API
  - Access to location information via Nextech API
  - Access to patient data via Nextech API
  - Access to user/practitioner data via Nextech API
  - Appointment booking functionality via Nextech API
  - Consistent error handling across providers
  - Proper authentication and authorization
  
- Non-functional requirements
  - High performance (response time < 500ms for 95% of requests)
  - High availability (99.9% uptime)
  - Scalability to handle increasing numbers of requests
  - Security compliance with healthcare data standards
  - Comprehensive logging and monitoring
  
- Integration requirements
  - OAuth 2.0 authentication with Nextech
  - Rate limiting management (20 requests per second per endpoint)
  - Pagination handling
  - Response format normalization

## Epic List

### Epic-1: External API v2 Core Architecture

### Epic-2: Nextech API Provider Implementation

### Epic-3: External API v2 Endpoints Implementation

### Epic-N: Future Provider Integrations (Beyond Scope of current PRD)

## Epic 1: Story List

- Story 1: External API v2 Core Architecture Design
  Status: ''
  Requirements:
  - Design provider-agnostic interfaces and contracts
  - Create adapter pattern for provider implementations
  - Design error handling and logging strategy
  - Create authentication and authorization strategy

- Story 2: External API v2 Base Implementation
  Status: ''
  Requirements:
  - Create provider registry
  - Implement provider factory
  - Create base controller classes
  - Implement middleware for request validation and authentication

## Epic 2: Story List

- Story 1: Nextech API Provider Authentication
  Status: ''
  Requirements:
  - Implement OAuth 2.0 authentication
  - Create token management and refresh strategy
  - Implement secure credential storage

- Story 2: Nextech API Basic Services
  Status: ''
  Requirements:
  - Implement clinic service
  - Implement location service
  - Implement base HTTP client with error handling and retries

- Story 3: Nextech API Patient and User Services
  Status: ''
  Requirements:
  - Implement patient service
  - Implement user/practitioner service
  - Create data mapping to common models

- Story 4: Nextech API Appointment Service
  Status: ''
  Requirements:
  - Implement appointment booking
  - Implement appointment search and retrieval
  - Implement appointment modification and cancellation

## Epic 3: Story List

- Story 1: Clinic and Location Endpoints
  Status: ''
  Requirements:
  - Create GET /api/external-api/v2/clinics endpoint
  - Create GET /api/external-api/v2/locations endpoint
  - Implement filtering and search capabilities

- Story 2: Patient and User Endpoints
  Status: ''
  Requirements:
  - Create GET /api/external-api/v2/patients endpoint
  - Create GET /api/external-api/v2/users endpoint
  - Implement proper authentication and authorization checks

- Story 3: Appointment Endpoints
  Status: ''
  Requirements:
  - Create GET /api/external-api/v2/appointments endpoint
  - Create POST /api/external-api/v2/appointments endpoint for booking
  - Create PATCH and DELETE methods for appointment management

## Epic 4: Office Hours & On-Call Doctors Integration

- **Story 15: Office Hours Integration - Foundation Services**
  - Status: In Progress
  - Implements foundational services for office hours detection and agent-to-location mapping. Includes Firestore integration, office hours utility service, and enhanced call session handler for office hours context.

- **Story 16: Database Schema & Infrastructure Setup**
  - Status: Not Started
  - Sets up Firestore collections, indexes, and security rules for agent-location mappings, on-call schedules, and notifications. Includes migration scripts, schema documentation, and seed data for testing.

- **Story 17: Location Office Hours Management API**
  - Status: Not Started
  - Enhances the location API with office hours CRUD operations, validation, and timezone handling. Adds dedicated endpoints for office hours management and status, and improves office hours utility services.

- **Story 18: On-Call Doctors Database & Models**
  - Status: Not Started
  - Implements models and service layer for on-call doctor schedules and notifications. Adds API endpoints for schedule management, eligible doctors, and conflict detection.

- **Story 19: SMS Notification System**
  - Status: Not Started
  - Implements SMS notification system for alerting on-call doctors after hours. Integrates with call session handler, adds notification logging, retry logic, and admin management endpoints.

## Technology Stack

| Technology | Description |
| ------------ | ------------------------------------------------------------- |
| Next.js | Server-side rendering and API framework |
| TypeScript | Type-safe language for backend development |
| Zod | Schema validation for API requests and responses |
| Jest | Testing framework |
| Swagger/OpenAPI | API documentation |
| Axios | HTTP client for provider API communication |

## Reference

### Provider Adapter Pattern

```mermaid
graph TD
    Client[API Client] --> ExternalAPIV2[External API v2 Controllers]
    ExternalAPIV2 --> ProviderRegistry[Provider Registry]
    ProviderRegistry --> ProviderFactory[Provider Factory]
    ProviderFactory --> NextechProvider[Nextech Provider]
    ProviderFactory --> FutureProvider1[Future Provider 1]
    ProviderFactory --> FutureProvider2[Future Provider 2]
    NextechProvider --> NextechAPIClient[Nextech API Client]
    FutureProvider1 --> Provider1APIClient[Provider 1 API Client]
    FutureProvider2 --> Provider2APIClient[Provider 2 API Client]
```

## Data Models, API Specs, Schemas, etc...

### Common Models

```typescript
// Clinic Model
interface Clinic {
  id: string;
  name: string;
  address: Address;
  phoneNumber: string;
  emailAddress?: string;
  website?: string;
  providerInfo: ProviderInfo;
}

// Location Model
interface Location {
  id: string;
  name: string;
  address: Address;
  clinicId: string;
  providerInfo: ProviderInfo;
}

// Provider Info Model
interface ProviderInfo {
  provider: string; // e.g., "nextech", "otherProvider"
  externalId: string; // ID in the provider's system
}
```

## Project Structure

```text
pages/api/external-api/
├── v1/                  # Existing v1 API implementation
├── v2/                  # New v2 API implementation
│   ├── index.ts         # Main API router
│   ├── controllers/     # API endpoint controllers
│   │   ├── clinics.ts
│   │   ├── locations.ts
│   │   ├── patients.ts
│   │   ├── users.ts
│   │   └── appointments.ts
│   ├── providers/       # Provider implementations
│   │   ├── index.ts     # Provider registry and factory
│   │   ├── types.ts     # Common provider interfaces
│   │   ├── nextech/     # Nextech provider implementation
│   │   │   ├── index.ts
│   │   │   ├── client.ts
│   │   │   ├── auth.ts
│   │   │   ├── services/
│   │   │   │   ├── clinic.ts
│   │   │   │   ├── location.ts
│   │   │   │   ├── patient.ts
│   │   │   │   ├── user.ts
│   │   │   │   └── appointment.ts
│   │   │   └── models.ts
│   │   └── [future-provider]/
│   ├── middleware/      # Middleware for authentication, validation, etc.
│   ├── utils/           # Utility functions
│   └── models/          # Common data models
├── index.ts             # Root API handler (redirects to latest version)
└── README.md            # API documentation
```

## Change Log

| Change               | Story ID | Description                            |
| -------------------- | -------- | -------------------------------------- |
| Initial draft        | N/A      | Initial draft PRD                      | 