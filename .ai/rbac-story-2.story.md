# Story: RBAC — Role Management and Permission APIs

status: approved
owner: Frontdesk Team
last-updated: 2025-08-07
related-docs:
- .ai/prd.md
- .ai/arch.md
- USER_ROLES_PERMISSIONS_IMPLEMENTATION_PLAN.md
- .ai/rbac-story-1.story.md

## Objective
Expose secure, clinic-scoped APIs for managing roles, cloning, templates, and effective/override permissions, with audit logging and validations.

## In Scope
- Roles CRUD API
- Role clone API
- Role templates endpoint
- User permissions APIs (effective matrix, overrides)
 - User permissions APIs (effective matrix, overrides with optional expires_at)
- Request validation and permission middleware wiring
- Audit logging for all writes
- Unit/integration tests

## Out of Scope
- UI pages and components (handled in later stories)

## Acceptance Criteria
- Endpoints exist and are documented:
  - GET/POST /api/roles
  - GET/PUT/DELETE /api/roles/[id]
  - POST /api/roles/clone
  - GET /api/roles/templates
  - POST /api/permissions/check (returns { level })
  - GET/PUT /api/users/[id]/permissions (effective matrix and per-user overrides)
- All writes create entries in role_audit_logs with actor, action, subject, payload
- All endpoints enforce clinic isolation and require sufficient permission
- Validations: role name uniqueness per clinic; templates immutable; cannot delete system roles; overrides limited to admin-only
 - Validations: if override.expires_at is in the past, treat as inactive (do not apply)
- Tests cover happy/deny paths and edge cases

## Tasks
- API Routes (Next.js)
  - pages/api/roles/index.ts: GET list by clinic, POST create { name, description, permissions[] }
  - pages/api/roles/[id].ts: GET details, PUT update, DELETE soft delete (if not system)
  - pages/api/roles/clone.ts: POST { sourceRoleId, name }
  - pages/api/roles/templates.ts: GET templates
  - pages/api/permissions/check.ts: POST { feature, locationId? } -> { level }
  - pages/api/users/[id]/permissions.ts: GET effective matrix, PUT overrides
- Middleware
  - Apply requirePermission with appropriate features/levels
  - Extract and validate clinicId and location ownership
- Validation
  - Zod schemas for payloads (name length, enum values, non-empty arrays)
- Repositories/Services
  - Extend roles/permissions repositories for listing/join queries used by APIs
  - Implement role-service methods: create/update/delete/clone with invariants
  - Wire permission-service for effective resolution and check endpoint
  - Implement audit logging utility used by routes
- Documentation
  - Update docs/api with request/response shapes and examples
- Tests
  - Unit tests for role-service invariants and cloning
  - Integration tests for each route (200/4xx/403), including clinic isolation and permission gating

## Test Plan
- Validate that creating/updating/deleting/clone writes audit logs
- Validate clinic isolation by attempting cross-clinic access -> 403
- Validate deny-by-default without sufficient permissions
- Validate permission check returns correct levels with and without locationId
- Ensure templates endpoint returns system templates only (read-only)

## Dependencies
- Story 1 foundations (migrations, repositories, services, middleware, templates)

## Chat Log
- 2025-08-07: Drafted per approved PRD/ARCH; awaiting approval

---

Please review and approve. After approval, I will implement these APIs behind ENABLE_NEW_RBAC and add tests.
