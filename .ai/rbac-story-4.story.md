# Story: RBAC — SUPER_ADMIN Platform UI: Create Clinic & Locations

status: approved
owner: Frontdesk Team
last-updated: 2025-08-08
related-docs:
- .ai/prd.md
- .ai/arch.md
- .ai/rbac-story-3.story.md

## Objective
Provide a minimal, internal SUPER_ADMIN-only UI to perform platform-scoped actions:
- Create a new clinic (and bootstrap its first clinic admin)
- Create locations for any clinic

The UI must use existing gated APIs, ensure auditability, and not be visible to non-super-admin users.

## In Scope
- New page: `/dashboard/admin/platform` (SUPER_ADMIN-only)
- Form A: Create Clinic (fields: clinicName, clinicLogoUrl?, clinicAddress?, clinicPhone?, adminEmail, adminPassword, adminName)
- Form B: Create Location (fields: clinicId, name, address, phone?, officeHours?, practiceId)
- Client-side calls to:
  - POST `/api/clinics/create`
  - POST `/api/locations`
- UI feedback, field validation, error handling
- Link to audit trail docs (audit logging already in handlers)

## Out of Scope
- Comprehensive platform admin console (search, pagination, edit/delete)
- Account owner lifecycle management UI
- Non-RBAC business logic changes beyond what’s required for SUPER_ADMIN flows

## Acceptance Criteria
- `/dashboard/admin/platform` renders only for SUPER_ADMIN; others see 403/redirect
- Create Clinic form submits to `/api/clinics/create` and shows success with returned IDs
- Create Location form:
  - For SUPER_ADMIN, can target any `clinicId`
  - For non-super-admin users, UI/route guarded and inaccessible
- All failed requests surface clear error messages
- No new lint errors; TypeScript strict; no `any`

## UI/UX Spec
- Route: `/dashboard/admin/platform`
- Layout:
  - Section 1: "Create Clinic" card with form + submit
  - Divider
  - Section 2: "Create Location" card with form + submit
- States: loading spinner on submit; success toast; error alert
- Access Gate: client-side render gate plus server-side protection (Next.js page guard)

## API Contracts
- Create Clinic
  - POST `/api/clinics/create`
  - Body
    ```json
    {
      "clinicName": "string",
      "clinicLogoUrl": "string?",
      "clinicAddress": "string?",
      "clinicPhone": "string?",
      "adminEmail": "string",
      "adminPassword": "string",
      "adminName": "string"
    }
    ```
  - Response: 200 `{ success, clinicId, adminId }` or error

- Create Location
  - POST `/api/locations`
  - Body
    ```json
    {
      "clinicId": "number",
      "name": "string",
      "address": "string",
      "phone": "string?",
      "officeHours": { "Mon": { "start": "09:00", "end": "17:00" } }?,
      "practiceId": "string"
    }
    ```
  - Response: 201 `{ success, location, message }` or error

## Required Backend Adjustments
- `pages/api/locations/index.ts` currently requires `user.clinicId` and does not accept `clinicId` in body.
  - Update handler to allow SUPER_ADMIN to pass `clinicId` explicitly; default to `user.clinicId` for clinic admins
  - Keep RBAC gating for `LOCATION_CREATE:ADMIN` (SUPER_ADMIN bypass applies)
  - Continue audit logging on success

## Tasks
- Page & Access Control
  - Add Next.js page: `pages/dashboard/admin/platform.tsx`
  - Server-side auth check to ensure SUPER_ADMIN (via permission check or role claim), else redirect 403
  - Optional client-side `PermissionCheck` wrapper for extra safety

- Forms & Calls
  - Build Create Clinic form and POST to `/api/clinics/create`
  - Build Create Location form and POST to `/api/locations`
  - Show success and error states; basic field validations

- Backend tweak
  - Adjust `/api/locations` POST to accept `clinicId` in body when caller is SUPER_ADMIN

- Docs & Observability
  - Update `docs/api` to mention platform UI and SUPER_ADMIN-only flows
  - Ensure denial logs remain structured (already implemented)

- Tests
  - Unit: form validators, helpers
  - Integration (API):
    - POST `/api/locations` accepts `clinicId` from SUPER_ADMIN; denies non-admin
  - E2E-light (optional, behind flag): page access gating renders for SUPER_ADMIN only

## Test Plan
- Access
  - Visit `/dashboard/admin/platform` as non-super-admin → 403/redirect
  - Visit as SUPER_ADMIN → page renders
- Create Clinic
  - Submit valid payload → 200 and UI shows success with IDs
  - Missing required fields → client-side validation blocks; server returns 400 when bypassed
- Create Location
  - As SUPER_ADMIN with explicit `clinicId` → 201 success
  - As clinic admin → 403 (page hidden); API forbidden
- Lint & Coverage
  - No new lint errors; >80% coverage for new helpers and modified API

## Rollout & Feature Flag
- Respect `ENABLE_NEW_RBAC`
- Ship page hidden unless SUPER_ADMIN present

## Risks & Mitigation
- Misassignment of locations to wrong clinic: require `clinicId` confirmation and success messaging
- Exposure to non-admins: double gate (server + client)

## Dependencies
- Story 3 (SUPER_ADMIN role, middleware, API gating, audits)

## Chat Log
- 2025-08-08: Drafted UI follow-up for SUPER_ADMIN platform actions; requires minor API tweak for `clinicId` passthrough on locations
