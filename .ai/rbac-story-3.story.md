# Story: RBAC — Super Admin Platform Role & Platform Gating

status: approved
owner: Frontdesk Team
last-updated: 2025-08-08
related-docs:
- .ai/prd.md
- .ai/arch.md
- .ai/rbac-story-1.story.md
- .ai/rbac-story-2.story.md

## Objective
Introduce a platform-scoped `SUPER_ADMIN` role that can view/manage all clinics and locations and perform platform-level actions (create clinics, create locations, invite account owners). Implement seeding, permission resolution bypass, strict API gating, and comprehensive tests without impacting non-admin users.

## In Scope
- Define and seed a system `SUPER_ADMIN` role (platform scope via `clinic_id = null`)
- Permission resolution changes to grant `SUPER_ADMIN` full access and bypass clinic/location isolation
- API middleware gating for platform-level endpoints (create clinics, create locations, invite account owners)
- Documentation and observability for denials and admin actions
- Unit and integration tests; >=80% coverage for new logic

## Out of Scope
- UI pages for super admin dashboards
- Cross-tenant billing or payment administration
- Changes to non-RBAC business logic beyond access control

## Acceptance Criteria
- A `SUPER_ADMIN` system role exists in the database with `clinic_id = null`, `is_system = true`, not deletable/immutable via normal APIs
- Seeding is idempotent and creates `SUPER_ADMIN` when missing
- Permission resolution treats users with `SUPER_ADMIN` as:
  - Allowed for all `FeatureKey`s at the maximum `PermissionLevel`
  - Exempt from clinic/location scoping (may act across all clinics/locations)
- Platform endpoints are gated to `SUPER_ADMIN` only, including at minimum:
  - Create clinic (e.g., `pages/api/clinics/create.ts`)
  - Create location (e.g., POST in `pages/api/locations/index.ts`)
  - Account owners management (e.g., `pages/api/admin/account-owners` routes)
  - Staff invite for account owners (e.g., relevant `pages/api/staff/*` and `pages/api/auth/*` invite endpoints)
- Non-super admin users receive `403` on the above endpoints regardless of clinic/location assignments
- All super admin actions are audit logged (actor, action, subject, payload, timestamp)
- Tests cover: seeding, bypass logic, gating of endpoints, audit logging, and deny paths

## Tasks
- Types & Enums
  - Extend `FeatureKey` to include platform actions if not already present (e.g., `PLATFORM_ADMIN`, `CLINIC_CREATE`, `LOCATION_CREATE`, `INVITE_ACCOUNT_OWNER`)
  - Ensure `PermissionLevel` supports a maximum level used for bypass comparisons

- Templates & Seeding
  - Update `utils/role-templates.ts` to define `SUPER_ADMIN` with full platform feature set
  - Update `scripts/seed-rbac-templates.ts` to seed `SUPER_ADMIN` with `clinic_id = null`, `is_system = true`

- Services & Middleware
  - Update `lib/services/permission-service.ts` to:
    - Detect `SUPER_ADMIN` assignment (role with name/SYSTEM flag and `clinic_id = null`)
    - Short-circuit to maximum level for any feature and ignore clinic/location scoping
  - Update `lib/middleware/permission-middleware.ts` to:
    - Recognize `SUPER_ADMIN` for immediate allow on any `requirePermission`
    - Provide structured denial logs for non-super admin attempts on platform APIs

- API Routes Wiring (Gating)
  - Apply `requirePermission` with platform features to:
    - `pages/api/clinics/create.ts` (require `CLINIC_CREATE` at ADMIN level or `SUPER_ADMIN`)
    - `pages/api/locations/index.ts` (POST, require `LOCATION_CREATE` or `SUPER_ADMIN`)
    - `pages/api/admin/account-owners` routes (require `PLATFORM_ADMIN` or `SUPER_ADMIN`)
    - Staff invite flow endpoints that create account owners (require `INVITE_ACCOUNT_OWNER` or `SUPER_ADMIN`)
  - Ensure audit logging is called on successful writes

- Documentation & Observability
  - Update `.ai/arch.md` RBAC section with super admin precedence and scoping bypass
  - Update API docs to reflect `SUPER_ADMIN`-only routes
  - Add structured logs for denials: userId, endpoint, feature, reason

- Tests
  - Unit tests: permission-service bypass logic, feature-level checks, scoping bypass
  - Integration tests: gated routes return 403 for non-super admin and 200 for super admin
  - Seeding tests: idempotent creation of `SUPER_ADMIN`
  - Audit tests: verify audit logs written on super admin actions

## Test Plan
- Permission Resolution
  - When user has `SUPER_ADMIN`, `check(feature, locationId?)` returns max level and ignores clinic/location
  - When user does not have `SUPER_ADMIN`, legacy precedence and scoping apply unchanged

- API Gating
  - POST create clinic: 403 for non-super admin, 200 for super admin
  - POST create location: 403 for non-super admin, 200 for super admin
  - Account owners admin endpoints: 403 for non-super admin, 200 for super admin
  - Invite account owner: 403 for non-super admin, 200 for super admin

- Seeding & Audit
  - Running seeding twice keeps a single `SUPER_ADMIN` role
  - Super admin write actions produce audit entries with actor and payload

- Lint & Coverage
  - No new lint errors; strict TypeScript, no `any`
  - >= 80% coverage across new modules and changes

## Rollout & Migration
- Deploy changes behind `ENABLE_NEW_RBAC` (respect existing flag)
- Seed `SUPER_ADMIN` on deploy (idempotent)
- Assign `SUPER_ADMIN` to designated platform operators only
- Monitor logs for denials and audit correctness

## Risks & Mitigation
- Excessive privilege: restrict assignment to super admins via manual/secure process; immutable system role
- Accidental data modification: enforce audits and alerts on platform actions
- Backward compatibility: changes respect flag and do not alter non-platform routes’ behavior

## Dependencies
- Story 1: Foundations (migrations, repositories, permission-service baseline, middleware, templates)
- Story 2: Role/Permission APIs (for visibility, effective checks, and overrides)

## Chat Log
- 2025-08-08: Drafted per user request to introduce platform `SUPER_ADMIN` role and gating of platform APIs


