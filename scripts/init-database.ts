#!/usr/bin/env ts-node

import * as dotenv from 'dotenv';
import { mysqlService } from '../lib/database/mysql-service';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function initializeDatabase(): Promise<void> {
  console.log('🚀 Starting database initialization...');

  try {
    // Initialize MySQL connection
    await mysqlService.initialize();

    // Run database migrations
    console.log('📝 Running database migrations...');
    await mysqlService.runMigrations();

    // Test the connection
    const healthCheck = await mysqlService.healthCheck();
    console.log('🔍 Database health check:', healthCheck);

    if (healthCheck.status === 'healthy') {
      console.log('✅ Database initialization completed successfully!');
      console.log('📊 Database is ready for use');
    } else {
      console.error('❌ Database health check failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await mysqlService.close();
  }
}

// Run the initialization if this script is executed directly
if (require.main === module) {
  initializeDatabase().catch(error => {
    console.error('Fatal error during database initialization:', error);
    process.exit(1);
  });
}

export { initializeDatabase };
