/**
 * This script deletes Firebase Authentication users and Firestore documents for clinic 12
 * Run with: node scripts/delete-clinic-12-users.js
 *
 * WARNING: This will permanently delete users! Make sure to backup data first.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Clinic 12 user emails from temp-users-clinic-12.json
const CLINIC_12_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

async function backupUsersData(users) {
  const backupData = [];

  for (const user of users) {
    try {
      // Get Firestore user data
      const userDoc = await db.collection('users').doc(user.uid).get();
      const userData = userDoc.exists ? userDoc.data() : null;

      backupData.push({
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        disabled: user.disabled,
        customClaims: user.customClaims,
        metadata: {
          creationTime: user.metadata.creationTime,
          lastSignInTime: user.metadata.lastSignInTime,
        },
        firestoreData: userData,
      });
    } catch (error) {
      console.error(`Error backing up user ${user.email}:`, error.message);
    }
  }

  // Save backup to file with timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFilename = `clinic-12-users-backup-${timestamp}.json`;
  fs.writeFileSync(backupFilename, JSON.stringify(backupData, null, 2));
  console.log(`Backup saved to: ${backupFilename}`);

  return backupFilename;
}

async function deleteClinic12Users() {
  try {
    console.log('Starting deletion of Clinic 12 users from Firebase...');
    console.log('WARNING: This will permanently delete users!');

    // Get all Firebase Auth users
    const allUsers = await admin.auth().listUsers();
    console.log(`Total Firebase Auth users: ${allUsers.users.length}`);

    // Filter users for clinic 12
    const clinic12Users = [];

    for (const user of allUsers.users) {
      // Check if user has clinic 12 in custom claims
      if (user.customClaims && user.customClaims.clinicId === 12) {
        clinic12Users.push(user);
      }
      // Also check by email for safety
      else if (user.email && CLINIC_12_EMAILS.includes(user.email.toLowerCase())) {
        clinic12Users.push(user);
      }
    }

    console.log(`Found ${clinic12Users.length} clinic 12 users to delete:`);
    clinic12Users.forEach(user => {
      console.log(`- ${user.email} (${user.displayName || 'No name'}) - UID: ${user.uid}`);
    });

    if (clinic12Users.length === 0) {
      console.log('No clinic 12 users found. Exiting.');
      return;
    }

    // Create backup before deletion
    console.log('\nCreating backup of user data...');
    const backupFile = await backupUsersData(clinic12Users);

    // Confirmation prompt (in a real scenario, you might want to add readline for interactive confirmation)
    console.log('\n⚠️  FINAL WARNING: About to delete these users permanently!');
    console.log('Press Ctrl+C to cancel, or wait 10 seconds to proceed...');

    // Wait 10 seconds for user to cancel
    await new Promise(resolve => setTimeout(resolve, 10000));

    let deletedCount = 0;
    let errorCount = 0;
    const deletionResults = [];

    // Delete each user
    for (const user of clinic12Users) {
      try {
        // Delete from Firestore first
        try {
          await db.collection('users').doc(user.uid).delete();
          console.log(`Deleted Firestore document for: ${user.email}`);
        } catch (firestoreError) {
          console.warn(
            `Warning: Could not delete Firestore doc for ${user.email}:`,
            firestoreError.message,
          );
        }

        // Delete from Firebase Auth
        await admin.auth().deleteUser(user.uid);
        console.log(`Deleted Firebase Auth user: ${user.email} (${user.uid})`);

        deletionResults.push({
          email: user.email,
          uid: user.uid,
          status: 'deleted',
          timestamp: new Date().toISOString(),
        });

        deletedCount++;
      } catch (error) {
        console.error(`Error deleting user ${user.email}:`, error.message);

        deletionResults.push({
          email: user.email,
          uid: user.uid,
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString(),
        });

        errorCount++;
      }
    }

    // Save deletion results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const resultsFilename = `clinic-12-deletion-results-${timestamp}.json`;
    fs.writeFileSync(resultsFilename, JSON.stringify(deletionResults, null, 2));

    console.log('\n=== DELETION SUMMARY ===');
    console.log(`Successfully deleted: ${deletedCount} users`);
    console.log(`Errors: ${errorCount} users`);
    console.log(`Backup saved to: ${backupFile}`);
    console.log(`Results saved to: ${resultsFilename}`);

    if (errorCount > 0) {
      console.log('\n⚠️  Some users could not be deleted. Check the results file for details.');
    }
  } catch (error) {
    console.error('Error in deleteClinic12Users:', error);
    process.exit(1);
  }
}

// Verify environment before running
if (
  !process.env.FIREBASE_PROJECT_ID ||
  !process.env.FIREBASE_CLIENT_EMAIL ||
  !process.env.FIREBASE_PRIVATE_KEY
) {
  console.error('Missing required Firebase environment variables');
  console.error(
    'Make sure .env.local contains FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY',
  );
  process.exit(1);
}

// Run the function
deleteClinic12Users()
  .then(() => {
    console.log('\nDeletion process completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to delete users:', error);
    process.exit(1);
  });
