/**
 * This script updates a user's email address in Firebase Auth, Firestore, and MySQL
 * Run with: node scripts/update-user-email.js
 */

const admin = require('firebase-admin');
const fs = require('fs');
require('dotenv').config({ path: '.env' });

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Email addresses to update
const OLD_EMAIL = '<EMAIL>';
const NEW_EMAIL = '<EMAIL>';

async function updateUserEmail() {
  try {
    console.log(`Starting email update: ${OLD_EMAIL} -> ${NEW_EMAIL}`);

    // Step 1: Find user by old email
    console.log('\nStep 1: Finding user by old email...');
    const userRecord = await admin.auth().getUserByEmail(OLD_EMAIL);
    console.log(`Found user: ${userRecord.displayName} (${userRecord.uid})`);

    // Step 2: Check if new email already exists
    console.log('\nStep 2: Checking if new email already exists...');
    try {
      await admin.auth().getUserByEmail(NEW_EMAIL);
      console.error(`ERROR: Email ${NEW_EMAIL} already exists in Firebase Auth!`);
      process.exit(1);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        console.log('New email is available ✓');
      } else {
        throw error;
      }
    }

    // Step 3: Backup current user data
    console.log('\nStep 3: Creating backup of current user data...');
    const firestoreDoc = await db.collection('users').doc(userRecord.uid).get();
    const firestoreData = firestoreDoc.exists ? firestoreDoc.data() : null;

    const backup = {
      uid: userRecord.uid,
      oldEmail: userRecord.email,
      newEmail: NEW_EMAIL,
      displayName: userRecord.displayName,
      customClaims: userRecord.customClaims,
      firestoreData: firestoreData,
      timestamp: new Date().toISOString(),
    };

    const backupFilename = `user-email-update-backup-${Date.now()}.json`;
    fs.writeFileSync(backupFilename, JSON.stringify(backup, null, 2));
    console.log(`Backup saved to: ${backupFilename}`);

    // Step 4: Update Firebase Auth email
    console.log('\nStep 4: Updating Firebase Authentication email...');
    await admin.auth().updateUser(userRecord.uid, {
      email: NEW_EMAIL,
    });
    console.log('Firebase Auth email updated ✓');

    // Step 5: Update Firestore document
    console.log('\nStep 5: Updating Firestore document...');
    if (firestoreData) {
      await db.collection('users').doc(userRecord.uid).update({
        email: NEW_EMAIL,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log('Firestore document updated ✓');
    } else {
      console.log('No Firestore document found for this user');
    }

    // Step 6: Update MySQL database if dual-database is enabled
    console.log('\nStep 6: Checking for MySQL database...');
    try {
      // Check if MySQL configuration exists
      if (process.env.DB_HOST && process.env.DB_NAME) {
        const mysql = require('mysql2/promise');

        const connection = await mysql.createConnection({
          host: process.env.DB_HOST,
          user: process.env.DB_USER,
          password: process.env.DB_PASSWORD,
          database: process.env.DB_NAME,
          port: process.env.DB_PORT || 3306,
        });

        // Update user email in MySQL users table
        const [result] = await connection.execute(
          'UPDATE users SET email = ?, updated_at = NOW() WHERE id = ?',
          [NEW_EMAIL, userRecord.uid],
        );

        await connection.end();

        if (result.affectedRows > 0) {
          console.log('MySQL database updated ✓');
        } else {
          console.log('No user found in MySQL database (this is OK if not using dual-database)');
        }
      } else {
        console.log('MySQL not configured, skipping database update');
      }
    } catch (mysqlError) {
      console.warn(
        'MySQL update failed (this might be OK if not using dual-database):',
        mysqlError.message,
      );
    }

    // Step 7: Verify the update
    console.log('\nStep 7: Verifying the update...');
    const updatedUser = await admin.auth().getUser(userRecord.uid);
    console.log(`Verified: User email is now ${updatedUser.email}`);

    const updatedFirestoreDoc = await db.collection('users').doc(userRecord.uid).get();
    if (updatedFirestoreDoc.exists) {
      const updatedFirestoreData = updatedFirestoreDoc.data();
      console.log(`Verified: Firestore email is now ${updatedFirestoreData.email}`);
    }

    console.log('\n=== UPDATE SUMMARY ===');
    console.log(`✓ Firebase Auth: ${OLD_EMAIL} -> ${NEW_EMAIL}`);
    console.log(`✓ Firestore: Updated user document`);
    console.log(`✓ Backup saved: ${backupFilename}`);
    console.log('\nEmail update completed successfully!');
  } catch (error) {
    console.error('Error updating user email:', error);

    if (error.code === 'auth/user-not-found') {
      console.error(`User with email ${OLD_EMAIL} not found in Firebase Auth`);
    } else if (error.code === 'auth/email-already-exists') {
      console.error(`Email ${NEW_EMAIL} already exists in Firebase Auth`);
    }

    process.exit(1);
  }
}

// Verify environment before running
if (
  !process.env.FIREBASE_PROJECT_ID ||
  !process.env.FIREBASE_CLIENT_EMAIL ||
  !process.env.FIREBASE_PRIVATE_KEY
) {
  console.error('Missing required Firebase environment variables');
  console.error(
    'Make sure .env.local contains FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY',
  );
  process.exit(1);
}

// Run the function
updateUserEmail()
  .then(() => {
    console.log('\nEmail update process completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to update user email:', error);
    process.exit(1);
  });
