/*
  Sync Firestore users -> MySQL users table.
  - Upsert users that exist in Firestore
  - Optionally delete MySQL users that no longer exist in Firestore (safety: off by default)
*/
import * as dotenv from 'dotenv';
import { mysqlService } from '../lib/database/mysql-service';
import { UsersRepository } from '../lib/repositories/users-repository';
import { User } from '../models/auth';

async function main() {
  // Load environment
  dotenv.config({ path: '.env' });

  // Ensure Firebase Admin is initialized BEFORE any repository that may access Firestore
  const { default: admin } = await import('../utils/firebase-admin');

  const shouldDeleteMissing = true;
  await mysqlService.initialize();

  const repo = new UsersRepository();

  console.log('Loading Firestore users...');
  const snapshot = await admin.firestore().collection('users').get();
  const fsUsers: User[] = snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      email: data.email || '',
      phone: data.phone || undefined,
      name: data.name || '',
      role: data.role,
      specialty: data.specialty || undefined,
      clinicId: data.clinicId ?? null,
      profilePicture: data.profilePicture || undefined,
      canTakeAppointments: data.canTakeAppointments ?? false,
      locationIds: data.locationIds || [],
      currentLocationId: data.currentLocationId || undefined,
      practiceIds: data.practiceIds || undefined,
      createdAt: data.createdAt?.toDate?.() || new Date(),
      updatedAt: data.updatedAt?.toDate?.() || new Date(),
    } as User;
  });

  console.log(`Firestore users: ${fsUsers.length}`);

  // Build set for quick lookup
  const fsIds = new Set(fsUsers.map(u => u.id));

  // Upsert Firestore users into MySQL
  for (const u of fsUsers) {
    // eslint-disable-next-line no-await-in-loop
    const existing = await repo.findById(u.id, { forceMySQL: true });
    if (existing) {
      // eslint-disable-next-line no-await-in-loop
      await repo.update(u.id, {
        email: u.email,
        phone: u.phone,
        name: u.name,
        role: u.role,
        specialty: u.specialty,
        clinicId: u.clinicId,
        profilePicture: u.profilePicture,
        canTakeAppointments: u.canTakeAppointments,
        locationIds: u.locationIds,
        currentLocationId: u.currentLocationId,
        practiceIds: u.practiceIds,
      });
    } else {
      // eslint-disable-next-line no-await-in-loop
      await repo.create(
        {
          email: u.email,
          phone: u.phone,
          name: u.name,
          role: u.role,
          specialty: u.specialty,
          clinicId: u.clinicId,
          profilePicture: u.profilePicture,
          canTakeAppointments: u.canTakeAppointments,
          locationIds: u.locationIds,
          currentLocationId: u.currentLocationId,
          practiceIds: u.practiceIds,
          createdAt: u.createdAt,
          updatedAt: u.updatedAt,
        },
        { generateEntityId: () => u.id },
      );
    }
  }

  if (shouldDeleteMissing) {
    console.log('Checking for MySQL users not present in Firestore...');
    const all = await repo.findMany({ where: {}, limit: 10000 });
    for (const m of all.items) {
      if (!fsIds.has(m.id)) {
        // eslint-disable-next-line no-await-in-loop
        await repo.delete(m.id);
        console.log(`Deleted MySQL user not in Firestore: ${m.id}`);
      }
    }
  }

  await mysqlService.close();
  console.log('✅ Sync complete');
}

main().catch(err => {
  console.error('Failed to sync users', err);
  process.exit(1);
});
