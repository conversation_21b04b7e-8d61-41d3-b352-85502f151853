/*
  Bootstrap RBAC for existing users:
  - Clone global role templates per clinic (Practice Manager, Doctor, Staff, ReadOn<PERSON>, Account Owner)
  - Assign users to clinic-scoped roles based on legacy UserRole
*/
import * as dotenv from 'dotenv';
import { randomUUID } from 'crypto';
import { mysqlService } from '@/lib/database/mysql-service';
import { UserRole } from '@/models/auth';

type Template = {
  id: string;
  name: string;
  description: string | null;
};

async function ensureClinicRoleFromTemplate(clinicId: number, template: Template): Promise<string> {
  const existing = await mysqlService.query<{ id: string }>(
    'SELECT id FROM roles WHERE clinic_id = ? AND name = ? LIMIT 1',
    [clinicId, template.name],
  );
  if (existing.length > 0) return existing[0].id;

  const newRoleId = randomUUID();
  await mysqlService.query(
    `INSERT INTO roles (id, clinic_id, name, description, is_system, is_template, created_by)
     VALUES (?, ?, ?, ?, FALSE, FALSE, 'rbac-bootstrap')`,
    [newRoleId, clinicId, template.name, template.description],
  );

  await mysqlService.query(
    `INSERT INTO role_permissions (role_id, feature, level)
     SELECT ?, feature, level FROM role_permissions WHERE role_id = ?`,
    [newRoleId, template.id],
  );

  return newRoleId;
}

async function assignUserToRoleIfMissing(userId: string, roleId: string): Promise<void> {
  await mysqlService.query(
    `INSERT INTO user_roles (user_id, role_id, location_id)
     VALUES (?, ?, NULL)
     ON DUPLICATE KEY UPDATE role_id = role_id`,
    [userId, roleId],
  );
}

async function main() {
  dotenv.config({ path: '.env' });
  await mysqlService.initialize();

  // Load global templates
  const templates = await mysqlService.query<Template>(
    `SELECT id, name, description FROM roles WHERE clinic_id IS NULL AND is_template = TRUE`,
    [],
  );
  const templateByName = new Map<string, Template>(templates.map(t => [t.name, t]));

  const requiredTemplates = ['Practice Manager', 'Doctor', 'Staff', 'ReadOnly', 'Account Owner'];
  for (const name of requiredTemplates) {
    if (!templateByName.has(name)) {
      throw new Error(`Missing required global template: ${name}`);
    }
  }

  // Find clinics with users
  const clinicRows = await mysqlService.query<{ clinic_id: number }>(
    `SELECT DISTINCT clinic_id FROM users WHERE clinic_id IS NOT NULL`,
    [],
  );

  for (const row of clinicRows) {
    const clinicId = row.clinic_id;
    console.log(`\nProcessing clinic ${clinicId}`);

    // Ensure clinic-scoped roles exist
    const practiceManagerRoleId = await ensureClinicRoleFromTemplate(
      clinicId,
      templateByName.get('Practice Manager')!,
    );
    const doctorRoleId = await ensureClinicRoleFromTemplate(
      clinicId,
      templateByName.get('Doctor')!,
    );
    const staffRoleId = await ensureClinicRoleFromTemplate(clinicId, templateByName.get('Staff')!);

    // Map legacy roles to RBAC roles
    type LegacyUserRole = UserRole | 'DOCTOR';
    const users = await mysqlService.query<{
      id: string;
      role: LegacyUserRole;
      clinic_id: number | null;
    }>(`SELECT id, role, clinic_id FROM users WHERE clinic_id = ?`, [clinicId]);

    for (const u of users) {
      let targetRoleId: string | null = null;
      if (u.role === UserRole.CLINIC_ADMIN) targetRoleId = practiceManagerRoleId;
      else if (u.role === 'DOCTOR') targetRoleId = doctorRoleId;
      else if (u.role === UserRole.STAFF) targetRoleId = staffRoleId;
      else if (u.role === UserRole.SUPER_ADMIN) {
        // For now, also grant Practice Manager in their clinic for UI access
        targetRoleId = practiceManagerRoleId;
      }

      if (targetRoleId) {
        await assignUserToRoleIfMissing(u.id, targetRoleId);
        console.log(`Assigned ${u.id} -> role ${targetRoleId}`);
      }
    }
  }

  await mysqlService.close();
  console.log('✅ RBAC bootstrap complete');
}

main().catch(err => {
  console.error('RBAC bootstrap failed', err);
  process.exit(1);
});
