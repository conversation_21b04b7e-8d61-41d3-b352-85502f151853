/*
  Backfill firebase_uid for existing MySQL users by matching Firebase Auth / Firestore via email

  Usage:
    pnpm ts-node -r tsconfig-paths/register -O '{"module":"commonjs"}' scripts/backfill-firebase-uid.ts
*/
import * as dotenv from 'dotenv';
import { mysqlService } from '@/lib/database/mysql-service';
// IMPORTANT: Load env before initializing firebase-admin
dotenv.config({ path: '.env.local' });
if (!process.env.FIREBASE_PROJECT_ID) {
  dotenv.config({ path: '.env' });
}
// Use direct firebase-admin to ensure controlled initialization in scripts
// eslint-disable-next-line @typescript-eslint/no-var-requires
const admin = require('firebase-admin') as typeof import('firebase-admin');
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
  });
}

type DbUser = { id: string; email: string | null; firebase_uid: string | null };

async function main(): Promise<void> {
  // env already loaded above
  await mysqlService.initialize();
  const dryRun = process.env.DRY_RUN === 'true';

  console.log('🔎 Fetching users missing firebase_uid…');
  const users = await mysqlService.query<DbUser>(
    `SELECT id, email, firebase_uid FROM users WHERE firebase_uid IS NULL OR firebase_uid = ''`,
  );
  console.log(`Found ${users.length} users to backfill`);

  let updated = 0;
  for (const u of users) {
    const email = (u.email || '').trim();
    if (!email) {
      console.warn(`Skipping user ${u.id} — missing email`);
      continue;
    }

    try {
      // Prefer Firebase Auth for authoritative UID
      const userRecord = await admin.auth().getUserByEmail(email);
      const firebaseUid = userRecord.uid;
      console.log(`✔  ${email} -> ${firebaseUid}`);
      if (!dryRun) {
        await mysqlService.query(`UPDATE users SET firebase_uid = ? WHERE id = ?`, [
          firebaseUid,
          u.id,
        ]);
      }
      updated += 1;
      continue;
    } catch (e) {
      console.warn(`Auth lookup failed for ${email}, trying Firestore…`);
    }

    try {
      // Fallback: find Firestore doc by email
      const snap = await admin
        .firestore()
        .collection('users')
        .where('email', '==', email)
        .limit(1)
        .get();
      if (!snap.empty) {
        const doc = snap.docs[0];
        const firebaseUid = doc.id;
        console.log(`✔  ${email} -> ${firebaseUid} (from Firestore)`);
        if (!dryRun) {
          await mysqlService.query(`UPDATE users SET firebase_uid = ? WHERE id = ?`, [
            firebaseUid,
            u.id,
          ]);
        }
        updated += 1;
        continue;
      }
      console.warn(`No Firebase UID found for ${email}`);
    } catch (e) {
      console.error(`Error querying Firestore for ${email}`, e);
    }
  }

  console.log(`✅ Backfill complete. Updated ${updated}/${users.length} users.`);
  await mysqlService.close();
}

main().catch(err => {
  console.error('Backfill failed', err);
  process.exit(1);
});
