/*
  Seeds default RBAC templates (system roles) that are clinic-agnostic.
  Safe to run multiple times (idempotent based on role name + clinic_id NULL).
*/
import { mysqlService } from '@/lib/database/mysql-service';
import * as dotenv from 'dotenv';
import { FeatureKey, PermissionLevel } from '@/models/auth';
import { randomUUID } from 'crypto';

type Template = {
  name: string;
  description: string;
  grants: Array<{ feature: FeatureKey; level: PermissionLevel }>;
};

const TEMPLATES: Template[] = [
  {
    name: 'Account Owner',
    description: 'All features including billing',
    grants: Object.values(FeatureKey).map(f => ({
      feature: f as FeatureKey,
      level: PermissionLevel.ADMIN,
    })),
  },
  {
    name: 'Practice Manager',
    description: 'All features except billing',
    grants: Object.values(FeatureKey)
      .filter(f => f !== FeatureKey.BILLING)
      .map(f => ({ feature: f as FeatureKey, level: PermissionLevel.ADMIN })),
  },
  {
    name: 'Doctor',
    description: 'View call logs, dashboard, and no-show (location-scoped)',
    grants: [
      { feature: FeatureKey.CALL_LOGS, level: PermissionLevel.READ },
      { feature: FeatureKey.DASHBOARD, level: PermissionLevel.READ },
      { feature: FeatureKey.NO_SHOW, level: PermissionLevel.READ },
    ],
  },
  {
    name: 'Staff',
    description: 'View call logs, dashboard, and no-show (location-scoped)',
    grants: [
      { feature: FeatureKey.CALL_LOGS, level: PermissionLevel.READ },
      { feature: FeatureKey.DASHBOARD, level: PermissionLevel.READ },
      { feature: FeatureKey.NO_SHOW, level: PermissionLevel.READ },
    ],
  },
  {
    name: 'ReadOnly',
    description: 'Read-only access across enabled features',
    grants: Object.values(FeatureKey).map(f => ({
      feature: f as FeatureKey,
      level: PermissionLevel.READ,
    })),
  },
];

async function upsertTemplate(template: Template): Promise<void> {
  // Find existing global template by name
  const existing = await mysqlService.query<{ id: string }>(
    `SELECT * FROM roles WHERE clinic_id IS NULL AND name = ? LIMIT 1`,
    [template.name],
  );

  let roleId: string;
  if (existing.length === 0) {
    roleId = randomUUID();
    await mysqlService.query(
      `INSERT INTO roles (id, clinic_id, name, description, is_system, is_template, created_by)
       VALUES (?, NULL, ?, ?, TRUE, TRUE, 'system')`,
      [roleId, template.name, template.description],
    );
  } else {
    roleId = existing[0].id as string;
    await mysqlService.query(
      `UPDATE roles SET description = ?, is_system = TRUE, is_template = TRUE WHERE id = ?`,
      [template.description, roleId],
    );
  }

  // Reset role permissions for this template
  await mysqlService.query(`DELETE FROM role_permissions WHERE role_id = ?`, [roleId]);
  if (template.grants.length > 0) {
    const valuesSql = template.grants.map(() => '(?, ?, ?)').join(',');
    const params: unknown[] = [];
    template.grants.forEach(g => params.push(roleId, g.feature, g.level));
    await mysqlService.query(
      `INSERT INTO role_permissions (role_id, feature, level) VALUES ${valuesSql}`,
      params,
    );
  }
}

async function main() {
  // Load environment variables (default to dev env if present)
  dotenv.config({ path: '.env' });

  // Initialize DB connection
  await mysqlService.initialize();
  console.log('Seeding RBAC templates...');
  for (const t of TEMPLATES) {
    // eslint-disable-next-line no-await-in-loop
    await upsertTemplate(t);
    console.log(`✅ Seeded template: ${t.name}`);
  }
  console.log('🎉 RBAC templates seeded successfully');
  await mysqlService.close();
}

main().catch(err => {
  console.error('Failed to seed RBAC templates', err);
  process.exit(1);
});
