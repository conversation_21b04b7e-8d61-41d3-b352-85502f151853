## Tools Endpoints (Direct HTTP calls only)

Only functions that make explicit HTTP requests (with concrete endpoint URLs) are listed below.

### manage_patients_tool.py

- get_patients(patient_id?: str, date_of_birth?: str, phone_number?: str, full_name?: str, tool_context?: ToolContext) -> dict
  - GET `/api/external-api/v2/patients`
  - Query: id, dateOfBirth (YYYY-MM-DD), phoneNumber, fullName
  - 200: patient_not_found | patient_found | patient_confirmation_required; Errors include status_code and details

### manage_appointments_tool.py

- get_appointment_availability(tool_context: ToolContext, start_date: str, end_date: str) -> dict
  - GET `/api/external-api/v2/appointment-availability`
  - Query: appointmentTypeId (11|75 via state), startDate, endDate (YYYY-MM-DD)
  - 200: found-timeslots | no-available-timeslots; failure on errors

- get_patient_appointments(patient_id: str, is_for_rescheduling: bool) -> dict
  - GET `/api/external-api/v2/appointments/for-rescheduling`
  - Query: patientId, isForRescheduling (true|false)
  - 200: { success: true, data, message }; Errors with status_code

- book_new_appointment(call_id: str, appointment_start_time: str, is_appointment_notification_enabled: bool, patient_id?: str, patient?: dict) -> dict
  - POST `/api/external-api/v3/appointments/booking`
  - Body: { dryRun: false, appointment: { startTime }, callId, isAppointmentNotificationEnabled, patient }
  - 200: { status: success }; failure or error otherwise

- book_appointment(patient: dict, appointment: dict, call: dict) -> dict
  - POST `/api/external-api/v2/finalize-conversation`
  - Body: { patient, appointment, call }
  - 200: { success: true, data, message }; Errors with status_code

- cancel_appointment(appointment_id: str, session_id: str) -> dict
  - DELETE `/api/external-api/v2/appointments/{appointment_id}`
  - Query: sessionId
  - 204: { success: true, message }; Errors with status_code

- reschedule_appointment(appointment_id: str, start_time: str, end_time: str, tool_context: ToolContext) -> dict
  - POST `/api/external-api/v2/appointments/{appointment_id}/change`
  - Body: { startTime, endTime, sessionId }
  - 200: { success: true, data, message }; Errors with status_code

### sms_confirmation_tool.py

- sms_confirmation(phone_number: str, appointment_id?: str, is_for_cancellation: bool=False, tool_context?: ToolContext) -> dict
  - POST `/api/external-api/v2/appointments/send-confirmation`
  - Body: { appointmentId, phoneNumber, isForCancellation }
  - 200: { success: true, message, appointment_id, message_sid? }; Errors with details

### session_management_tool.py

- end_session(session_id: str, reason: str="completed", outcome: str="successful", summary: str="Session completed successfully", notes?: str, tool_context?: ToolContext) -> dict
  - POST `/api/external-api/v2/calls/end-conversation`
  - Body: { sessionId, outcome, summary, reason, notes, endTime }
  - Success/Errors include webhook_response or error

- end_session_with_transfer(session_id: str, transfer_details: dict, summary?: str, tool_context?: ToolContext) -> dict
  - POST `/api/external-api/v2/calls/end-conversation`
  - Body: { sessionId, outcome: transferred, summary, reason: transferred, transferDetails, notes, endTime }

### enhanced_patient_tools.py

- get_patients_with_session(patient_id?: str, date_of_birth?: str, phone_number?: str, full_name?: str) -> dict
  - GET `/api/external-api/v2/patients`
  - Query: id, dateOfBirth (YYYY-MM-DD), phoneNumber, fullName
  - 200: { success: true, data, message, search_criteria }; Errors with status_code

