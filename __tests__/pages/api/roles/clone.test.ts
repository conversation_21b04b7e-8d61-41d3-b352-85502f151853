import handler from '@/pages/api/roles/clone';
import httpMocks from 'node-mocks-http';

jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(async () => ({ id: 'user-1', clinicId: 1 })),
}));

jest.mock('@/lib/services/permission-service', () => ({
  PermissionService: jest.fn().mockImplementation(() => ({
    resolveEffectiveLevel: jest.fn(async () => 'ADMIN'),
  })),
}));

jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    query: jest.fn(async (sql: string, params: unknown[]) => {
      if (sql.includes('SELECT * FROM roles WHERE id = ?')) {
        return [
          {
            id: params[0],
            clinic_id: 1,
            name: 'Source Role',
            description: null,
            is_system: 0,
            is_template: 0,
            created_by: 'user-1',
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];
      }
      return [];
    }),
  },
}));

describe('POST /api/roles/clone', () => {
  const OLD_ENV = process.env;
  beforeAll(() => {
    process.env = { ...OLD_ENV, ENABLE_NEW_RBAC: 'true' };
  });
  afterAll(() => {
    process.env = OLD_ENV;
  });

  it('clones a role', async () => {
    const req = httpMocks.createRequest({
      method: 'POST',
      body: { sourceRoleId: 'r1', name: 'Cloned Role' },
    });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(201);
  });
});
