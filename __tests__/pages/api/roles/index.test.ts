import handler from '@/pages/api/roles/index';
import httpMocks from 'node-mocks-http';

jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(async () => ({
    id: 'user-1',
    clinicId: 1,
  })),
}));

jest.mock('@/lib/services/permission-service', () => ({
  PermissionService: jest.fn().mockImplementation(() => ({
    resolveEffectiveLevel: jest.fn(async () => 'ADMIN'),
  })),
}));

jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    query: jest.fn(async (sql: string, params: unknown[]) => {
      if (sql.includes('SELECT * FROM roles WHERE id = ?')) {
        return [
          {
            id: params[0],
            clinic_id: 1,
            name: 'New Role',
            description: null,
            is_system: 0,
            is_template: 0,
            created_by: 'user-1',
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];
      }
      if (sql.includes('SELECT * FROM roles WHERE clinic_id')) {
        return [
          {
            id: 'r1',
            clinic_id: 1,
            name: 'Role 1',
            description: null,
            is_system: 0,
            is_template: 0,
            created_by: 'user-1',
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];
      }
      return [];
    }),
  },
}));

describe('/api/roles index', () => {
  const OLD_ENV = process.env;
  beforeAll(() => {
    process.env = { ...OLD_ENV, ENABLE_NEW_RBAC: 'true' };
  });
  afterAll(() => {
    process.env = OLD_ENV;
  });

  it('GET returns roles list', async () => {
    const req = httpMocks.createRequest({ method: 'GET', query: {} });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(200);
    const data = res._getJSONData();
    expect(Array.isArray(data)).toBe(true);
    expect(data[0].name).toBe('Role 1');
  });

  it('POST creates a role', async () => {
    const req = httpMocks.createRequest({
      method: 'POST',
      body: {
        name: 'New Role',
        description: 'desc',
        permissions: [
          { feature: 'CALL_LOGS', level: 'READ' },
          { feature: 'DASHBOARD', level: 'READ' },
        ],
      },
    });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(201);
    const data = res._getJSONData();
    expect(data.name).toBe('New Role');
  });
});
