import handler from '@/pages/api/roles/[id]';
import httpMocks from 'node-mocks-http';

jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(async () => ({ id: 'user-1', clinicId: 1 })),
}));

jest.mock('@/lib/services/permission-service', () => ({
  PermissionService: jest.fn().mockImplementation(() => ({
    resolveEffectiveLevel: jest.fn(async () => 'ADMIN'),
  })),
}));

let lastUpdateName = 'Role 1';

jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    query: jest.fn(async (sql: string, params: unknown[]) => {
      if (sql.includes('SELECT * FROM roles WHERE id = ?')) {
        return [
          {
            id: params[0],
            clinic_id: 1,
            name: lastUpdateName,
            description: null,
            is_system: 0,
            is_template: 0,
            created_by: 'user-1',
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];
      }
      if (sql.startsWith('UPDATE roles')) {
        lastUpdateName = 'Updated Role';
        return [];
      }
      return [];
    }),
  },
}));

describe('/api/roles/[id]', () => {
  const OLD_ENV = process.env;
  beforeAll(() => {
    process.env = { ...OLD_ENV, ENABLE_NEW_RBAC: 'true' };
  });
  afterAll(() => {
    process.env = OLD_ENV;
  });

  it('GET returns a role', async () => {
    const req = httpMocks.createRequest({ method: 'GET', query: { id: 'r1' } });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData().id).toBe('r1');
  });

  it('PUT updates a role', async () => {
    const req = httpMocks.createRequest({
      method: 'PUT',
      query: { id: 'r1' },
      body: { name: 'Updated Role', permissions: [{ feature: 'CALL_LOGS', level: 'READ' }] },
    });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData().name).toBe('Updated Role');
  });

  it('DELETE removes a role', async () => {
    const req = httpMocks.createRequest({ method: 'DELETE', query: { id: 'r1' } });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(204);
  });
});
