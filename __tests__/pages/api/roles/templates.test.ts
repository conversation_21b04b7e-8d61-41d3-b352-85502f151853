import handler from '@/pages/api/roles/templates';
import httpMocks from 'node-mocks-http';

jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    query: jest.fn(async () => [
      { id: 't1', name: 'Account Owner', grants: 'BILLING:ADMIN' },
      { id: 't2', name: 'Practice Manager', grants: 'ANSWERING_SERVICE:ADMIN' },
    ]),
  },
}));

describe('GET /api/roles/templates', () => {
  const OLD_ENV = process.env;
  beforeAll(() => {
    process.env = { ...OLD_ENV, ENABLE_NEW_RBAC: 'true' };
  });
  afterAll(() => {
    process.env = OLD_ENV;
  });

  it('returns templates', async () => {
    const req = httpMocks.createRequest({ method: 'GET' });
    const res = httpMocks.createResponse();

    await handler(req as any, res as any);

    expect(res.statusCode).toBe(200);
    const data = res._getJSONData();
    expect(Array.isArray(data)).toBe(true);
    expect(data[0].name).toBe('Account Owner');
  });
});
