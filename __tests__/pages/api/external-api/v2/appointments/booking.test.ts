import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
// Import handler after mocks are set up to ensure mocks are applied

// Mocks
const mockAddOrUpdateCallSession = jest.fn();

jest.mock('@/utils/firestore', () => ({
  callSessionsService: {
    addOrUpdateCallSession: mockAddOrUpdateCallSession,
  },
}));

// Mock provider services used by booking handler
jest.mock('@/lib/external-api/v2/providers', () => {
  const createdPatient = {
    id: 'patient-1',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: '1990-01-01',
    phoneNumber: '+15551234567',
    providerInfo: { provider: 'nextech', externalId: 'ext-patient-1' },
  };

  const existingPatient = {
    id: 'patient-2',
    firstName: 'Jane',
    lastName: 'Roe',
    dateOfBirth: '1980-02-02',
    phoneNumber: '+15557654321',
    providerInfo: { provider: 'nextech', externalId: 'ext-patient-2' },
  };

  const mockPatientService = {
    getPatientById: jest.fn(),
  };

  const mockAppointmentService = {
    createAppointment: jest.fn().mockResolvedValue({
      id: 'appt-1',
      patientId: 'ext-patient-1',
      locationId: 'location-1',
      providerInfo: { provider: 'nextech', externalId: 'ext-appt-1' },
      startTime: '2025-12-31T10:00:00',
      patientName: 'John Doe',
      locationName: 'Location',
      practitionerName: 'Smith',
    }),
  };

  const mockLocationService = {
    getLocationById: jest.fn().mockResolvedValue({
      id: 'location-1',
      address: {
        line1: '123 Main St',
        city: 'City',
        state: 'ST',
        postalCode: '12345',
        country: 'US',
      },
      phoneNumber: '************',
    }),
  };

  const mockProvider = {
    name: 'nextech',
    getPatientService: jest.fn().mockReturnValue(mockPatientService),
    getAppointmentService: jest.fn().mockReturnValue(mockAppointmentService),
    getLocationService: jest.fn().mockReturnValue(mockLocationService),
  };

  return {
    providerRegistry: {
      getDefaultProvider: jest.fn().mockReturnValue(mockProvider),
      getProvider: jest.fn().mockReturnValue(mockProvider),
      getAvailableProviders: jest.fn(() => ['nextech']),
    },
  };
});

// Mock supporting utils used by booking (patient/utils, appointment/utils, events, factories)
jest.mock('@/lib/external-api/v2/utils/patient-utils', () => ({
  createPatientWithReference: jest.fn().mockResolvedValue({
    id: 'patient-1',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: '1990-01-01',
    phoneNumber: '+15551234567',
    providerInfo: { provider: 'nextech', externalId: 'ext-patient-1' },
  }),
  findPatientByCriteria: jest.fn().mockResolvedValue(null),
}));

jest.mock('@/lib/external-api/v2/utils/appointment-utils', () => ({
  storeAppointmentReference: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('@/lib/external-api/v2/utils/events', () => ({
  eventEmitter: { emit: jest.fn() },
  EVENTS: { APPOINTMENT: { CREATED: 'APPOINTMENT.CREATED' } },
}));

jest.mock('@/lib/factories/patient-factory', () => ({
  patientFactory: {
    getPatientCoordinatorService: () => ({
      storePatient: jest.fn().mockResolvedValue(undefined),
    }),
  },
}));

// Mock SMS service to avoid side effects
jest.mock('@/lib/services/sms-service', () => ({
  smsService: {
    sendNewPatientFormLink: jest.fn().mockResolvedValue('sid-new'),
    sendAppointmentConfirmation: jest.fn().mockResolvedValue('sid-conf'),
  },
}));
const { smsService } = require('@/lib/services/sms-service');

// Mock env api key
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';

describe('Booking Handler - sendNewPatientForm flag', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const handler = require('@/pages/api/external-api/v3/appointments/booking').default as (
    req: NextApiRequest,
    res: NextApiResponse,
  ) => Promise<void>;

  it('sets sendNewPatientForm: true when a new patient is created', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: {
        dryRun: false,
        isAppointmentNotificationEnabled: true,
        callId: 'session-123',
        appointment: { startTime: '2099-12-31T12:00:00' },
        patient: {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1990-01-01',
          phoneNumber: '+15551234567',
          address: { line1: '1', city: 'C', state: 'S', postalCode: 'P', country: 'US' },
        },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockAddOrUpdateCallSession).toHaveBeenCalledWith(
      'session-123',
      expect.objectContaining({
        sendNewPatientForm: true,
      }),
    );
    expect(smsService.sendNewPatientFormLink).toHaveBeenCalled();
    expect(smsService.sendAppointmentConfirmation).not.toHaveBeenCalled();
  });

  it('sets sendNewPatientForm: false when using an existing patient', async () => {
    // Make findPatientByCriteria return an existing patient
    const patientUtils = require('@/lib/external-api/v2/utils/patient-utils');
    patientUtils.findPatientByCriteria.mockResolvedValueOnce({
      id: 'patient-2',
      firstName: 'Jane',
      lastName: 'Roe',
      dateOfBirth: '1980-02-02',
      phoneNumber: '+15557654321',
      providerInfo: { provider: 'nextech', externalId: 'ext-patient-2' },
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: {
        dryRun: false,
        isAppointmentNotificationEnabled: true,
        callId: 'session-999',
        appointment: { startTime: '2099-12-31T12:00:00' },
        patient: {
          firstName: 'Jane',
          lastName: 'Roe',
          dateOfBirth: '1980-02-02',
          phoneNumber: '+15557654321',
          address: { line1: '1', city: 'C', state: 'S', postalCode: 'P', country: 'US' },
        },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockAddOrUpdateCallSession).toHaveBeenCalledWith(
      'session-999',
      expect.objectContaining({
        sendNewPatientForm: false,
      }),
    );
    expect(smsService.sendAppointmentConfirmation).toHaveBeenCalled();
    expect(smsService.sendNewPatientFormLink).not.toHaveBeenCalled();
  });
});
