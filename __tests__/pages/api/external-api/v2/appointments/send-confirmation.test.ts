import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../../pages/api/external-api/v2/appointments/send-confirmation';
import { smsService } from '@/lib/services/sms-service';

// Mock Firebase
jest.mock('../../../../../../utils/firestore', () => ({
  callSessionsService: {
    findCallSessionsByAppointmentId: jest.fn().mockResolvedValue([]),
    addOrUpdateCallSession: jest.fn().mockResolvedValue({}),
  },
}));

// Mock the database initialization
jest.mock('@/lib/middleware/db-init', () => ({
  ensureDbInitialized: jest.fn(),
}));

// Mock the locations repository
jest.mock('@/lib/repositories/locations-repository', () => ({
  LocationsRepository: jest.fn().mockImplementation(() => ({
    findById: jest.fn().mockResolvedValue({ name: 'Test Location', phone: '************' }),
  })),
}));

// Mock the logger
jest.mock('@/lib/external-api/v2/utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock the SMS service
jest.mock('../../../../../../lib/services/sms-service', () => ({
  smsService: {
    sendSms: jest.fn().mockResolvedValue('test-message-sid'),
    sendAppointmentConfirmation: jest.fn().mockResolvedValue('test-message-sid'),
    sendAppointmentCancellationConfirmation: jest.fn().mockResolvedValue('test-message-sid'),
    sendNewPatientFormLink: jest.fn().mockResolvedValue('test-message-sid-new'),
  },
}));

// Mock the provider registry and appointment service
jest.mock('../../../../../../lib/external-api/v2/providers', () => {
  const mockAppointmentService = {
    getAppointmentById: jest.fn().mockImplementation(id => {
      if (id === 'valid-appointment-id') {
        return Promise.resolve({
          id: 'valid-appointment-id',
          patientId: 'patient-123',
          providerId: 'provider-456',
          startTime: '2023-06-15T10:30:00Z',
          endTime: '2023-06-15T11:00:00Z',
          status: 'scheduled',
          patientName: 'Patient',
          locationName: 'Location',
          locationId: 'location-789',
          practitionerName: undefined,
        });
      }
      return Promise.resolve(null);
    }),
  };

  const mockLocationService = {
    getLocationById: jest.fn().mockImplementation(id => {
      if (id === 'location-789') {
        return Promise.resolve({
          id: 'location-789',
          name: 'Location',
          address: {
            line1: '123 Main St',
            city: 'Anytown',
            state: 'ST',
            postalCode: '12345',
          },
          phoneNumber: '************',
        });
      }
      return Promise.resolve(null);
    }),
  };

  const mockProvider = {
    getAppointmentService: jest.fn().mockReturnValue(mockAppointmentService),
    getLocationService: jest.fn().mockReturnValue(mockLocationService),
  };

  return {
    providerRegistry: {
      getDefaultProvider: jest.fn().mockReturnValue(mockProvider),
      getProvider: jest.fn().mockReturnValue(mockProvider),
      getAvailableProviders: jest.fn(() => ['nextech']),
    },
  };
});

// Mock Firestore callSessionsService to control new patient determination
jest.mock('../../../../../../utils/firestore', () => ({
  callSessionsService: {
    findCallSessionsByAppointmentId: jest.fn().mockResolvedValue([]),
  },
}));

// Mock the external-api/v2 module
jest.mock('../../../../../../lib/external-api/v2', () => {
  const original = jest.requireActual('../../../../../../lib/external-api/v2');
  return {
    ...original,
    createApiHandler: jest.fn().mockImplementation((handler, options = {}) => {
      return async (req: NextApiRequest, res: NextApiResponse) => {
        try {
          // Apply middlewares manually if specified
          if (options.middleware) {
            for (const middleware of options.middleware) {
              await new Promise<void>((resolve, reject) => {
                try {
                  // Check if this is validateApiKey middleware
                  if (
                    middleware.name === 'validateApiKey' ||
                    middleware.toString().includes('validateApiKey')
                  ) {
                    const apiKey = req.headers['x-api-key'];
                    if (!apiKey) {
                      reject(new original.UnauthorizedError('API key is required'));
                      return;
                    }
                    if (apiKey !== 'test-api-key') {
                      reject(new original.UnauthorizedError('Invalid API key'));
                      return;
                    }
                  }
                  middleware(req, res, () => resolve());
                } catch (error) {
                  reject(error);
                }
              });
            }
          }

          // Call the handler
          return await handler(req, res);
        } catch (error: any) {
          // Simulate error handling in the actual implementation
          if (
            error instanceof original.UnauthorizedError ||
            (error.message && error.message.toLowerCase().includes('api key'))
          ) {
            res.status(401).json({
              status: 401,
              code: 'UNAUTHORIZED',
              message: error.message,
            });
          } else if (
            error instanceof original.BadRequestError ||
            (error.message &&
              (error.message.toLowerCase().includes('invalid') ||
                error.message.toLowerCase().includes('missing')))
          ) {
            res.status(400).json({
              status: 400,
              code: 'BAD_REQUEST',
              message: error.message,
              details: error.details,
            });
          } else if (
            error instanceof original.NotFoundError ||
            (error.message && error.message.toLowerCase().includes('not found'))
          ) {
            res.status(404).json({
              status: 404,
              code: 'NOT_FOUND',
              message: error.message,
            });
          } else {
            res.status(500).json({
              status: 500,
              code: 'INTERNAL_SERVER_ERROR',
              message: error.message,
            });
          }
        }
      };
    }),
    validateApiKey: jest.fn().mockImplementation((req, res, next) => {
      const apiKey = req.headers['x-api-key'];
      if (!apiKey) {
        throw new original.UnauthorizedError('API key is required');
      }
      if (apiKey !== 'test-api-key') {
        throw new original.UnauthorizedError('Invalid API key');
      }
      return next();
    }),
    getProviderFromRequest: jest.fn(),
    parseISODateString: jest.fn().mockReturnValue({ date: 'June 15, 2023', time: '10:30 AM' }),
  };
});

// Mock environment variables
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';

describe('Send Confirmation API Endpoint', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock getProviderFromRequest to return the mock provider
    const { providerRegistry } = require('../../../../../../lib/external-api/v2/providers');
    const mockProvider = providerRegistry.getProvider();
    const { getProviderFromRequest } = require('../../../../../../lib/external-api/v2');
    (getProviderFromRequest as jest.Mock).mockReturnValue(mockProvider);
  });

  it('should return 405 for non-POST requests', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method GET Not Allowed',
    });
  });

  it('should return 401 for requests without API key', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
  });

  it('should return 400 for invalid request body', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        // Missing required fields
        appointmentId: 'valid-appointment-id',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid request body');
  });

  it('should return 404 if appointment is not found', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'non-existent-id',
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData()).message).toContain(
      'Appointment with ID non-existent-id not found',
    );
  });

  it('should send SMS confirmation successfully with default template', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
      message: 'Confirmation SMS sent successfully',
      appointmentId: 'valid-appointment-id',
      messageSid: 'test-message-sid',
    });

    expect(smsService.sendAppointmentConfirmation).toHaveBeenCalledWith(
      '+15551234567',
      expect.objectContaining({
        patientName: 'Patient',
        date: 'June 15, 2023',
        time: '10:30 AM',
        location: 'Test Location',
        provider: undefined,
        locationAddress: '123 Main St',
        contactPhone: '************',
      }),
    );
  });

  it('should send custom SMS message if provided', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
        customMessage: 'This is a custom confirmation message.',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(smsService.sendSms).toHaveBeenCalledWith(
      '+15551234567',
      'This is a custom confirmation message.',
    );
  });

  it('should send new patient form link when call session indicates new patient', async () => {
    const firestore = require('../../../../../../utils/firestore') as {
      callSessionsService: {
        findCallSessionsByAppointmentId: jest.Mock<Promise<unknown[]>, [string]>;
      };
    };
    firestore.callSessionsService.findCallSessionsByAppointmentId.mockResolvedValueOnce([
      { id: 'session-1', sendNewPatientForm: true },
    ]);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({
        success: true,
        message: 'Confirmation SMS sent successfully',
        appointmentId: 'valid-appointment-id',
        messageSid: 'test-message-sid-new',
      }),
    );

    expect(smsService.sendNewPatientFormLink).toHaveBeenCalledWith('+15551234567', 'Patient');
    expect(smsService.sendAppointmentConfirmation).not.toHaveBeenCalled();
    expect(smsService.sendAppointmentCancellationConfirmation).not.toHaveBeenCalled();
  });

  it('should send cancellation confirmation when isForCancellation is true', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
        isForCancellation: true,
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(smsService.sendAppointmentCancellationConfirmation).toHaveBeenCalled();
    expect(smsService.sendNewPatientFormLink).not.toHaveBeenCalled();
  });
});
