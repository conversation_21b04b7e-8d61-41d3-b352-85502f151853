import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';

const getCompletePatientById = jest.fn();
const getCompletePatientByPhoneNumber = jest.fn();

jest.mock('@/lib/factories/patient-factory', () => ({
  patientFactory: {
    getPatientCoordinatorService: () => ({
      getCompletePatientById,
      getCompletePatientByPhoneNumber,
    }),
  },
}));

jest.mock('@/lib/services/office-hours', () => ({
  OfficeHoursService: {
    checkOfficeHours: () => ({ isOpen: true, currentStatus: 'open' }),
  },
  OfficeHoursStatus: class {},
}));

jest.mock('@/lib/services/agent-location-mapping', () => ({
  AgentLocationMappingService: {
    getLocationByAgentId: jest.fn().mockResolvedValue({
      id: 'loc-1',
      name: 'Loc',
      officeHours: { '1': { start: '09:00', end: '17:00' } },
      timeZone: 'America/Chicago',
    }),
  },
}));

jest.mock('@/lib/external-api/v2/services/call-service', () => ({
  callService: {
    createEmptyCall: jest.fn().mockResolvedValue({ id: 'call-1' }),
  },
}));

jest.mock('@/pages/api/external-api/v2/jobs/call-classification-update', () => ({
  scheduleCallClassificationUpdate: jest.fn().mockResolvedValue({ jobName: 'job-1' }),
}));

// Mock env api key
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';

describe('addOrUpdateCallSessionHandler - sendNewPatientForm flag', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getCompletePatientById.mockReset();
    getCompletePatientByPhoneNumber.mockReset();
  });

  function buildReqRes(body: Record<string, unknown>) {
    return createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body,
    });
  }

  const handler = require('@/pages/api/external-api/v2/calls/add-or-update-call-session')
    .default as (req: NextApiRequest, res: NextApiResponse) => Promise<void>;
  const firestore = require('@/utils/firestore') as {
    callSessionsService: { addOrUpdateCallSession: jest.Mock };
  };

  it('sets sendNewPatientForm: true when no patient is found by phone', async () => {
    getCompletePatientByPhoneNumber.mockResolvedValueOnce([]);

    const { req, res } = buildReqRes({
      sessionInfo: {
        session: 'projects/front/locations/global/agents/agent-1/sessions/session-1',
        parameters: { patientPhoneNumber: '+15551234567' },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const calls = (firestore.callSessionsService.addOrUpdateCallSession as jest.Mock).mock
      .calls as Array<[string, Record<string, unknown>]>;
    expect(
      calls.some(([sid, data]) => sid === 'session-1' && (data as any).sendNewPatientForm === true),
    ).toBe(true);
  });

  it('sets sendNewPatientForm: false when a single existing patient is found', async () => {
    getCompletePatientByPhoneNumber.mockResolvedValueOnce([
      { providerInfo: { externalId: 'p-1' }, firstName: 'A', lastName: 'B' },
    ]);

    const { req, res } = buildReqRes({
      sessionInfo: {
        session: 'projects/front/locations/global/agents/agent-1/sessions/session-2',
        parameters: { patientPhoneNumber: '+15559876543' },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const calls2 = (firestore.callSessionsService.addOrUpdateCallSession as jest.Mock).mock
      .calls as Array<[string, Record<string, unknown>]>;
    expect(
      calls2.some(
        ([sid, data]) =>
          sid === 'session-2' &&
          (data as any).sendNewPatientForm === false &&
          (data as any).patientId === 'p-1',
      ),
    ).toBe(true);
  });

  it('sets sendNewPatientForm: false when multiple patients are found (family number)', async () => {
    getCompletePatientByPhoneNumber.mockResolvedValueOnce([
      { providerInfo: { externalId: 'p-1' } },
      { providerInfo: { externalId: 'p-2' } },
    ]);

    const { req, res } = buildReqRes({
      sessionInfo: {
        session: 'projects/front/locations/global/agents/agent-1/sessions/session-3',
        parameters: { patientPhoneNumber: '+15550000000' },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const calls3 = (firestore.callSessionsService.addOrUpdateCallSession as jest.Mock).mock
      .calls as Array<[string, Record<string, unknown>]>;
    expect(
      calls3.some(
        ([sid, data]) => sid === 'session-3' && (data as any).sendNewPatientForm === false,
      ),
    ).toBe(true);
  });

  it('sets sendNewPatientForm: false when patientId is provided and found', async () => {
    getCompletePatientById.mockResolvedValueOnce({
      providerInfo: { externalId: 'p-9' },
      firstName: 'F',
      lastName: 'G',
    });

    const { req, res } = buildReqRes({
      sessionInfo: {
        session: 'projects/front/locations/global/agents/agent-1/sessions/session-4',
        parameters: { patientId: 'p-9', patientPhoneNumber: '+15553334444' },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const calls4 = (firestore.callSessionsService.addOrUpdateCallSession as jest.Mock).mock
      .calls as Array<[string, Record<string, unknown>]>;
    expect(
      calls4.some(
        ([sid, data]) => sid === 'session-4' && (data as any).sendNewPatientForm === false,
      ),
    ).toBe(true);
  });
});

import { createMocks as createMocks2 } from 'node-mocks-http';
import { NextApiRequest as NextApiRequest2, NextApiResponse as NextApiResponse2 } from 'next';
import handlerForEndpointTests from '@/pages/api/external-api/v2/calls/add-or-update-call-session';
import { callSessionsService } from '@/utils/firestore';
import dayjs from 'dayjs';

// Import the handler for the second test suite
import handlerModule from '@/pages/api/external-api/v2/calls/add-or-update-call-session';

// Mock Firebase Admin to prevent initialization errors
jest.mock('@/utils/firebase-admin', () => {
  const mockFirestore = {
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
    set: jest.fn(),
    update: jest.fn(),
  };

  return {
    firestore: jest.fn(() => mockFirestore),
    apps: [],
    initializeApp: jest.fn(),
    default: {
      firestore: jest.fn(() => mockFirestore),
      apps: [],
      initializeApp: jest.fn(),
    },
  };
});

// Mock the external API modules
jest.mock('@/lib/external-api/v2', () => ({
  createApiHandler: jest.fn().mockImplementation((handler, options = {}) => {
    const middleware = options.middleware || [];

    return async (req: any, res: any) => {
      try {
        // Apply middleware first
        for (const middlewareFn of middleware) {
          await middlewareFn(req, res, () => Promise.resolve());
        }

        // Then call the handler
        await handler(req, res);
      } catch (error) {
        // Handle errors from middleware or handler
        if (error instanceof Error) {
          if (error.name === 'UnauthorizedError') {
            res.status(401).json({ message: error.message });
          } else if (error.name === 'BadRequestError') {
            res.status(400).json({ message: error.message });
          } else {
            res.status(500).json({ message: error.message });
          }
        } else {
          res.status(500).json({ message: 'Internal server error' });
        }
      }
    };
  }),
  validateApiKey: jest.fn().mockImplementation((req, res, next) => {
    const apiKey = req.headers['x-api-key'];

    if (!apiKey) {
      const error = new Error('API key is required');
      error.name = 'UnauthorizedError';
      throw error;
    }

    if (apiKey !== 'test-api-key') {
      const error = new Error('Invalid API key');
      error.name = 'UnauthorizedError';
      throw error;
    }

    return next();
  }),
  BadRequestError: class BadRequestError extends Error {
    constructor(message: string, options?: any) {
      super(message);
      this.name = 'BadRequestError';
    }
  },
}));

// Mock the callSessionsService
jest.mock('@/utils/firestore', () => ({
  callSessionsService: {
    addOrUpdateCallSession: jest.fn().mockImplementation((sessionId, data) => {
      return Promise.resolve({
        id: 'test-uuid',
        sessionId,
        ...data,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      });
    }),
    getCallSessionBySessionId: jest.fn().mockImplementation(sessionId => {
      return Promise.resolve({
        id: 'test-uuid',
        sessionId,
        hasVoiceMail: true,
        callType: 1,
        callerPhone: '************',
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      });
    }),
    findCallSessionsByPhone: jest.fn().mockResolvedValue([]),
  },
  callsService: {
    getCallsBySessionId: jest.fn().mockResolvedValue([]),
  },
}));

// Mock the call service
jest.mock('@/lib/external-api/v2/services/call-service', () => ({
  callService: {
    createEmptyCall: jest.fn().mockResolvedValue({
      id: 'call-123',
      sessionId: 'test-session-id',
    }),
  },
}));

// Mock the patient factory
// Use the top-level patient-factory mock for consistency across suites

// Mock the location service
jest.mock('@/lib/services/locationService', () => ({
  LocationService: {
    getLocationById: jest.fn(),
  },
}));

// Mock app config
jest.mock('@/app-config', () => ({
  URMA_CLINIC_ID: 12,
  URMA_LOMBARD_LOCATION_ID: '118',
}));

// Mock dayjs
jest.mock('dayjs', () => {
  const mockDayjs = jest.fn((date?: any) => {
    if (date) {
      return {
        tz: jest.fn().mockReturnThis(),
        day: jest.fn().mockReturnValue(1), // Monday
        format: jest.fn().mockReturnValue('09:30'), // 9:30 AM
      };
    }
    return {
      tz: jest.fn().mockReturnThis(),
      day: jest.fn().mockReturnValue(1), // Monday
      format: jest.fn().mockReturnValue('09:30'), // 9:30 AM
    };
  }) as any;

  (mockDayjs as any).extend = jest.fn();
  return mockDayjs;
});

jest.mock('dayjs/plugin/utc', () => ({}));
jest.mock('dayjs/plugin/timezone', () => ({}));

// Mock the providerRegistry
jest.mock('../../../../../../lib/external-api/v2/providers', () => ({
  providerRegistry: {
    getProvider: jest.fn().mockReturnValue({
      getPatientService: jest.fn().mockReturnValue({
        getPatientByPhone: jest.fn().mockResolvedValue(null),
      }),
    }),
  },
}));

// Mock the agent-location mapping service
jest.mock('../../../../../../lib/services/agent-location-mapping', () => ({
  AgentLocationMappingService: {
    getLocationByAgentId: jest.fn().mockResolvedValue({
      id: 'test-location-id',
      name: 'Test Location',
      timeZone: 'America/Chicago',
      officeHours: {
        '1': { start: '09:00', end: '17:00' },
        '2': { start: '09:00', end: '17:00' },
        '3': { start: '09:00', end: '17:00' },
        '4': { start: '09:00', end: '17:00' },
        '5': { start: '09:00', end: '17:00' },
        '6': null,
        '7': null,
      },
    }),
  },
}));

// Mock the office hours service
jest.mock('../../../../../../lib/services/office-hours', () => ({
  OfficeHoursService: {
    checkOfficeHours: jest.fn().mockReturnValue({
      isOpen: true,
      currentStatus: 'open',
      nextOpenTime: undefined,
      nextCloseTime: '2023-01-01T17:00:00',
      todayHours: { start: '09:00', end: '17:00' },
      timezone: 'America/Chicago',
    }),
  },
}));

// Mock the on-call schedule service
jest.mock('../../../../../../lib/services/on-call-schedule', () => ({
  OnCallScheduleService: {
    getCurrentOnCallDoctor: jest.fn().mockResolvedValue({
      id: 'test-schedule-id',
      doctorId: 'test-doctor-id',
      doctorName: 'Dr. Test',
      doctorPhone: '+15551234567',
      clinicId: 1,
      timezone: 'America/Chicago',
      startTime: new Date('2023-01-01T00:00:00Z'),
      endTime: new Date('2023-01-02T00:00:00Z'),
      isActive: true,
      createdAt: new Date('2023-01-01T00:00:00Z'),
      updatedAt: new Date('2023-01-01T00:00:00Z'),
    }),
  },
}));

// Mock the on-call notification service
jest.mock('../../../../../../lib/services/on-call-notification', () => ({
  OnCallNotificationService: {
    notifyOnCallDoctor: jest.fn().mockResolvedValue('mock-message-sid'),
  },
}));

// Mock the call service
jest.mock('../../../../../../lib/external-api/v2/services/call-service', () => ({
  callService: {
    createCall: jest.fn().mockResolvedValue({
      id: 'test-call-id',
      sessionId: 'test-session-id',
    }),
    createEmptyCall: jest.fn().mockResolvedValue({
      id: 'test-call-id',
      sessionId: 'test-session-id',
    }),
  },
}));

// Mock environment variables
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';

// Mock logger to prevent console output during tests
jest.mock('../../../../../../lib/external-api/v2/utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  generateRequestId: jest.fn().mockReturnValue('test-request-id'),
}));

describe('Add or Update Call Session API Endpoint (v2)', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return 405 for non-POST requests', async () => {
    const { req, res } = createMocks2<NextApiRequest2, NextApiResponse2>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
      },
      env: {},
    });

    await handlerForEndpointTests(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method GET Not Allowed',
    });
  });

  it('should return 401 for requests without API key', async () => {
    const { req, res } = createMocks2<NextApiRequest2, NextApiResponse2>({
      method: 'POST',
      env: {},
    });

    await handlerForEndpointTests(req, res);

    expect(res._getStatusCode()).toBe(401);
  });

  it('should return 400 for invalid request body', async () => {
    const { req, res } = createMocks2<NextApiRequest2, NextApiResponse2>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        // Missing sessionInfo
        hasVoiceMail: true,
        callType: 1,
      },
      env: {},
    });

    await handlerForEndpointTests(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid request body');
  });

  it('should add or update a call session successfully', async () => {
    const { req, res } = createMocks2<NextApiRequest2, NextApiResponse2>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        sessionInfo: {
          session:
            'projects/frontdesk-454309/locations/global/agents/b8b3adbf-34cf-4b45-8f35-78da708d0f96/sessions/test-session-id',
          parameters: {
            'telephony-caller-id': '************',
          },
        },
        hasVoiceMail: true,
        callType: 1,
        intentInfo: {
          displayName: 'Test Intent',
          confidence: 0.9,
        },
      },
      env: {},
    });

    await handlerForEndpointTests(req, res);

    expect(res._getStatusCode()).toBe(200);

    const responseData = JSON.parse(res._getData());

    // Check that response follows the DialogFlow CX webhook response format
    expect(responseData).toHaveProperty('sessionInfo');
    expect(responseData).toHaveProperty('payload');

    // Check session info parameters
    expect(responseData.sessionInfo.parameters).toEqual({
      isAfterHours: false,
      patientId: null,
      callerPhone: '************',
      sessionId: 'test-session-id',
      isRedirected: false,
      locationId: 'test-location-id',
      isOfficeHours: true,
      officeStatus: 'open',
      nextOpenTime: undefined,
      todayHours: { start: '09:00', end: '17:00' },
      isFamilyNumber: false,
    });

    // Check payload
    expect(responseData.payload).toEqual({
      sessionId: 'test-session-id',
      location: {
        id: 'test-location-id',
        name: 'Test Location',
        timezone: 'America/Chicago',
        officeHours: {
          '1': { start: '09:00', end: '17:00' },
          '2': { start: '09:00', end: '17:00' },
          '3': { start: '09:00', end: '17:00' },
          '4': { start: '09:00', end: '17:00' },
          '5': { start: '09:00', end: '17:00' },
          '6': null,
          '7': null,
        },
      },
      schedule: {
        isOpen: true,
        currentStatus: 'open',
        nextOpenTime: undefined,
        nextCloseTime: '2023-01-01T17:00:00',
        todayHours: { start: '09:00', end: '17:00' },
        timezone: 'America/Chicago',
      },
      patient: null,
    });

    expect(callSessionsService.addOrUpdateCallSession).toHaveBeenCalledWith(
      'test-session-id',
      expect.objectContaining({
        hasVoiceMail: true,
        callType: 1,
        callerPhone: '************',
      }),
    );
  });
});

describe('add-or-update-call-session API', () => {
  let mockReq: Partial<NextApiRequest>;
  let mockRes: Partial<NextApiResponse>;
  let mockJson: jest.Mock;
  let mockStatus: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });

    mockReq = {
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        sessionInfo: {
          session:
            'projects/frontdesk-454309/locations/global/agents/0d35ec76-19cc-41c5-8ef3-693c9236c52d/sessions/3c5371dc-d5c0-4530-8363-d1ddf833c346',
          parameters: {
            'telephony-caller-id': '+15551234567',
          },
        },
      },
    };

    mockRes = {
      status: mockStatus,
      json: mockJson,
    };
  });

  describe('isAfterHours function', () => {
    it('should return true when location is not found', async () => {
      // Mock AgentLocationMappingService.getLocationByAgentId to return null
      const {
        AgentLocationMappingService,
      } = require('../../../../../../lib/services/agent-location-mapping');
      (AgentLocationMappingService.getLocationByAgentId as jest.Mock).mockResolvedValue(null);

      await handlerModule(mockReq as NextApiRequest, mockRes as NextApiResponse);

      // Check that isAfterHours was called and returned true
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          sessionInfo: expect.objectContaining({
            parameters: expect.objectContaining({
              isAfterHours: true,
            }),
          }),
        }),
      );
    });

    it('should return true when no office hours are configured', async () => {
      // Mock AgentLocationMappingService.getLocationByAgentId to return location with no office hours
      const {
        AgentLocationMappingService,
      } = require('../../../../../../lib/services/agent-location-mapping');
      (AgentLocationMappingService.getLocationByAgentId as jest.Mock).mockResolvedValue({
        id: '118',
        clinicId: 12,
        practiceId: 'practice-1',
        name: 'Lombard Location',
        address: '123 Main St',
        timeZone: 'America/Chicago',
        isActive: true,
        practiceName: 'Test Practice',
        officeHours: null, // No office hours
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await handlerModule(mockReq as NextApiRequest, mockRes as NextApiResponse);

      // Check that isAfterHours was called and returned true
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          sessionInfo: expect.objectContaining({
            parameters: expect.objectContaining({
              isAfterHours: true,
            }),
          }),
        }),
      );
    });

    it('should return false when current time is during office hours', async () => {
      // Mock AgentLocationMappingService.getLocationByAgentId to return location with office hours
      const {
        AgentLocationMappingService,
      } = require('../../../../../../lib/services/agent-location-mapping');
      (AgentLocationMappingService.getLocationByAgentId as jest.Mock).mockResolvedValue({
        id: '118',
        clinicId: 12,
        practiceId: 'practice-1',
        name: 'Lombard Location',
        address: '123 Main St',
        timeZone: 'America/Chicago',
        isActive: true,
        practiceName: 'Test Practice',
        officeHours: {
          '1': { start: '09:00', end: '17:00' }, // Monday 9 AM - 5 PM
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Mock dayjs to return 9:30 AM (during office hours)
      (dayjs as unknown as jest.Mock).mockImplementation(() => ({
        tz: jest.fn().mockReturnThis(),
        day: jest.fn().mockReturnValue(1), // Monday
        format: jest.fn().mockReturnValue('09:30'), // 9:30 AM
      }));

      await handlerModule(mockReq as NextApiRequest, mockRes as NextApiResponse);

      // Check that isAfterHours was called and returned false
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          sessionInfo: expect.objectContaining({
            parameters: expect.objectContaining({
              isAfterHours: false,
            }),
          }),
        }),
      );
    });

    it('should return true when current time is after office hours', async () => {
      // Mock AgentLocationMappingService.getLocationByAgentId to return location with office hours
      const {
        AgentLocationMappingService,
      } = require('../../../../../../lib/services/agent-location-mapping');
      (AgentLocationMappingService.getLocationByAgentId as jest.Mock).mockResolvedValue({
        id: '118',
        clinicId: 12,
        practiceId: 'practice-1',
        name: 'Lombard Location',
        address: '123 Main St',
        timeZone: 'America/Chicago',
        isActive: true,
        practiceName: 'Test Practice',
        officeHours: {
          '1': { start: '09:00', end: '17:00' }, // Monday 9 AM - 5 PM
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Mock dayjs to return 6:00 PM (after office hours)
      (dayjs as unknown as jest.Mock).mockImplementation(() => ({
        tz: jest.fn().mockReturnThis(),
        day: jest.fn().mockReturnValue(1), // Monday
        format: jest.fn().mockReturnValue('18:00'), // 6:00 PM
      }));

      await handlerModule(mockReq as NextApiRequest, mockRes as NextApiResponse);

      // Check that isAfterHours was called and returned true
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          sessionInfo: expect.objectContaining({
            parameters: expect.objectContaining({
              isAfterHours: true,
            }),
          }),
        }),
      );
    });
  });
});
