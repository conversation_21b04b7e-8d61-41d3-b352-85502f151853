import handler from '@/pages/api/users/[id]/permissions';
import httpMocks from 'node-mocks-http';

jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(async () => ({ id: 'admin-1', clinicId: 1 })),
}));

jest.mock('@/lib/services/permission-service', () => ({
  PermissionService: jest.fn().mockImplementation(() => ({
    resolveEffectiveLevel: jest.fn(async () => 'ADMIN'),
    invalidateUser: jest.fn(),
  })),
}));

jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    query: jest.fn(async () => []),
  },
}));

describe('/api/users/[id]/permissions', () => {
  const OLD_ENV = process.env;
  beforeAll(() => {
    process.env = { ...OLD_ENV, ENABLE_NEW_RBAC: 'true' };
  });
  afterAll(() => {
    process.env = OLD_ENV;
  });

  it('GET returns effective matrix', async () => {
    const req = httpMocks.createRequest({ method: 'GET', query: { id: 'u1' } });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(200);
    const data = res._getJSONData();
    expect(data.userId).toBe('u1');
    expect(Array.isArray(data.effective)).toBe(true);
  });

  it('PUT upserts overrides', async () => {
    const req = httpMocks.createRequest({
      method: 'PUT',
      query: { id: 'u1' },
      body: {
        overrides: [
          { feature: 'CALL_LOGS', level: 'READ' },
          { feature: 'DASHBOARD', level: 'WRITE', expiresAt: null },
        ],
      },
    });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(204);
  });
});
