import handler from '@/pages/api/permissions/check';
import httpMocks from 'node-mocks-http';

jest.mock('@/lib/services/permission-service', () => ({
  PermissionService: jest.fn().mockImplementation(() => ({
    resolveEffectiveLevel: jest.fn(async () => 'READ'),
  })),
}));

describe('POST /api/permissions/check', () => {
  const OLD_ENV = process.env;
  beforeAll(() => {
    process.env = { ...OLD_ENV, ENABLE_NEW_RBAC: 'true' };
  });
  afterAll(() => {
    process.env = OLD_ENV;
  });

  it('returns level', async () => {
    const req = httpMocks.createRequest({
      method: 'POST',
      body: { userId: 'u1', feature: 'DASHBOARD' },
    });
    const res = httpMocks.createResponse();
    await handler(req as any, res as any);
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData().level).toBe('READ');
  });
});
