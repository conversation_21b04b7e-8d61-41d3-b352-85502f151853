// Mock utils/firestore to prevent real Firebase usage
jest.mock('@/utils/firestore', () => ({
  locationsService: {
    getLocationById: jest.fn(),
  },
}));

// Mock repositories to prevent MySQL initialization
jest.mock('@/lib/repositories', () => ({
  getRepositories: jest.fn(() => ({
    initialize: jest.fn(),
    onCallSchedules: {
      findMany: jest.fn(),
    },
  })),
}));

import { OnCallScheduleService } from '@/lib/services/on-call-schedule';
import {
  OnCallScheduleCreateRequest,
  OnCallScheduleUpdateRequest,
  ScheduleValidationResult,
} from '@/models/OnCallSchedule';
import { LocationService } from '@/lib/services/locationService';

// Mock Firebase Admin
const mockGet = jest.fn();
const mockUpdate = jest.fn();
const mockAdd = jest.fn();
const mockQueryGet = jest.fn();
const mockWhere = jest.fn();
const mockOrderBy = jest.fn();
const mockDoc = jest.fn();
const mockCollection = jest.fn();

// Setup the mock chain properly
mockWhere.mockReturnValue({
  where: mockWhere,
  orderBy: mockOrderBy,
  get: mockQueryGet,
});

mockOrderBy.mockReturnValue({
  where: mockWhere,
  orderBy: mockOrderBy,
  get: mockQueryGet,
});

mockDoc.mockReturnValue({
  get: mockGet,
  update: mockUpdate,
});

mockCollection.mockReturnValue({
  add: mockAdd,
  doc: mockDoc,
  where: mockWhere,
  orderBy: mockOrderBy,
  get: mockQueryGet,
});

jest.mock('firebase-admin', () => ({
  firestore: jest.fn(),
}));

// Mock LocationService
jest.mock('@/lib/services/locationService', () => ({
  LocationService: {
    getLocationById: jest.fn(),
  },
}));

// Mock dayjs
jest.mock('dayjs', () => {
  const actualDayjs = jest.requireActual('dayjs');
  const mockDayjs = jest.fn((date?: unknown) => {
    const instance = actualDayjs(date);
    instance.tz = jest.fn(() => instance);
    instance.format = jest.fn((format?: string) => {
      if (format === 'YYYY-MM-DD') return '2024-01-15';
      if (format === 'HH:mm') return '10:00';
      if (format === 'YYYY-MM-DD HH:mm:ss') return '2024-01-15 10:00:00';
      return instance;
    });
    return instance;
  });

  // Add extend method to the mock
  (mockDayjs as unknown as { extend: jest.Mock }).extend = jest.fn();

  return mockDayjs;
});

describe('OnCallScheduleService', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the static db getter using Object.defineProperty
    Object.defineProperty(OnCallScheduleService, 'db', {
      get: jest.fn().mockReturnValue({
        collection: mockCollection,
      }),
      configurable: true,
    });
  });

  describe('validateScheduleData', () => {
    it('should validate correct schedule data', () => {
      const validScheduleData: OnCallScheduleCreateRequest = {
        doctorId: 'doctor-123',
        locationId: 'location-123',
        date: '2024-01-15',
        startTime: '09:00',
        endTime: '17:00',
        notes: 'Test schedule',
      };

      const result: ScheduleValidationResult =
        OnCallScheduleService.validateScheduleData(validScheduleData);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject missing required fields', () => {
      const invalidScheduleData = {
        doctorId: '',
        locationId: '',
        date: '',
        startTime: '',
        endTime: '',
      };

      const result = OnCallScheduleService.validateScheduleData(invalidScheduleData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Doctor ID is required');
      expect(result.errors).toContain('Location ID is required');
      expect(result.errors).toContain('Date is required');
      expect(result.errors).toContain('Start time is required');
      expect(result.errors).toContain('End time is required');
    });

    it('should reject invalid date format', () => {
      const invalidScheduleData: Partial<OnCallScheduleCreateRequest> = {
        doctorId: 'doctor-123',
        locationId: 'location-123',
        date: '15/01/2024', // Invalid format
        startTime: '09:00',
        endTime: '17:00',
      };

      const result = OnCallScheduleService.validateScheduleData(invalidScheduleData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Date must be in YYYY-MM-DD format');
    });

    it('should reject invalid time format', () => {
      const invalidScheduleData: Partial<OnCallScheduleCreateRequest> = {
        doctorId: 'doctor-123',
        locationId: 'location-123',
        date: '2024-01-15',
        startTime: 'invalid', // Invalid format
        endTime: '25:00', // Invalid hour
      };

      const result = OnCallScheduleService.validateScheduleData(invalidScheduleData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Start time must be in HH:MM format');
      expect(result.errors).toContain('End time must be in HH:MM format');
    });

    it('should reject start time after end time', () => {
      const invalidScheduleData: Partial<OnCallScheduleCreateRequest> = {
        doctorId: 'doctor-123',
        locationId: 'location-123',
        date: '2024-01-15',
        startTime: '18:00',
        endTime: '09:00', // End before start
      };

      const result = OnCallScheduleService.validateScheduleData(invalidScheduleData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Start time must be before end time');
    });

    it('should reject schedule too short', () => {
      const invalidScheduleData: Partial<OnCallScheduleCreateRequest> = {
        doctorId: 'doctor-123',
        locationId: 'location-123',
        date: '2024-01-15',
        startTime: '09:00',
        endTime: '09:15', // Only 15 minutes
      };

      const result = OnCallScheduleService.validateScheduleData(invalidScheduleData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Schedule must be at least 30 minutes long');
    });
  });

  describe('createSchedule', () => {
    const validScheduleData: OnCallScheduleCreateRequest = {
      doctorId: 'doctor-123',
      locationId: 'location-123',
      date: '2024-01-15',
      startTime: '09:00',
      endTime: '17:00',
      notes: 'Test schedule',
    };

    beforeEach(() => {
      // Mock location lookup - called directly in service
      mockGet.mockResolvedValue({
        exists: true,
        id: 'location-123',
        data: () => ({
          clinicId: 1,
          timeZone: 'America/Chicago',
          name: 'Test Location',
        }),
      });

      // Mock LocationService.getLocationById
      (LocationService.getLocationById as jest.Mock).mockResolvedValue({
        id: 'location-123',
        clinicId: 1,
        timeZone: 'America/Chicago',
        name: 'Test Location',
      });

      // Mock conflict check (no conflicts)
      mockQueryGet.mockResolvedValue({
        docs: [],
      });

      // Mock document creation
      mockAdd.mockResolvedValue({
        id: 'schedule-123',
      });
    });

    it('should create a valid schedule successfully', async () => {
      // Mock doctor lookup for the first call (location check)
      mockGet
        .mockResolvedValueOnce({
          exists: true,
          id: 'location-123',
          data: () => ({
            clinicId: 1,
            timeZone: 'America/Chicago',
            name: 'Test Location',
          }),
        })
        .mockResolvedValueOnce({
          exists: true,
          id: 'doctor-123',
          data: () => ({
            name: 'Dr. Test',
            phone: '1234567890',
            canTakeAppointments: true,
          }),
        });

      const result = await OnCallScheduleService.createSchedule(validScheduleData, 'admin-123');

      expect(result).toMatchObject({
        id: 'schedule-123',
        doctorId: 'doctor-123',
        locationId: 'location-123',
        date: '2024-01-15',
        startTime: '09:00',
        endTime: '17:00',
        doctorName: 'Dr. Test',
        doctorPhone: '+11234567890',
        clinicId: 1,
        timezone: 'America/Chicago',
        isActive: true,
        createdBy: 'admin-123',
      });

      expect(mockAdd).toHaveBeenCalledWith(
        expect.objectContaining({
          doctorId: 'doctor-123',
          locationId: 'location-123',
          date: '2024-01-15',
          startTime: '09:00',
          endTime: '17:00',
          isActive: true,
          clinicId: 1,
          timezone: 'America/Chicago',
          createdBy: 'admin-123',
        }),
      );
    });

    it('should throw error for invalid schedule data', async () => {
      const invalidData = { ...validScheduleData, startTime: 'invalid' };

      await expect(OnCallScheduleService.createSchedule(invalidData, 'admin-123')).rejects.toThrow(
        'Validation failed',
      );
    });

    it('should throw error if location not found', async () => {
      mockGet.mockResolvedValue({ exists: false });

      await expect(
        OnCallScheduleService.createSchedule(validScheduleData, 'admin-123'),
      ).rejects.toThrow('Location location-123 not found');
    });

    it('should throw error if doctor not found', async () => {
      // Mock location lookup first, then doctor lookup
      mockGet
        .mockResolvedValueOnce({
          exists: true,
          id: 'location-123',
          data: () => ({
            clinicId: 1,
            timeZone: 'America/Chicago',
            name: 'Test Location',
          }),
        })
        .mockResolvedValueOnce({ exists: false });

      await expect(
        OnCallScheduleService.createSchedule(validScheduleData, 'admin-123'),
      ).rejects.toThrow('Doctor doctor-123 not found');
    });

    it('should throw error if doctor cannot take appointments', async () => {
      mockGet
        .mockResolvedValueOnce({
          exists: true,
          id: 'location-123',
          data: () => ({
            clinicId: 1,
            timeZone: 'America/Chicago',
            name: 'Test Location',
          }),
        })
        .mockResolvedValueOnce({
          exists: true,
          data: () => ({
            name: 'Dr. Test',
            phone: '1234567890',
            canTakeAppointments: false, // Cannot take appointments
          }),
        });

      await expect(
        OnCallScheduleService.createSchedule(validScheduleData, 'admin-123'),
      ).rejects.toThrow('Doctor doctor-123 cannot take appointments');
    });

    it('should throw error if doctor has no phone number', async () => {
      mockGet
        .mockResolvedValueOnce({
          exists: true,
          id: 'location-123',
          data: () => ({
            clinicId: 1,
            timeZone: 'America/Chicago',
            name: 'Test Location',
          }),
        })
        .mockResolvedValueOnce({
          exists: true,
          data: () => ({
            name: 'Dr. Test',
            phone: '', // No phone number
            canTakeAppointments: true,
          }),
        });

      await expect(
        OnCallScheduleService.createSchedule(validScheduleData, 'admin-123'),
      ).rejects.toThrow('Doctor doctor-123 has no phone number for notifications');
    });

    it('should throw error if schedule conflicts exist', async () => {
      // Mock location and doctor lookups
      mockGet
        .mockResolvedValueOnce({
          exists: true,
          id: 'location-123',
          data: () => ({
            clinicId: 1,
            timeZone: 'America/Chicago',
            name: 'Test Location',
          }),
        })
        .mockResolvedValueOnce({
          exists: true,
          data: () => ({
            name: 'Dr. Test',
            phone: '1234567890',
            canTakeAppointments: true,
          }),
        });

      // Mock conflict detection
      mockQueryGet.mockResolvedValue({
        docs: [
          {
            id: 'conflict-schedule',
            data: () => ({
              doctorName: 'Dr. Conflict',
              startTime: '10:00',
              endTime: '12:00',
            }),
          },
        ],
      });

      await expect(
        OnCallScheduleService.createSchedule(validScheduleData, 'admin-123'),
      ).rejects.toThrow('Schedule conflicts detected');
    });
  });

  describe('updateSchedule', () => {
    const updateData: OnCallScheduleUpdateRequest = {
      startTime: '10:00',
      endTime: '18:00',
      notes: 'Updated notes',
    };

    beforeEach(() => {
      // Mock existing schedule
      mockGet.mockResolvedValue({
        exists: true,
        id: 'schedule-123',
        data: () => ({
          doctorId: 'doctor-123',
          locationId: 'location-123',
          date: '2024-01-15',
          startTime: '09:00',
          endTime: '17:00',
          doctorName: 'Dr. Test',
          doctorPhone: '+11234567890',
          clinicId: 1,
          timezone: 'America/Chicago',
          isActive: true,
        }),
      });

      // Mock conflict check (no conflicts)
      mockQueryGet.mockResolvedValue({
        docs: [],
      });
    });

    it('should update schedule successfully', async () => {
      // Mock the get call after update
      mockGet
        .mockResolvedValueOnce({
          exists: true,
          id: 'schedule-123',
          data: () => ({
            doctorId: 'doctor-123',
            locationId: 'location-123',
            date: '2024-01-15',
            startTime: '09:00',
            endTime: '17:00',
            doctorName: 'Dr. Test',
            doctorPhone: '+11234567890',
            clinicId: 1,
            timezone: 'America/Chicago',
            isActive: true,
          }),
        })
        .mockResolvedValueOnce({
          exists: true,
          id: 'schedule-123',
          data: () => ({
            doctorId: 'doctor-123',
            locationId: 'location-123',
            date: '2024-01-15',
            startTime: '10:00',
            endTime: '18:00',
            doctorName: 'Dr. Test',
            doctorPhone: '+11234567890',
            clinicId: 1,
            timezone: 'America/Chicago',
            isActive: true,
            notes: 'Updated notes',
          }),
        });

      const result = await OnCallScheduleService.updateSchedule(
        'schedule-123',
        updateData,
        'admin-123',
      );

      expect(mockUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          startTime: '10:00',
          endTime: '18:00',
          notes: 'Updated notes',
          updatedAt: expect.any(Date),
        }),
      );

      expect(result).toMatchObject({
        id: 'schedule-123',
        startTime: '10:00',
        endTime: '18:00',
        notes: 'Updated notes',
      });
    });

    it('should throw error if schedule not found', async () => {
      mockGet.mockResolvedValue({ exists: false });

      await expect(
        OnCallScheduleService.updateSchedule('schedule-123', updateData, 'admin-123'),
      ).rejects.toThrow('Schedule schedule-123 not found');
    });

    it('should validate time changes and check conflicts', async () => {
      // This test ensures the validation logic is called for time changes
      const timeUpdateData = {
        startTime: '25:00', // Invalid time
        endTime: '18:00',
      };

      await expect(
        OnCallScheduleService.updateSchedule('schedule-123', timeUpdateData, 'admin-123'),
      ).rejects.toThrow('Validation failed');
    });
  });

  describe('deleteSchedule', () => {
    it('should soft delete schedule successfully', async () => {
      mockGet.mockResolvedValue({ exists: true });

      await OnCallScheduleService.deleteSchedule('schedule-123', 'admin-123');

      expect(mockUpdate).toHaveBeenCalledWith({
        isActive: false,
        updatedAt: expect.any(Date),
      });
    });

    it('should throw error if schedule not found', async () => {
      mockGet.mockResolvedValue({ exists: false });

      await expect(
        OnCallScheduleService.deleteSchedule('schedule-123', 'admin-123'),
      ).rejects.toThrow('Schedule schedule-123 not found');
    });
  });

  describe('getSchedulesByLocation', () => {
    it('should return schedules for location', async () => {
      const mockSchedules = [
        {
          id: 'schedule-1',
          data: () => ({
            doctorId: 'doctor-1',
            doctorName: 'Dr. One',
            date: '2024-01-15',
            startTime: '09:00',
            endTime: '17:00',
          }),
        },
        {
          id: 'schedule-2',
          data: () => ({
            doctorId: 'doctor-2',
            doctorName: 'Dr. Two',
            date: '2024-01-16',
            startTime: '10:00',
            endTime: '18:00',
          }),
        },
      ];

      mockQueryGet.mockResolvedValue({ docs: mockSchedules });

      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-20');
      const result = await OnCallScheduleService.getSchedulesByLocation(
        'location-123',
        startDate,
        endDate,
        1,
      );

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: 'schedule-1',
        doctorName: 'Dr. One',
        date: '2024-01-15',
      });

      expect(mockWhere).toHaveBeenCalledWith('locationId', '==', 'location-123');
      expect(mockWhere).toHaveBeenCalledWith('clinicId', '==', 1);
      expect(mockWhere).toHaveBeenCalledWith('isActive', '==', true);
    });
  });

  describe('getCurrentOnCallDoctor', () => {
    beforeEach(() => {
      // Mock location lookup using the mocked locationsService
      const { locationsService } = require('@/utils/firestore');
      locationsService.getLocationById.mockResolvedValue({
        id: 'location-123',
        clinicId: 1,
        timeZone: 'America/Chicago',
        name: 'Test Location',
      });

      // Mock repositories
      const { getRepositories } = require('@/lib/repositories');
      getRepositories.mockReturnValue({
        initialize: jest.fn(),
        onCallSchedules: {
          findMany: jest.fn().mockResolvedValue({
            items: [
              {
                id: 'current-schedule',
                doctorId: 'doctor-123',
                doctorName: 'Dr. Current',
                startTime: '08:00',
                endTime: '20:00',
                date: '2024-01-15',
              },
            ],
          }),
        },
      });
    });

    it('should return current on-call doctor', async () => {
      const result = await OnCallScheduleService.getCurrentOnCallDoctor('location-123');

      expect(result).toMatchObject({
        id: 'current-schedule',
        doctorName: 'Dr. Current',
        startTime: '08:00',
        endTime: '20:00',
      });
    });

    it('should return null if no doctor is on-call', async () => {
      const { getRepositories } = require('@/lib/repositories');
      getRepositories.mockReturnValue({
        initialize: jest.fn(),
        onCallSchedules: {
          findMany: jest.fn().mockResolvedValue({
            items: [],
          }),
        },
      });

      const result = await OnCallScheduleService.getCurrentOnCallDoctor('location-123');

      expect(result).toBeNull();
    });

    it('should throw error if location not found', async () => {
      const { locationsService } = require('@/utils/firestore');
      locationsService.getLocationById.mockResolvedValue(null);

      await expect(OnCallScheduleService.getCurrentOnCallDoctor('location-123')).rejects.toThrow(
        'Location location-123 not found',
      );
    });
  });

  describe('checkScheduleConflicts', () => {
    it('should detect overlapping schedules', async () => {
      const conflictingSchedule = {
        id: 'conflict-schedule',
        data: () => ({
          doctorId: 'doctor-123',
          doctorName: 'Dr. Conflict',
          startTime: '10:00',
          endTime: '14:00',
        }),
      };

      mockQueryGet.mockResolvedValue({ docs: [conflictingSchedule] });

      const conflicts = await OnCallScheduleService.checkScheduleConflicts(
        'location-123',
        '2024-01-15',
        '12:00',
        '16:00',
      );

      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].conflictingSchedule.doctorName).toBe('Dr. Conflict');
      expect(conflicts[0].conflictType).toBe('partial_overlap');
    });

    it('should return no conflicts for non-overlapping schedules', async () => {
      const nonConflictingSchedule = {
        id: 'non-conflict-schedule',
        data: () => ({
          doctorId: 'doctor-123',
          doctorName: 'Dr. NoConflict',
          startTime: '06:00',
          endTime: '08:00',
        }),
      };

      mockQueryGet.mockResolvedValue({ docs: [nonConflictingSchedule] });

      const conflicts = await OnCallScheduleService.checkScheduleConflicts(
        'location-123',
        '2024-01-15',
        '10:00',
        '12:00',
      );

      expect(conflicts).toHaveLength(0);
    });

    it('should exclude specific schedule from conflict check', async () => {
      const existingSchedule = {
        id: 'exclude-me',
        data: () => ({
          doctorId: 'doctor-123',
          doctorName: 'Dr. Exclude',
          startTime: '10:00',
          endTime: '14:00',
        }),
      };

      mockQueryGet.mockResolvedValue({ docs: [existingSchedule] });

      const conflicts = await OnCallScheduleService.checkScheduleConflicts(
        'location-123',
        '2024-01-15',
        '12:00',
        '16:00',
        'exclude-me', // Exclude this schedule
      );

      expect(conflicts).toHaveLength(0);
    });
  });

  describe('getSchedules', () => {
    it('should apply filters correctly', async () => {
      const mockSchedules = [
        {
          id: 'schedule-1',
          data: () => ({
            doctorId: 'doctor-1',
            locationId: 'location-1',
            clinicId: 1,
          }),
        },
      ];

      mockQueryGet.mockResolvedValue({ docs: mockSchedules });

      const filters = {
        locationId: 'location-1',
        doctorId: 'doctor-1',
        clinicId: 1,
        isActive: true,
      };

      const result = await OnCallScheduleService.getSchedules(filters);

      expect(result).toHaveLength(1);
      expect(mockWhere).toHaveBeenCalledWith('locationId', '==', 'location-1');
      expect(mockWhere).toHaveBeenCalledWith('doctorId', '==', 'doctor-1');
      expect(mockWhere).toHaveBeenCalledWith('clinicId', '==', 1);
      expect(mockWhere).toHaveBeenCalledWith('isActive', '==', true);
    });
  });

  describe('getScheduleById', () => {
    it('should return schedule by ID', async () => {
      mockGet.mockResolvedValue({
        exists: true,
        id: 'schedule-123',
        data: () => ({
          doctorId: 'doctor-123',
          doctorName: 'Dr. Test',
          locationId: 'location-123',
          date: '2024-01-15',
          startTime: '09:00',
          endTime: '17:00',
        }),
      });

      const result = await OnCallScheduleService.getScheduleById('schedule-123');

      expect(result).toMatchObject({
        id: 'schedule-123',
        doctorName: 'Dr. Test',
        locationId: 'location-123',
      });
    });

    it('should return null if schedule not found', async () => {
      mockGet.mockResolvedValue({ exists: false });

      const result = await OnCallScheduleService.getScheduleById('schedule-123');

      expect(result).toBeNull();
    });
  });
});
