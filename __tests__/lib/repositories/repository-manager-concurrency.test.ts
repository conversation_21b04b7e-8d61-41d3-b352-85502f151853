/**
 * Test to verify RepositoryManager handles concurrent initialization correctly
 */
import { RepositoryManager } from '@/lib/repositories';
import { mysqlService } from '@/lib/database/mysql-service';

// Mock the MySQL service to avoid actual database connections
jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    initialize: jest.fn(),
  },
}));

describe('RepositoryManager Concurrency', () => {
  let originalConsoleLog: typeof console.log;
  let originalConsoleError: typeof console.error;

  beforeEach(() => {
    // Reset the singleton instance before each test
    (RepositoryManager as unknown as { _instance: RepositoryManager | undefined })._instance =
      undefined;

    // Mock console methods to capture logs
    originalConsoleLog = console.log;
    originalConsoleError = console.error;
    console.log = jest.fn();
    console.error = jest.fn();

    // Reset the mock
    (mysqlService.initialize as jest.Mock).mockClear();
  });

  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  });

  it('should handle concurrent initialization calls without race conditions', async () => {
    // Mock MySQL service initialization with a delay to simulate real-world scenario
    let initializationCount = 0;
    (mysqlService.initialize as jest.Mock).mockImplementation(async () => {
      initializationCount++;
      // Add a small delay to simulate real initialization time
      await new Promise(resolve => setTimeout(resolve, 50));
      return Promise.resolve();
    });

    const repositoryManager = RepositoryManager.getInstance();

    // Start multiple concurrent initialization calls
    const promises = [
      repositoryManager.initialize(),
      repositoryManager.initialize(),
      repositoryManager.initialize(),
      repositoryManager.initialize(),
      repositoryManager.initialize(),
    ];

    // Wait for all promises to complete
    await Promise.all(promises);

    // MySQL service should only be initialized once despite multiple concurrent calls
    expect(mysqlService.initialize).toHaveBeenCalledTimes(1);
    expect(initializationCount).toBe(1);

    // Verify the initialization success log was called
    expect(console.log).toHaveBeenCalledWith('🔄 Initializing RepositoryManager...');
    expect(console.log).toHaveBeenCalledWith('✅ RepositoryManager initialized successfully');
  });

  it('should allow retry after failed initialization', async () => {
    let attemptCount = 0;
    (mysqlService.initialize as jest.Mock).mockImplementation(async () => {
      attemptCount++;
      if (attemptCount === 1) {
        throw new Error('First attempt failed');
      }
      return Promise.resolve();
    });

    const repositoryManager = RepositoryManager.getInstance();

    // First initialization should fail
    await expect(repositoryManager.initialize()).rejects.toThrow('First attempt failed');

    // Second initialization should succeed
    await expect(repositoryManager.initialize()).resolves.not.toThrow();

    // MySQL service should have been called twice (once failed, once succeeded)
    expect(mysqlService.initialize).toHaveBeenCalledTimes(2);
    expect(attemptCount).toBe(2);
  });

  it('should handle concurrent calls when first one fails', async () => {
    let attemptCount = 0;
    (mysqlService.initialize as jest.Mock).mockImplementation(async () => {
      attemptCount++;
      // Add delay to simulate real initialization
      await new Promise(resolve => setTimeout(resolve, 50));
      throw new Error('Initialization failed');
    });

    const repositoryManager = RepositoryManager.getInstance();

    // Start multiple concurrent initialization calls that will all fail
    const promises = [
      repositoryManager.initialize().catch(e => e),
      repositoryManager.initialize().catch(e => e),
      repositoryManager.initialize().catch(e => e),
    ];

    const results = await Promise.all(promises);

    // All should have failed with the same error
    results.forEach(result => {
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Initialization failed');
    });

    // MySQL service should only be called once despite multiple concurrent calls
    expect(mysqlService.initialize).toHaveBeenCalledTimes(1);
    expect(attemptCount).toBe(1);
  });

  it('should handle subsequent calls after successful initialization', async () => {
    (mysqlService.initialize as jest.Mock).mockResolvedValue(undefined);

    const repositoryManager = RepositoryManager.getInstance();

    // First initialization
    await repositoryManager.initialize();

    // Subsequent calls should return immediately without calling MySQL service again
    await repositoryManager.initialize();
    await repositoryManager.initialize();

    // MySQL service should only be called once
    expect(mysqlService.initialize).toHaveBeenCalledTimes(1);
  });
});
