import { ClinicsRepository } from '../../../lib/repositories/clinics-repository';
import { Clinic } from '../../../models/Clinic';

describe('ClinicsRepository', () => {
  let repository: ClinicsRepository;

  beforeEach(() => {
    repository = new ClinicsRepository();
  });

  describe('constructor', () => {
    it('should initialize with correct table and collection names', () => {
      expect(repository['tableName']).toBe('clinics');
      expect(repository['collectionName']).toBe('clinics');
    });

    it('should have correct field mappings', () => {
      const mappings = repository['getSchemaMapping']();
      expect(mappings.id).toBe('uuid');
      expect(mappings.practiceId).toBe('practice_id');
      expect(mappings.name).toBe('name');
      expect(mappings.description).toBe('description');
      expect(mappings.paymentLink).toBe('payment_link');
      expect(mappings.createdAt).toBe('created_at');
      expect(mappings.updatedAt).toBe('updated_at');
    });
  });

  describe('validateEntity', () => {
    it('should validate a valid clinic entity', () => {
      const clinic: Partial<Clinic> = {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'Test Clinic',
        description: 'A test clinic',
        paymentLink: 'https://example.com/pay',
      };

      expect(() => repository.validateEntity(clinic)).not.toThrow();
    });

    it('should throw error for missing ID', () => {
      const clinic: Partial<Clinic> = {
        name: 'Test Clinic',
      };

      expect(() => repository.validateEntity(clinic)).toThrow('Clinic ID is required');
    });

    it('should throw error for missing name', () => {
      const clinic: Partial<Clinic> = {
        id: '123e4567-e89b-12d3-a456-************',
      };

      expect(() => repository.validateEntity(clinic)).toThrow('Clinic name is required');
    });

    it('should throw error for invalid payment link', () => {
      const clinic: Partial<Clinic> = {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'Test Clinic',
        paymentLink: 'invalid-url',
      };

      expect(() => repository.validateEntity(clinic)).toThrow('Payment link must be a valid URL');
    });

    it('should allow empty payment link', () => {
      const clinic: Partial<Clinic> = {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'Test Clinic',
        paymentLink: '',
      };

      expect(() => repository.validateEntity(clinic)).not.toThrow();
    });
  });

  describe('mysqlDataToEntity', () => {
    it('should convert MySQL row to Clinic entity', () => {
      const mysqlRow = {
        id: 1,
        uuid: '123e4567-e89b-12d3-a456-************',
        practice_id: 5,
        name: 'Test Clinic',
        description: 'A test clinic',
        payment_link: 'https://example.com/pay',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
      };

      const result = repository['mysqlDataToEntity'](mysqlRow);

      expect(result).toEqual({
        id: '123e4567-e89b-12d3-a456-************',
        practiceId: 5,
        name: 'Test Clinic',
        description: 'A test clinic',
        paymentLink: 'https://example.com/pay',
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-02T00:00:00Z'),
      });
    });

    it('should handle null practice_id', () => {
      const mysqlRow = {
        id: 1,
        uuid: '123e4567-e89b-12d3-a456-************',
        practice_id: null,
        name: 'Test Clinic',
        description: null,
        payment_link: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
      };

      const result = repository['mysqlDataToEntity'](mysqlRow);

      expect(result.practiceId).toBeUndefined();
      expect(result.description).toBeUndefined();
      expect(result.paymentLink).toBeUndefined();
    });
  });

  describe('entityToMysqlData', () => {
    it('should convert Clinic entity to MySQL data', () => {
      const clinic: Clinic = {
        id: '123e4567-e89b-12d3-a456-************',
        practiceId: 5,
        name: 'Test Clinic',
        description: 'A test clinic',
        paymentLink: 'https://example.com/pay',
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-02T00:00:00Z'),
      };

      const result = repository['entityToMysqlData'](clinic);

      expect(result).toEqual({
        uuid: '123e4567-e89b-12d3-a456-************',
        practice_id: 5,
        name: 'Test Clinic',
        description: 'A test clinic',
        payment_link: 'https://example.com/pay',
        created_at: new Date('2024-01-01T00:00:00Z'),
        updated_at: new Date('2024-01-02T00:00:00Z'),
      });
    });

    it('should handle undefined optional fields', () => {
      const clinic: Clinic = {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'Test Clinic',
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-02T00:00:00Z'),
      };

      const result = repository['entityToMysqlData'](clinic);

      expect(result.practice_id).toBeNull();
      expect(result.description).toBeNull();
      expect(result.payment_link).toBeNull();
    });
  });

  describe('clinic-specific methods', () => {
    it('should have findById method', () => {
      expect(typeof repository.findById).toBe('function');
    });

    it('should have findByPracticeId method', () => {
      expect(typeof repository.findByPracticeId).toBe('function');
    });

    it('should have findByName method', () => {
      expect(typeof repository.findByName).toBe('function');
    });

    it('should have isNameUnique method', () => {
      expect(typeof repository.isNameUnique).toBe('function');
    });

    it('should have isIdUnique method', () => {
      expect(typeof repository.isIdUnique).toBe('function');
    });
  });
});
