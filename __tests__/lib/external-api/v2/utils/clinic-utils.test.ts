import {
  getClinicBySessionId,
  getLocationBySessionId,
} from '@/lib/external-api/v2/utils/clinic-utils';
import { callsService } from '@/utils/firestore';
import { AgentLocationMappingService } from '@/lib/services/agent-location-mapping';
import { ClinicsRepository } from '@/lib/repositories/clinics-repository';
import { ensureDbInitialized } from '@/lib/middleware/db-init';
import { Call } from '@/models/Call';
import { Location } from '@/models/Location';
import { Clinic } from '@/models/Clinic';

// Mock all dependencies
jest.mock('@/utils/firestore', () => ({
  callsService: {
    getCallsBySessionId: jest.fn(),
  },
}));

jest.mock('@/lib/services/agent-location-mapping', () => ({
  AgentLocationMappingService: {
    getLocationByAgentId: jest.fn(),
  },
}));

jest.mock('@/lib/repositories/clinics-repository');

jest.mock('@/lib/middleware/db-init', () => ({
  ensureDbInitialized: jest.fn(),
}));

jest.mock('@/lib/external-api/v2/utils/logger', () => ({
  warn: jest.fn(),
  info: jest.fn(),
  error: jest.fn(),
}));

describe('clinic-utils', () => {
  const mockCallsService = callsService as jest.Mocked<typeof callsService>;
  const mockAgentLocationMappingService = AgentLocationMappingService as jest.Mocked<
    typeof AgentLocationMappingService
  >;
  const mockEnsureDbInitialized = ensureDbInitialized as jest.MockedFunction<
    typeof ensureDbInitialized
  >;

  let mockClinicsRepository: { findById: jest.MockedFunction<ClinicsRepository['findById']> };

  beforeEach(() => {
    jest.clearAllMocks();

    // Create a mock ClinicsRepository instance
    mockClinicsRepository = {
      findById: jest.fn(),
    };

    // Mock the constructor to return our mock instance
    (ClinicsRepository as jest.MockedClass<typeof ClinicsRepository>).mockImplementation(
      () => mockClinicsRepository as unknown as ClinicsRepository,
    );
  });

  describe('getClinicBySessionId', () => {
    const sessionId = 'test-session-123';
    const agentId = 'test-agent-456';
    const locationId = 'test-location-789';
    const clinicId = 123;

    const mockCall: Call = {
      id: 'call-123',
      sessionId,
      agentId,
      phoneNumber: '************',
      date: new Date(),
      type: 1, // Use numeric type instead of string
      duration: '5 min',
      transcription: 'Hello, I need help',
      clientId: 'client-123',
      clientName: 'John Doe',
      clinicId: clinicId,
      userId: 'user-123',
      locationId: Number(locationId), // Convert to number
      recordingUrl: 'https://example.com/recording.mp3',
      reason: 'General inquiry',
      notes: 'Test call',
      priorityScore: 1,
      urgent: false,
      tags: [],
      hasVoiceMail: false,
      voicemailUrl: undefined,
      transcriptionWithAudio: undefined,
      callTypes: [1],
      lastAppointmentId: undefined,
      lastAppointmentDate: undefined,
      lastAppointmentPractitionerId: undefined,
      lastAppointmentPractitionerName: undefined,
      transferToLocationId: undefined,
    } as Call;

    const mockLocation: Location = {
      id: locationId,
      name: 'Test Location',
      clinicId,
      practiceId: 'practice-123',
      address: '123 Test St',
      phone: '************',
      timeZone: 'America/Chicago',
      officeHours: {},
      isActive: true,
      practiceName: 'Test Practice',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as Location;

    const mockClinic: Clinic = {
      id: clinicId.toString(),
      name: 'Test Clinic',
      description: 'A test clinic',
      practiceId: 456,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should successfully retrieve clinic by session ID', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      mockAgentLocationMappingService.getLocationByAgentId.mockResolvedValue(mockLocation);
      mockEnsureDbInitialized.mockResolvedValue();
      mockClinicsRepository.findById.mockResolvedValue(mockClinic);

      // Act
      const result = await getClinicBySessionId(sessionId);

      // Assert
      expect(result).toEqual(mockClinic);
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
      expect(mockEnsureDbInitialized).toHaveBeenCalled();
      expect(mockClinicsRepository.findById).toHaveBeenCalledWith(clinicId.toString());
    });

    it('should return null when call is not found', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([]);

      // Act
      const result = await getClinicBySessionId(sessionId);

      // Assert
      expect(result).toBeNull();
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).not.toHaveBeenCalled();
      expect(mockEnsureDbInitialized).not.toHaveBeenCalled();
      expect(mockClinicsRepository.findById).not.toHaveBeenCalled();
    });

    it('should return null when agent ID is not found', async () => {
      // Arrange
      const callWithoutAgentId = { ...mockCall, agentId: undefined };
      mockCallsService.getCallsBySessionId.mockResolvedValue([callWithoutAgentId]);

      // Act
      const result = await getClinicBySessionId(sessionId);

      // Assert
      expect(result).toBeNull();
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).not.toHaveBeenCalled();
      expect(mockEnsureDbInitialized).not.toHaveBeenCalled();
      expect(mockClinicsRepository.findById).not.toHaveBeenCalled();
    });

    it('should return null when location is not found', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      mockAgentLocationMappingService.getLocationByAgentId.mockResolvedValue(null);

      // Act
      const result = await getClinicBySessionId(sessionId);

      // Assert
      expect(result).toBeNull();
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
      expect(mockEnsureDbInitialized).not.toHaveBeenCalled();
      expect(mockClinicsRepository.findById).not.toHaveBeenCalled();
    });

    it('should return null when clinic is not found', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      mockAgentLocationMappingService.getLocationByAgentId.mockResolvedValue(mockLocation);
      mockEnsureDbInitialized.mockResolvedValue();
      mockClinicsRepository.findById.mockResolvedValue(null);

      // Act
      const result = await getClinicBySessionId(sessionId);

      // Assert
      expect(result).toBeNull();
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
      expect(mockEnsureDbInitialized).toHaveBeenCalled();
      expect(mockClinicsRepository.findById).toHaveBeenCalledWith(clinicId.toString());
    });

    it('should handle errors and rethrow them', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      mockCallsService.getCallsBySessionId.mockRejectedValue(error);

      // Act & Assert
      await expect(getClinicBySessionId(sessionId)).rejects.toThrow('Database connection failed');
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
    });

    it('should handle errors from AgentLocationMappingService', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      const error = new Error('Location service unavailable');
      mockAgentLocationMappingService.getLocationByAgentId.mockRejectedValue(error);

      // Act & Assert
      await expect(getClinicBySessionId(sessionId)).rejects.toThrow('Location service unavailable');
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
    });

    it('should handle errors from ClinicsRepository', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      mockAgentLocationMappingService.getLocationByAgentId.mockResolvedValue(mockLocation);
      mockEnsureDbInitialized.mockResolvedValue();
      const error = new Error('Clinic repository error');
      mockClinicsRepository.findById.mockRejectedValue(error);

      // Act & Assert
      await expect(getClinicBySessionId(sessionId)).rejects.toThrow('Clinic repository error');
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
      expect(mockEnsureDbInitialized).toHaveBeenCalled();
      expect(mockClinicsRepository.findById).toHaveBeenCalledWith(clinicId.toString());
    });
  });

  describe('getLocationBySessionId', () => {
    const sessionId = 'test-session-123';
    const agentId = 'test-agent-456';
    const locationId = 'test-location-789';
    const clinicId = 123;

    const mockCall: Call = {
      id: 'call-123',
      sessionId,
      agentId,
      phoneNumber: '************',
      date: new Date(),
      type: 1, // Use numeric type instead of string
      duration: '5 min',
      transcription: 'Hello, I need help',
      clientId: 'client-123',
      clientName: 'John Doe',
      clinicId: clinicId,
      userId: 'user-123',
      locationId: Number(locationId), // Convert to number
      recordingUrl: 'https://example.com/recording.mp3',
      reason: 'General inquiry',
      notes: 'Test call',
      priorityScore: 1,
      urgent: false,
      tags: [],
      hasVoiceMail: false,
      voicemailUrl: undefined,
      transcriptionWithAudio: undefined,
      callTypes: [1],
      lastAppointmentId: undefined,
      lastAppointmentDate: undefined,
      lastAppointmentPractitionerId: undefined,
      lastAppointmentPractitionerName: undefined,
      transferToLocationId: undefined,
    } as Call;

    const mockLocation: Location = {
      id: locationId,
      name: 'Test Location',
      clinicId,
      practiceId: 'practice-123',
      address: '123 Test St',
      phone: '************',
      timeZone: 'America/Chicago',
      officeHours: {},
      isActive: true,
      practiceName: 'Test Practice',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as Location;

    it('should successfully retrieve location by session ID', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      mockAgentLocationMappingService.getLocationByAgentId.mockResolvedValue(mockLocation);

      // Act
      const result = await getLocationBySessionId(sessionId);

      // Assert
      expect(result).toEqual(mockLocation);
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
    });

    it('should return null when call is not found', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([]);

      // Act
      const result = await getLocationBySessionId(sessionId);

      // Assert
      expect(result).toBeNull();
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).not.toHaveBeenCalled();
    });

    it('should return null when agent ID is not found', async () => {
      // Arrange
      const originalEnv = process.env.GCP_AGENT_ID;
      delete process.env.GCP_AGENT_ID;

      const callWithoutAgentId = { ...mockCall, agentId: undefined };
      mockCallsService.getCallsBySessionId.mockResolvedValue([callWithoutAgentId]);

      // Act
      const result = await getLocationBySessionId(sessionId);

      // Assert
      expect(result).toBeNull();
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).not.toHaveBeenCalled();

      // Cleanup
      if (originalEnv) {
        process.env.GCP_AGENT_ID = originalEnv;
      }
    });

    it('should return null when location is not found', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      mockAgentLocationMappingService.getLocationByAgentId.mockResolvedValue(null);

      // Act
      const result = await getLocationBySessionId(sessionId);

      // Assert
      expect(result).toBeNull();
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
    });

    it('should handle errors and rethrow them', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      mockCallsService.getCallsBySessionId.mockRejectedValue(error);

      // Act & Assert
      await expect(getLocationBySessionId(sessionId)).rejects.toThrow('Database connection failed');
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
    });

    it('should handle errors from AgentLocationMappingService', async () => {
      // Arrange
      mockCallsService.getCallsBySessionId.mockResolvedValue([mockCall]);
      const error = new Error('Location service unavailable');
      mockAgentLocationMappingService.getLocationByAgentId.mockRejectedValue(error);

      // Act & Assert
      await expect(getLocationBySessionId(sessionId)).rejects.toThrow(
        'Location service unavailable',
      );
      expect(mockCallsService.getCallsBySessionId).toHaveBeenCalledWith(sessionId);
      expect(mockAgentLocationMappingService.getLocationByAgentId).toHaveBeenCalledWith(agentId);
    });
  });
});
