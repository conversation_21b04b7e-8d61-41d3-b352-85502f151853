import { createMocks } from 'node-mocks-http';
import type { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../../pages/api/external-api/v2/appointments/[id]/change';
import { getProviderFromRequest } from '@/lib/external-api/v2';
import { AppointmentStatus } from '@/lib/external-api/v2';
import { NextechAppointmentTypes } from '@/models/AppointmentTypes';

// Mock Firebase
jest.mock('../../../../../../utils/firestore', () => ({
  callSessionsService: {
    findCallSessionsByAppointmentId: jest.fn().mockResolvedValue([]),
    addOrUpdateCallSession: jest.fn().mockResolvedValue({}),
  },
}));

// Mock the database initialization
jest.mock('@/lib/middleware/db-init', () => ({
  ensureDbInitialized: jest.fn(),
}));

// Mock the locations repository
jest.mock('@/lib/repositories/locations-repository', () => ({
  LocationsRepository: jest.fn().mockImplementation(() => ({
    findById: jest.fn().mockResolvedValue({ name: 'Test Location' }),
  })),
}));

// Mock the logger
jest.mock('@/lib/external-api/v2/utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock the event emitter
jest.mock('@/lib/external-api/v2/utils/events', () => ({
  eventEmitter: {
    emit: jest.fn(),
  },
  EVENTS: {
    APPOINTMENT: {
      CREATED: 'appointment:created',
    },
  },
}));

// Mock the external-api/v2 module
jest.mock('../../../../../../lib/external-api/v2', () => {
  const original = jest.requireActual('../../../../../../lib/external-api/v2');
  return {
    ...original,
    createApiHandler: jest.fn().mockImplementation((handler, options = {}) => {
      return async (req: NextApiRequest, res: NextApiResponse) => {
        try {
          // Apply middlewares manually if specified
          if (options.middleware) {
            for (const middleware of options.middleware) {
              await new Promise<void>((resolve, reject) => {
                try {
                  middleware(req, res, () => resolve());
                } catch (error) {
                  reject(error);
                }
              });
            }
          }

          // Call the handler
          return await handler(req, res);
        } catch (error: any) {
          // Simulate error handling in the actual implementation
          if (
            error instanceof original.UnauthorizedError ||
            (error.message && error.message.toLowerCase().includes('api key'))
          ) {
            res.status(401).json({
              status: 401,
              code: 'UNAUTHORIZED',
              message: error.message,
            });
          } else if (
            error instanceof original.BadRequestError ||
            (error.message &&
              (error.message.toLowerCase().includes('invalid') ||
                error.message.toLowerCase().includes('missing')))
          ) {
            res.status(400).json({
              status: 400,
              code: 'BAD_REQUEST',
              message: error.message,
              details: error.details,
            });
          } else if (
            error instanceof original.NotFoundError ||
            (error.message && error.message.toLowerCase().includes('not found'))
          ) {
            res.status(404).json({
              status: 404,
              code: 'NOT_FOUND',
              message: error.message,
            });
          } else {
            res.status(500).json({
              status: 500,
              code: 'INTERNAL_SERVER_ERROR',
              message: error.message,
            });
          }
        }
      };
    }),
    validateApiKey: jest.fn().mockImplementation((req, res, next) => next()),
    getProviderFromRequest: jest.fn(),
    ensureProvidersInitialized: jest.fn().mockImplementation((req, res, next) => next()),
  };
});

describe('POST /api/external-api/v2/appointments/[id]/change', () => {
  // Mock appointment service
  const mockAppointmentService = {
    getAppointmentById: jest.fn(),
    changeAppointment: jest.fn(),
  };

  // Mock provider
  const mockProvider = {
    name: 'nextech',
    getAppointmentService: jest.fn().mockReturnValue(mockAppointmentService),
  };

  // Mock appointment data
  const mockAppointment = {
    id: '123',
    patientId: '456',
    providerId: '789',
    locationId: '101',
    clinicId: '202',
    startTime: '2030-01-01T10:00:00Z',
    endTime: '2030-01-01T11:00:00Z',
    status: AppointmentStatus.BOOKED,
    type: NextechAppointmentTypes.FOLLOW_UP,
    reason: 'Original reason',
    notes: 'Original notes',
    createdAt: '2023-01-01T09:00:00Z',
    updatedAt: '2023-01-01T09:00:00Z',
    providerInfo: {
      provider: 'nextech',
      externalId: '123',
    },
  };

  // Updated appointment data
  const updatedMockAppointment = {
    ...mockAppointment,
    id: '456',
    startTime: '2030-01-02T10:00:00Z',
    endTime: '2030-01-02T11:00:00Z',
    reason: 'Updated reason',
    createdAt: '2023-01-01T15:00:00Z',
    updatedAt: '2023-01-01T15:00:00Z',
  };

  // Valid update data
  const validUpdateData = {
    startTime: '2030-01-02T10:00:00Z',
    endTime: '2030-01-02T11:00:00Z',
    reason: 'Updated reason',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getProviderFromRequest as jest.Mock).mockReturnValue(mockProvider);
  });

  it('should change an appointment successfully', async () => {
    // Mock the appointment service methods
    mockAppointmentService.getAppointmentById.mockResolvedValueOnce(mockAppointment);
    mockAppointmentService.changeAppointment.mockResolvedValueOnce(updatedMockAppointment);

    // Create mock request and response
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-key',
        'Content-Type': 'application/json',
      },
      query: { id: '123' },
      body: validUpdateData,
    });

    // Call the handler
    await handler(req, res);

    // Verify the response
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(updatedMockAppointment);

    // Verify that the appointment service methods were called correctly
    expect(mockAppointmentService.getAppointmentById).toHaveBeenCalledWith('123');
    expect(mockAppointmentService.changeAppointment).toHaveBeenCalledWith('123', validUpdateData);
  });

  it('should return 400 if appointment ID is missing', async () => {
    // Create mock request and response
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-key',
        'Content-Type': 'application/json',
      },
      query: { id: '' },
      body: validUpdateData,
    });

    // Call the handler
    await handler(req, res);

    // Verify the response
    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty(
      'message',
      'Invalid or missing appointment ID',
    );
  });

  it('should return 400 if request body is empty', async () => {
    // Create mock request and response
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-key',
        'Content-Type': 'application/json',
      },
      query: { id: '123' },
      body: {},
    });

    // Call the handler
    await handler(req, res);

    // Verify the response
    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid request body');
  });

  it('should return 404 if appointment is not found', async () => {
    // Mock the appointment service methods
    mockAppointmentService.getAppointmentById.mockResolvedValueOnce(null);

    // Create mock request and response
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-key',
        'Content-Type': 'application/json',
      },
      query: { id: '123' },
      body: validUpdateData,
    });

    // Call the handler
    await handler(req, res);

    // Verify the response
    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Appointment not found');
  });

  it('should return 400 if request body is invalid', async () => {
    // Create mock request and response with invalid data
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-key',
        'Content-Type': 'application/json',
      },
      query: { id: '123' },
      body: {
        startTime: 'invalid-date-format', // Invalid date format
        endTime: '2023-01-02T11:00:00Z',
      },
    });

    // Call the handler
    await handler(req, res);

    // Verify the response
    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid request body');
  });

  it('should return 405 for non-POST methods', async () => {
    // Create mock request and response with GET method
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-key',
      },
      query: { id: '123' },
    });

    // Call the handler
    await handler(req, res);

    // Verify the response
    expect(res._getStatusCode()).toBe(405);
  });
});
