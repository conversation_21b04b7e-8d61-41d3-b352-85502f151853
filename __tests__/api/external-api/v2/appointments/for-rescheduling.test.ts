import { NextApiRequest, NextApiResponse } from 'next';
import { createMocks } from 'node-mocks-http';

// Mock Firebase before importing anything else
jest.mock('@/utils/firestore', () => ({
  callSessionsService: {
    updateCallSession: jest.fn(),
    getCallSession: jest.fn(),
  },
  callsService: {
    updateCall: jest.fn(),
    getCall: jest.fn(),
  },
}));

// Mock the database initialization
jest.mock('@/lib/middleware/db-init', () => ({
  ensureDbInitialized: jest.fn(),
}));

// Mock the locations repository
jest.mock('@/lib/repositories/locations-repository', () => ({
  LocationsRepository: jest.fn().mockImplementation(() => ({
    getLocationNamesMap: jest.fn().mockResolvedValue(new Map()),
  })),
}));

// Mock the call type utils
jest.mock('@/lib/external-api/v2/utils/call-type-utils', () => ({
  updateCallSessionType: jest.fn(),
  updateCallSessionTypeForPatient: jest.fn(),
}));

// Mock the external API v2 module
jest.mock('@/lib/external-api/v2', () => {
  const originalModule = jest.requireActual('@/lib/external-api/v2');

  return {
    ...originalModule,
    createApiHandler: jest.fn().mockImplementation((handler, options = {}) => {
      return async (req: NextApiRequest, res: NextApiResponse) => {
        try {
          // Apply middlewares manually if specified
          if (options.middleware) {
            for (const middleware of options.middleware) {
              await new Promise<void>((resolve, reject) => {
                try {
                  middleware(req, res, () => resolve());
                } catch (error) {
                  reject(error);
                }
              });
            }
          }

          // Call the handler
          return await handler(req, res);
        } catch (error: any) {
          // Simulate error handling in the actual implementation
          if (
            error instanceof originalModule.UnauthorizedError ||
            (error.message && error.message.toLowerCase().includes('api key'))
          ) {
            res.status(401).json({
              status: 401,
              code: 'UNAUTHORIZED',
              message: error.message,
            });
          } else if (
            error instanceof originalModule.BadRequestError ||
            (error.message &&
              (error.message.toLowerCase().includes('invalid') ||
                error.message.toLowerCase().includes('missing')))
          ) {
            res.status(400).json({
              status: 400,
              code: 'BAD_REQUEST',
              message: error.message,
              details: error.details,
            });
          } else if (
            error instanceof originalModule.NotFoundError ||
            (error.message && error.message.toLowerCase().includes('not found'))
          ) {
            res.status(404).json({
              status: 404,
              code: 'NOT_FOUND',
              message: error.message,
            });
          } else {
            res.status(500).json({
              status: 500,
              code: 'INTERNAL_SERVER_ERROR',
              message: error.message,
            });
          }
        }
      };
    }),
    validateApiKey: jest.fn().mockImplementation((req, res, next) => next()),
    getProviderFromRequest: jest.fn(),
    ensureProvidersInitialized: jest.fn().mockImplementation((req, res, next) => next()),
  };
});

// Mock the provider registry and appointment service
jest.mock('@/lib/external-api/v2/providers', () => {
  const mockAppointmentService = {
    getAppointmentsForRescheduling: jest.fn(),
  };

  const mockProvider = {
    name: 'nextech',
    getAppointmentService: jest.fn(() => mockAppointmentService),
  };

  return {
    providerRegistry: {
      getProvider: jest.fn(() => mockProvider),
      getAvailableProviders: jest.fn(() => ['nextech']),
    },
  };
});

import handler from '@/pages/api/external-api/v2/appointments/for-rescheduling';
import { AppointmentStatus } from '@/lib/external-api/v2/models/types';
import { providerRegistry } from '@/lib/external-api/v2/providers';
import { getProviderFromRequest } from '@/lib/external-api/v2';

describe('GET /api/external-api/v2/appointments/for-rescheduling', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock getProviderFromRequest to return the mock provider
    const mockProvider = providerRegistry.getProvider();
    (getProviderFromRequest as jest.Mock).mockReturnValue(mockProvider);
  });

  // Mock appointment data
  const mockAppointments = {
    items: [
      {
        id: 'appt-123',
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        clinicId: 'clinic-123',
        startTime: '2099-05-15T10:00:00Z',
        endTime: '2099-05-15T11:00:00Z',
        status: AppointmentStatus.BOOKED,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        providerInfo: {
          provider: 'nextech',
          externalId: 'appt-123',
        },
      },
    ],
    pagination: {
      totalCount: 1,
      limit: 30,
      offset: 0,
      hasMore: false,
      links: {},
    },
  };

  it('should return 405 for non-GET requests', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Method POST Not Allowed' });
  });

  it('should return 400 if patientId is missing', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { isForRescheduling: 'true' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid request parameters');
  });

  it('should return appointments for a patient with isForRescheduling=false', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123', isForRescheduling: 'false' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      false,
    );
  });

  it('should return appointments for a patient with isForRescheduling=true', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123', isForRescheduling: 'true' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      true,
    );
  });

  it('should handle numeric values for isForRescheduling', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123', isForRescheduling: '1' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      true,
    );
  });

  it('should use default value for isForRescheduling if not provided', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      false,
    );
  });

  it('should handle errors gracefully', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method to throw an error
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockRejectedValueOnce(
      new Error('API Error'),
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'API Error');
  });
});
