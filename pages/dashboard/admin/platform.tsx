import Head from 'next/head';
import { useEffect, useMemo, useState, Fragment } from 'react';

import DashboardLayout from '@/components/DashboardLayout';
import { FeatureKey, PermissionLevel } from '@/models/auth';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getToken } from '@/utils/auth';
import {
  Button,
  Card,
  Checkbox,
  Label,
  Modal,
  Select,
  Spinner,
  Table,
  TextInput,
} from 'flowbite-react';
import { HiChevronDown, HiChevronRight } from 'react-icons/hi';

import type { GetServerSideProps } from 'next';

type CreateClinicForm = {
  clinicName: string;
  clinicLogoUrl?: string;
  clinicAddress?: string;
  clinicPhone?: string;
  adminEmail: string;
  adminPassword: string;
  adminName: string;
};

type CreateLocationForm = {
  clinicId?: number;
  name: string;
  address: string;
  phone?: string;
  practiceId: string;
};

export const getServerSideProps: GetServerSideProps = async ctx => {
  const req = ctx.req;
  const user = await verifyAuthAndGetUser(req as never);
  if (!user) {
    return { redirect: { destination: '/login', permanent: false } };
  }
  // Ensure DB is ready before RBAC check in SSR
  try {
    const { ensureDbInitialized } = await import('@/lib/middleware/db-init');
    await ensureDbInitialized();
  } catch (e) {
    console.error('DB init failed in getServerSideProps', e);
    return { notFound: true };
  }
  const { PermissionService } = await import('@/lib/services/permission-service');
  const svc = new PermissionService();
  const level = await svc.resolveEffectiveLevel(user.id, FeatureKey.PLATFORM_ADMIN);
  if (level !== PermissionLevel.ADMIN) {
    return { notFound: true };
  }
  return { props: {} };
};

export default function PlatformAdminPage() {
  const [clinicForm, setClinicForm] = useState<CreateClinicForm>({
    clinicName: '',
    adminEmail: '',
    adminPassword: '',
    adminName: '',
  });
  const [locationForm, setLocationForm] = useState<CreateLocationForm>({
    clinicId: undefined,
    name: '',
    address: '',
    practiceId: '',
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showClinicModal, setShowClinicModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [editingClinicId, setEditingClinicId] = useState<string | null>(null);
  const [editingLocation, setEditingLocation] = useState<LocationRow | null>(null);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteClinicId, setInviteClinicId] = useState<number | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRoleId, setInviteRoleId] = useState<string | undefined>(undefined);
  const [inviteLocationIds, setInviteLocationIds] = useState<string[]>([]);
  const [availableInviteRoles, setAvailableInviteRoles] = useState<
    Array<{ id: string; name: string }>
  >([]);
  const [inviteLoadingRoles, setInviteLoadingRoles] = useState(false);

  // Clinics list (SUPER_ADMIN only)
  type ClinicRow = {
    id: string;
    name: string;
    address?: string | null;
    phone?: string | null;
    logoUrl?: string | null;
    createdAt?: string;
    updatedAt?: string;
  };
  const [clinics, setClinics] = useState<ClinicRow[]>([]);
  const [clinicsLoading, setClinicsLoading] = useState(false);
  const [clinicsError, setClinicsError] = useState<string | null>(null);

  // All locations across clinics (read-only overview for SUPER_ADMIN)
  type LocationRow = {
    id: string;
    clinicId: number;
    practiceId: string;
    name: string;
    address: string;
    phone?: string | null;
    practiceName?: string;
    isActive?: boolean;
  };
  const [allLocations, setAllLocations] = useState<LocationRow[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(false);
  const [locationsError, setLocationsError] = useState<string | null>(null);
  const [expandedClinicIds, setExpandedClinicIds] = useState<Set<string>>(new Set());

  const clinicIdToLocations = useMemo(() => {
    const map = new Map<number, LocationRow[]>();
    for (const loc of allLocations) {
      const arr = map.get(loc.clinicId) ?? [];
      arr.push(loc);
      map.set(loc.clinicId, arr);
    }
    return map;
  }, [allLocations]);

  const toggleExpanded = (clinicId: string) => {
    setExpandedClinicIds(prev => {
      const next = new Set(prev);
      if (next.has(clinicId)) {
        next.delete(clinicId);
      } else {
        next.add(clinicId);
      }
      return next;
    });
  };

  const loadClinics = useMemo(
    () => async () => {
      try {
        setClinicsLoading(true);
        setClinicsError(null);
        const res = await fetch('/api/clinics', {
          headers: { Authorization: `Bearer ${getToken()}` },
        });
        if (!res.ok) {
          const data = await res.json().catch(() => ({}));
          throw new Error(data?.message || 'Failed to load clinics');
        }
        const data: ClinicRow[] = await res.json();
        setClinics(data);
      } catch (e) {
        setClinicsError(e instanceof Error ? e.message : String(e));
      } finally {
        setClinicsLoading(false);
      }
    },
    [],
  );

  const loadAllLocations = useMemo(
    () => async () => {
      try {
        setLocationsLoading(true);
        setLocationsError(null);
        const res = await fetch('/api/platform/locations/all', {
          headers: { Authorization: `Bearer ${getToken()}` },
        });
        if (!res.ok) {
          const data = await res.json().catch(() => ({}));
          throw new Error(data?.message || 'Failed to load locations');
        }
        const data: LocationRow[] = await res.json();
        setAllLocations(data);
      } catch (e) {
        setLocationsError(e instanceof Error ? e.message : String(e));
      } finally {
        setLocationsLoading(false);
      }
    },
    [],
  );

  useEffect(() => {
    // Load initial data for SUPER_ADMIN overview
    void loadClinics();
    void loadAllLocations();
  }, [loadClinics, loadAllLocations]);

  // Load role options when invite clinic changes
  useEffect(() => {
    const loadRoles = async () => {
      if (!inviteClinicId) {
        setAvailableInviteRoles([]);
        return;
      }
      try {
        setInviteLoadingRoles(true);
        const res = await fetch(`/api/roles?clinicId=${inviteClinicId}`, {
          headers: { Authorization: `Bearer ${getToken()}` },
        });
        if (res.ok) {
          const roles = await res.json();
          const mapped = (roles || [])
            .filter((r: { clinic_id: number | null }) => r.clinic_id !== null)
            .map((r: { id: string; name: string }) => ({ id: r.id, name: r.name }));
          setAvailableInviteRoles(mapped);
        } else {
          setAvailableInviteRoles([]);
        }
      } catch {
        setAvailableInviteRoles([]);
      } finally {
        setInviteLoadingRoles(false);
      }
    };
    void loadRoles();
  }, [inviteClinicId]);

  const submitClinic = async () => {
    setLoading(true);
    setError(null);
    setMessage(null);
    try {
      const isEditing = Boolean(editingClinicId);
      const endpoint = isEditing ? `/api/clinics/${editingClinicId}` : '/api/clinics/create';
      const method = isEditing ? 'PUT' : 'POST';
      const payload = isEditing
        ? {
            name: clinicForm.clinicName || undefined,
            address: clinicForm.clinicAddress ?? undefined,
            phone: clinicForm.clinicPhone ?? undefined,
            logoUrl: clinicForm.clinicLogoUrl ?? undefined,
          }
        : clinicForm;

      const res = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${getToken()}` },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.message || 'Failed to create clinic');
      if (isEditing) {
        setMessage('Clinic updated successfully');
      } else {
        setMessage(`Clinic created. clinicId=${data.clinicId}, adminId=${data.adminId}`);
        setClinicForm({ clinicName: '', adminEmail: '', adminPassword: '', adminName: '' });
      }
      setShowClinicModal(false);
      setEditingClinicId(null);
      // Refresh lists
      void loadClinics();
    } catch (e) {
      setError(e instanceof Error ? e.message : String(e));
    } finally {
      setLoading(false);
    }
  };

  const submitLocation = async () => {
    setLoading(true);
    setError(null);
    setMessage(null);
    try {
      const isEditing = Boolean(editingLocation);
      const endpoint = isEditing ? `/api/locations/${editingLocation!.id}` : '/api/locations';
      const method = isEditing ? 'PUT' : 'POST';

      const res = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${getToken()}` },
        body: JSON.stringify(locationForm),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.message || 'Failed to create location');
      if (isEditing) {
        setMessage('Location updated successfully');
      } else {
        setMessage(`Location created: ${data?.location?.name || ''}`);
        setLocationForm({ clinicId: undefined, name: '', address: '', practiceId: '' });
      }
      setShowLocationModal(false);
      setEditingLocation(null);
      // Refresh locations overview
      void loadAllLocations();
    } catch (e) {
      setError(e instanceof Error ? e.message : String(e));
    } finally {
      setLoading(false);
    }
  };

  const handleEditClinic = (clinic: ClinicRow) => {
    setEditingClinicId(clinic.id);
    setClinicForm({
      clinicName: clinic.name,
      clinicLogoUrl: clinic.logoUrl || '',
      clinicAddress: clinic.address || '',
      clinicPhone: clinic.phone || '',
      adminEmail: '',
      adminPassword: '',
      adminName: '',
    });
    setShowClinicModal(true);
  };

  const handleEditLocation = (loc: LocationRow) => {
    setEditingLocation(loc);
    setLocationForm({
      clinicId: loc.clinicId,
      name: loc.name,
      address: loc.address,
      phone: loc.phone || '',
      practiceId: loc.practiceId,
    });
    setShowLocationModal(true);
  };

  const handleDeleteLocation = async (loc: LocationRow) => {
    if (!confirm(`Delete location "${loc.name}"?`)) return;
    try {
      setLoading(true);
      setError(null);
      const res = await fetch(`/api/locations/${loc.id}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${getToken()}` },
      });
      const data = await res.json().catch(() => ({}));
      if (!res.ok) throw new Error(data?.message || 'Failed to delete location');
      setMessage('Location deleted');
      void loadAllLocations();
    } catch (e) {
      setError(e instanceof Error ? e.message : String(e));
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Platform Admin</title>
      </Head>
      <DashboardLayout>
        <div className="container mx-auto max-w-6xl">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-semibold">Platform Admin (SUPER_ADMIN)</h1>
            <div className="flex gap-2">
              <Button color="blue" onClick={() => setShowClinicModal(true)}>
                Create Clinic
              </Button>
              <Button color="blue" onClick={() => setShowLocationModal(true)}>
                Create Location
              </Button>
              <Button color="blue" onClick={() => setShowInviteModal(true)}>
                Invite Staff
              </Button>
            </div>
          </div>

          {message && <div className="mb-4 rounded bg-green-100 p-3 text-green-800">{message}</div>}
          {error && <div className="mb-4 rounded bg-red-100 p-3 text-red-800">{error}</div>}

          <div className="grid grid-cols-1 gap-6">
            <Card>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-medium">Clinics</h2>
                <div className="flex gap-2">
                  <Button
                    color="light"
                    onClick={() => void loadClinics()}
                    disabled={clinicsLoading}
                  >
                    {clinicsLoading ? 'Refreshing…' : 'Refresh Clinics'}
                  </Button>
                  <Button
                    color="light"
                    onClick={() => void loadAllLocations()}
                    disabled={locationsLoading}
                  >
                    {locationsLoading ? 'Refreshing…' : 'Refresh Locations'}
                  </Button>
                </div>
              </div>
              {(clinicsError || locationsError) && (
                <div className="mb-3 rounded bg-red-50 px-3 py-2 text-red-800">
                  {clinicsError || locationsError}
                </div>
              )}
              {clinicsLoading && (
                <div className="flex items-center justify-center py-6">
                  <Spinner />
                </div>
              )}
              <div className="overflow-x-auto">
                <Table striped>
                  <Table.Head>
                    <Table.HeadCell />
                    <Table.HeadCell>Name</Table.HeadCell>
                    <Table.HeadCell>Address</Table.HeadCell>
                    <Table.HeadCell>Phone</Table.HeadCell>
                    <Table.HeadCell>Locations</Table.HeadCell>
                    <Table.HeadCell>Clinic ID</Table.HeadCell>
                  </Table.Head>
                  <Table.Body>
                    {clinics.map(c => {
                      const locs = clinicIdToLocations.get(Number(c.id)) ?? [];
                      const isOpen = expandedClinicIds.has(c.id);
                      return (
                        <Fragment key={`clinic-block-${c.id}`}>
                          <Table.Row key={c.id} className="bg-white">
                            <Table.Cell className="w-10">
                              <button
                                aria-label={isOpen ? 'Collapse' : 'Expand'}
                                className="p-1 rounded hover:bg-gray-100"
                                onClick={() => toggleExpanded(c.id)}
                              >
                                {isOpen ? (
                                  <HiChevronDown className="h-5 w-5" />
                                ) : (
                                  <HiChevronRight className="h-5 w-5" />
                                )}
                              </button>
                            </Table.Cell>
                            <Table.Cell className="font-medium">{c.name}</Table.Cell>
                            <Table.Cell>{c.address || '—'}</Table.Cell>
                            <Table.Cell>{c.phone || '—'}</Table.Cell>
                            <Table.Cell className="whitespace-nowrap">{locs.length}</Table.Cell>
                            <Table.Cell className="whitespace-nowrap">{c.id}</Table.Cell>
                            <Table.Cell className="text-right">
                              <Button size="xs" color="light" onClick={() => handleEditClinic(c)}>
                                Edit
                              </Button>
                            </Table.Cell>
                          </Table.Row>
                          {isOpen && (
                            <Table.Row key={`expand-${c.id}`}>
                              <Table.Cell colSpan={7} className="bg-gray-50 p-0">
                                <div className="px-4 py-3">
                                  {locationsLoading ? (
                                    <div className="flex items-center justify-center py-6">
                                      <Spinner />
                                    </div>
                                  ) : (
                                    <div className="overflow-x-auto">
                                      <Table>
                                        <Table.Head>
                                          <Table.HeadCell>Location</Table.HeadCell>
                                          <Table.HeadCell>Practice</Table.HeadCell>
                                          <Table.HeadCell>Address</Table.HeadCell>
                                          <Table.HeadCell>Phone</Table.HeadCell>
                                          <Table.HeadCell>Status</Table.HeadCell>
                                          <Table.HeadCell className="text-right">
                                            Actions
                                          </Table.HeadCell>
                                        </Table.Head>
                                        <Table.Body>
                                          {locs.map(l => (
                                            <Table.Row key={l.id} className="bg-white">
                                              <Table.Cell className="font-medium">
                                                {l.name}
                                              </Table.Cell>
                                              <Table.Cell className="whitespace-nowrap">
                                                {l.practiceName || l.practiceId}
                                              </Table.Cell>
                                              <Table.Cell>{l.address}</Table.Cell>
                                              <Table.Cell>{l.phone || '—'}</Table.Cell>
                                              <Table.Cell>
                                                {l.isActive === false ? 'Inactive' : 'Active'}
                                              </Table.Cell>
                                              <Table.Cell className="text-right">
                                                <div className="flex gap-2 justify-end">
                                                  <Button
                                                    size="xs"
                                                    color="light"
                                                    onClick={() => handleEditLocation(l)}
                                                  >
                                                    Edit
                                                  </Button>
                                                  <Button
                                                    size="xs"
                                                    color="failure"
                                                    onClick={() => handleDeleteLocation(l)}
                                                  >
                                                    Delete
                                                  </Button>
                                                </div>
                                              </Table.Cell>
                                            </Table.Row>
                                          ))}
                                          {locs.length === 0 && (
                                            <Table.Row>
                                              <Table.Cell colSpan={6} className="text-center">
                                                No locations for this clinic
                                              </Table.Cell>
                                            </Table.Row>
                                          )}
                                        </Table.Body>
                                      </Table>
                                    </div>
                                  )}
                                </div>
                              </Table.Cell>
                            </Table.Row>
                          )}
                        </Fragment>
                      );
                    })}
                    {clinics.length === 0 && !clinicsLoading && (
                      <Table.Row>
                        <Table.Cell colSpan={6} className="text-center">
                          No clinics found
                        </Table.Cell>
                      </Table.Row>
                    )}
                  </Table.Body>
                </Table>
              </div>
            </Card>
          </div>

          {/* Create Clinic Modal */}
          <Modal
            show={showClinicModal}
            onClose={() => setShowClinicModal(false)}
            size="md"
            dismissible
          >
            <Modal.Header>Create Clinic</Modal.Header>
            <Modal.Body>
              <div className="grid gap-3">
                <TextInput
                  placeholder="Clinic Name"
                  value={clinicForm.clinicName}
                  onChange={e => setClinicForm({ ...clinicForm, clinicName: e.target.value })}
                />
                <TextInput
                  placeholder="Clinic Logo URL (optional)"
                  value={clinicForm.clinicLogoUrl || ''}
                  onChange={e => setClinicForm({ ...clinicForm, clinicLogoUrl: e.target.value })}
                />
                <TextInput
                  placeholder="Clinic Address (optional)"
                  value={clinicForm.clinicAddress || ''}
                  onChange={e => setClinicForm({ ...clinicForm, clinicAddress: e.target.value })}
                />
                <TextInput
                  placeholder="Clinic Phone (optional)"
                  value={clinicForm.clinicPhone || ''}
                  onChange={e => setClinicForm({ ...clinicForm, clinicPhone: e.target.value })}
                />
                <TextInput
                  placeholder="Admin Email"
                  value={clinicForm.adminEmail}
                  onChange={e => setClinicForm({ ...clinicForm, adminEmail: e.target.value })}
                />
                <TextInput
                  placeholder="Admin Password"
                  type="password"
                  value={clinicForm.adminPassword}
                  onChange={e => setClinicForm({ ...clinicForm, adminPassword: e.target.value })}
                />
                <TextInput
                  placeholder="Admin Name"
                  value={clinicForm.adminName}
                  onChange={e => setClinicForm({ ...clinicForm, adminName: e.target.value })}
                />
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button color="gray" onClick={() => setShowClinicModal(false)} disabled={loading}>
                Cancel
              </Button>
              <Button color="blue" onClick={submitClinic} disabled={loading}>
                {loading ? 'Creating…' : 'Create Clinic'}
              </Button>
            </Modal.Footer>
          </Modal>

          {/* Create Location Modal */}
          <Modal
            show={showLocationModal}
            onClose={() => setShowLocationModal(false)}
            size="md"
            dismissible
          >
            <Modal.Header>Create Location</Modal.Header>
            <Modal.Body>
              <div className="grid gap-3">
                <TextInput
                  placeholder="Clinic ID (optional for SUPER_ADMIN)"
                  value={locationForm.clinicId?.toString() ?? ''}
                  onChange={e =>
                    setLocationForm({
                      ...locationForm,
                      clinicId: Number(e.target.value) || undefined,
                    })
                  }
                />
                <TextInput
                  placeholder="Location Name"
                  value={locationForm.name}
                  onChange={e => setLocationForm({ ...locationForm, name: e.target.value })}
                />
                <TextInput
                  placeholder="Address"
                  value={locationForm.address}
                  onChange={e => setLocationForm({ ...locationForm, address: e.target.value })}
                />
                <TextInput
                  placeholder="Phone (optional)"
                  value={locationForm.phone || ''}
                  onChange={e => setLocationForm({ ...locationForm, phone: e.target.value })}
                />
                <TextInput
                  placeholder="Practice ID"
                  value={locationForm.practiceId}
                  onChange={e => setLocationForm({ ...locationForm, practiceId: e.target.value })}
                />
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button color="gray" onClick={() => setShowLocationModal(false)} disabled={loading}>
                Cancel
              </Button>
              <Button color="blue" onClick={submitLocation} disabled={loading}>
                {loading ? 'Creating…' : 'Create Location'}
              </Button>
            </Modal.Footer>
          </Modal>

          {/* Invite Staff Modal */}
          <Modal
            show={showInviteModal}
            onClose={() => setShowInviteModal(false)}
            size="md"
            dismissible
          >
            <Modal.Header>Invite Staff Member</Modal.Header>
            <Modal.Body>
              <div className="grid gap-4">
                <div>
                  <Label value="Teammate's Email (optional)" />
                  <TextInput
                    placeholder="<EMAIL>"
                    value={inviteEmail}
                    onChange={e => setInviteEmail(e.target.value)}
                  />
                </div>
                <div>
                  <Label value="Target Clinic" />
                  <Select
                    value={inviteClinicId?.toString() ?? ''}
                    onChange={e => {
                      const val = e.target.value;
                      setInviteClinicId(val ? Number(val) : null);
                      setInviteRoleId(undefined);
                      setInviteLocationIds([]);
                    }}
                  >
                    <option value="">Select a clinic</option>
                    {clinics.map(c => (
                      <option key={c.id} value={Number(c.id)}>
                        {c.name} (ID: {c.id})
                      </option>
                    ))}
                  </Select>
                </div>
                <div>
                  <Label value="Assign Role (optional)" />
                  <Select
                    value={inviteRoleId ?? ''}
                    onChange={e => setInviteRoleId(e.target.value || undefined)}
                    disabled={!inviteClinicId || inviteLoadingRoles}
                  >
                    <option value="">Admin (full access)</option>
                    {availableInviteRoles.map(r => (
                      <option key={r.id} value={r.id}>
                        {r.name}
                      </option>
                    ))}
                  </Select>
                </div>
                <div>
                  <Label value="Location Access (optional)" />
                  <div className="max-h-48 overflow-y-auto border rounded p-2 space-y-1">
                    {(inviteClinicId ? (clinicIdToLocations.get(inviteClinicId) ?? []) : []).map(
                      l => (
                        <label key={l.id} className="flex items-center space-x-2 text-sm">
                          <Checkbox
                            checked={inviteLocationIds.includes(l.id)}
                            onChange={() =>
                              setInviteLocationIds(prev =>
                                prev.includes(l.id)
                                  ? prev.filter(x => x !== l.id)
                                  : [...prev, l.id],
                              )
                            }
                          />
                          <span>{l.name}</span>
                        </label>
                      ),
                    )}
                    {inviteClinicId &&
                      (clinicIdToLocations.get(inviteClinicId) ?? []).length === 0 && (
                        <div className="text-xs text-gray-500">No locations in this clinic</div>
                      )}
                    {!inviteClinicId && (
                      <div className="text-xs text-gray-500">
                        Select a clinic to choose locations
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button color="gray" onClick={() => setShowInviteModal(false)} disabled={loading}>
                Cancel
              </Button>
              <Button
                color="blue"
                onClick={async () => {
                  try {
                    setLoading(true);
                    setError(null);
                    const res = await fetch('/api/staff/generate-invite-code', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${getToken()}`,
                      },
                      body: JSON.stringify({
                        clinicId: inviteClinicId,
                        email: inviteEmail || undefined,
                        assignedRoleId: inviteRoleId,
                        assignedLocationIds: inviteLocationIds,
                      }),
                    });
                    const data = await res.json();
                    if (!res.ok) throw new Error(data?.message || 'Failed to generate invite code');
                    setMessage(`Invite code created${data.code ? `: ${data.code}` : ''}`);
                    setShowInviteModal(false);
                    setInviteEmail('');
                    setInviteClinicId(null);
                    setInviteRoleId(undefined);
                    setInviteLocationIds([]);
                  } catch (e) {
                    setError(e instanceof Error ? e.message : String(e));
                  } finally {
                    setLoading(false);
                  }
                }}
                disabled={loading || inviteClinicId == null}
              >
                {loading ? 'Sending…' : 'Generate Invite'}
              </Button>
            </Modal.Footer>
          </Modal>

          <div className="mt-8 text-sm text-gray-500">
            Actions are audit logged and restricted to SUPER_ADMIN. Ensure you understand the impact
            of platform-level operations.
          </div>
        </div>
      </DashboardLayout>
    </>
  );
}
