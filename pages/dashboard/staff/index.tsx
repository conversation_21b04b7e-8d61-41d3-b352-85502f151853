import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Al<PERSON> } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { useRouter } from 'next/router';
import { UserRole } from '@/models/auth';

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  status: 'active' | 'pending';
  clinicId: number | null;
  rbacRoles?: string[];
}

interface CurrentUser {
  id: string;
  name: string;
  role: UserRole;
  clinicId: number | null;
}

const StaffManagement = () => {
  const [loading, setLoading] = useState(true);
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [error, setError] = useState<string>('');
  const router = useRouter();

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const response = await fetch('/api/staff/me');
        const data = await response.json();
        setCurrentUser(data);
      } catch (error) {
        console.error('Error fetching current user:', error);
      }
    };

    const fetchStaff = async () => {
      try {
        // API will handle filtering based on user role
        const response = await fetch('/api/staff');
        const data = await response.json();

        if (data.success) {
          setStaff(data.staff || []);
        }
      } catch (error) {
        console.error('Error fetching staff:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentUser();
    fetchStaff();
  }, []);

  const isSuperAdmin = currentUser?.role === UserRole.SUPER_ADMIN;

  return (
    <DashboardLayout>
      <div className="px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <CustomButton
            onClick={() => router.push('/dashboard/staff/invite')}
            disabled={
              !(
                currentUser?.role === UserRole.CLINIC_ADMIN ||
                currentUser?.role === UserRole.SUPER_ADMIN
              )
            }
            title={
              currentUser?.role === UserRole.CLINIC_ADMIN ||
              currentUser?.role === UserRole.SUPER_ADMIN
                ? ''
                : 'Only clinic admins or super admins can invite staff members'
            }
          >
            Invite Staff Member
          </CustomButton>
        </div>

        {loading ? (
          <div className="flex justify-center my-12">
            <Spinner size="xl" />
          </div>
        ) : (
          <div className="overflow-x-auto">
            {error && (
              <Alert color="failure" className="mb-3">
                {error}
              </Alert>
            )}
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Name</Table.HeadCell>
                <Table.HeadCell>Email</Table.HeadCell>
                <Table.HeadCell>RBAC Roles</Table.HeadCell>
                {isSuperAdmin && <Table.HeadCell>Clinic ID</Table.HeadCell>}
                <Table.HeadCell className="text-right">Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {staff.length === 0 ? (
                  <Table.Row>
                    <Table.Cell colSpan={isSuperAdmin ? 6 : 5} className="text-center py-10">
                      No staff members found. Invite your first team member to get started.
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  staff.map(member => (
                    <Table.Row key={member.id} className="bg-white">
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900">
                        {member.name}
                      </Table.Cell>
                      <Table.Cell>{member.email}</Table.Cell>
                      <Table.Cell>
                        {member.rbacRoles && member.rbacRoles.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {member.rbacRoles.map(r => {
                              const color =
                                r === 'Practice Manager'
                                  ? 'success'
                                  : r === 'Doctor'
                                    ? 'info'
                                    : r === 'ReadOnly'
                                      ? 'warning'
                                      : r === 'Account Owner'
                                        ? 'warning'
                                        : 'gray';
                              return (
                                <Badge
                                  key={r}
                                  color={color as 'success' | 'info' | 'warning' | 'gray'}
                                >
                                  {r}
                                </Badge>
                              );
                            })}
                          </div>
                        ) : (
                          <span className="text-xs text-gray-500">None</span>
                        )}
                      </Table.Cell>
                      {isSuperAdmin && <Table.Cell>{member.clinicId}</Table.Cell>}
                      <Table.Cell className="text-right">
                        <div className="flex gap-2 justify-end">
                          <CustomButton
                            size="xs"
                            onClick={() => router.push(`/dashboard/staff/${member.id}`)}
                          >
                            View
                          </CustomButton>
                          {(currentUser?.role === UserRole.CLINIC_ADMIN ||
                            currentUser?.role === UserRole.SUPER_ADMIN) && (
                            <Button
                              color="failure"
                              size="xs"
                              onClick={() => {
                                setSelectedUserId(member.id);
                                setConfirmOpen(true);
                              }}
                            >
                              Delete
                            </Button>
                          )}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))
                )}
              </Table.Body>
            </Table>
          </div>
        )}
      </div>
      {/* Delete confirmation modal */}
      <Modal show={confirmOpen} size="md" onClose={() => !deleting && setConfirmOpen(false)}>
        <Modal.Header>Confirm Deletion</Modal.Header>
        <Modal.Body>
          <div className="space-y-2">
            <p>Are you sure you want to delete this staff user? This action cannot be undone.</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setConfirmOpen(false)} disabled={deleting}>
            Cancel
          </Button>
          <Button
            color="failure"
            onClick={async () => {
              if (!selectedUserId) return;
              setDeleting(true);
              setError('');
              try {
                const resp = await fetch(`/api/staff/${selectedUserId}`, { method: 'DELETE' });
                if (!resp.ok) {
                  const data = await resp.json().catch(() => ({}));
                  throw new Error(data.message || 'Failed to delete user');
                }
                setStaff(prev => prev.filter(s => s.id !== selectedUserId));
                setConfirmOpen(false);
              } catch (e) {
                setError(e instanceof Error ? e.message : 'Failed to delete user');
              } finally {
                setDeleting(false);
                setSelectedUserId(null);
              }
            }}
            disabled={deleting}
          >
            {deleting ? 'Deleting...' : 'Delete'}
          </Button>
        </Modal.Footer>
      </Modal>
    </DashboardLayout>
  );
};

export default StaffManagement;
