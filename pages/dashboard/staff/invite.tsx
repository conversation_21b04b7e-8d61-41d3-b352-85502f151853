import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import DashboardLayout from '@/components/DashboardLayout';
import {
  <PERSON><PERSON>,
  Card,
  Spinner,
  Alert,
  Table,
  Badge,
  Label,
  TextInput,
  Select,
  Checkbox,
} from 'flowbite-react';
import { HiArrowLeft, HiClipboardCopy, HiCheck, HiExclamation, HiRefresh } from 'react-icons/hi';
import { UserRole } from '@/models/auth';
import { getToken } from '@/utils/auth';

interface InviteCode {
  id: string;
  code: string;
  clinicId: number;
  used: boolean;
  expiresAt: string;
  createdAt: string;
  usedAt?: string;
}

interface CurrentUser {
  id: string;
  name: string;
  email: string;
  role?: UserRole;
  clinicId?: number | null;
}

const InviteStaffPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [currentUser, setCurrentUser] = useState<CurrentUser>({ id: '', name: '', email: '' });
  const [inviteCode, setInviteCode] = useState('');
  const [email, setEmail] = useState('');
  const [availableRoles, setAvailableRoles] = useState<Array<{ id: string; name: string }>>([]);
  const [assignedRoleId, setAssignedRoleId] = useState<string>('');
  const [availableLocations, setAvailableLocations] = useState<
    Array<{ id: string; name: string; clinicId?: number }>
  >([]);
  const [selectedLocationIds, setSelectedLocationIds] = useState<string[]>([]);
  const [existingCodes, setExistingCodes] = useState<InviteCode[]>([]);
  const [error, setError] = useState('');
  const [copied, setCopied] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [clinics, setClinics] = useState<Array<{ id: string; name: string }>>([]);
  const [selectedClinicId, setSelectedClinicId] = useState<number | null>(null);
  const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

  const fetchExistingCodes = useCallback(async () => {
    const clinicIdToUse = isSuperAdmin ? selectedClinicId : currentUser.clinicId;
    if (!clinicIdToUse) {
      return;
    }

    try {
      setRefreshing(true);
      console.log('Fetching invite codes for clinic ID:', currentUser.clinicId);

      // Get auth token
      const token = getToken();
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Call the API endpoint
      const response = await fetch(`/api/staff/invite-codes?clinicId=${clinicIdToUse}`, {
        headers,
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch invite codes: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('API response:', data);

      if (data.success) {
        setExistingCodes(data.inviteCodes || []);
        console.log('Invite codes loaded:', data.inviteCodes.length);
      } else {
        throw new Error(data.message || 'Failed to fetch invite codes');
      }
    } catch (error) {
      console.error('Error fetching invite codes:', error);
      setError('Error loading invite codes. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, [currentUser.clinicId, isSuperAdmin, selectedClinicId]);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = getToken();
        const headers: HeadersInit = {};

        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch('/api/staff/me', { headers });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch current user: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();
        console.log('Current user data:', data);

        setCurrentUser(data);

        // For super admin, load clinics list
        if (data.role === UserRole.SUPER_ADMIN) {
          const clinicsResp = await fetch('/api/clinics', { headers });
          if (clinicsResp.ok) {
            const clinicsData = await clinicsResp.json();
            setClinics(
              clinicsData.map((c: { id: string; name: string }) => ({ id: c.id, name: c.name })),
            );
          }
        }

        setSelectedClinicId(data.clinicId ?? null);

        if (data.clinicId) {
          fetchExistingCodes();
          const rolesResp = await fetch(`/api/roles?clinicId=${data.clinicId}`, {
            headers,
          });
          if (rolesResp.ok) {
            const roles = await rolesResp.json();
            setAvailableRoles(
              (roles || [])
                .filter((r: { clinic_id: number | null }) => r.clinic_id !== null)
                .map((r: { id: string; name: string }) => ({ id: r.id, name: r.name })),
            );
          }
          const locResp = await fetch(`/api/locations?clinicId=${data.clinicId}`, { headers });
          if (locResp.ok) {
            const locs = await locResp.json();
            const items = (locs.locations || locs || []).map((l: { id: string; name: string }) => ({
              id: l.id,
              name: l.name,
            }));
            setAvailableLocations(items);
          }
        }
      } catch (error) {
        console.error('Error fetching current user:', error);
        setError('Could not load your user information');
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentUser();
  }, [fetchExistingCodes]);

  // Super admin: when selectedClinicId changes, load locations and roles for that clinic
  useEffect(() => {
    const loadForSelectedClinic = async () => {
      if (currentUser.role !== UserRole.SUPER_ADMIN) return;
      if (!selectedClinicId) {
        setAvailableLocations([]);
        setAvailableRoles([]);
        return;
      }
      try {
        const token = getToken();
        const headers: HeadersInit = {};
        if (token) headers['Authorization'] = `Bearer ${token}`;

        const rolesResp = await fetch(`/api/roles?clinicId=${selectedClinicId}`, { headers });
        if (rolesResp.ok) {
          const roles = await rolesResp.json();
          setAvailableRoles(
            (roles || [])
              .filter((r: { clinic_id: number | null }) => r.clinic_id !== null)
              .map((r: { id: string; name: string }) => ({ id: r.id, name: r.name })),
          );
        } else {
          setAvailableRoles([]);
        }

        const allLocsResp = await fetch('/api/platform/locations/all', { headers });
        if (allLocsResp.ok) {
          const allLocs = await allLocsResp.json();
          const items = (allLocs || [])
            .filter((l: { clinicId: number }) => Number(l.clinicId) === Number(selectedClinicId))
            .map((l: { id: string; name: string }) => ({ id: l.id, name: l.name }));
          setAvailableLocations(items);
        } else {
          setAvailableLocations([]);
        }

        await fetchExistingCodes();
      } catch {
        setAvailableRoles([]);
        setAvailableLocations([]);
      }
    };
    void loadForSelectedClinic();
  }, [currentUser.role, selectedClinicId, fetchExistingCodes]);

  const handleGoBack = () => {
    router.push('/dashboard/staff');
  };

  const handleGenerateCode = async () => {
    if (!currentUser.clinicId) {
      setError('No clinic ID found for your account');
      return;
    }
    if (!email) {
      setError('Recipient email is required');
      return;
    }

    try {
      setGenerating(true);
      setError('');

      // Get authentication token
      const token = getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      console.log('Generating invite code for clinic:', currentUser.clinicId);

      // Call API endpoint to generate code
      const response = await fetch('/api/staff/generate-invite-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          clinicId: currentUser.clinicId,
          email,
          assignedRoleId: assignedRoleId || undefined,
          assignedLocationIds: selectedLocationIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error response:', errorData);
        throw new Error(
          `Failed to generate invite code: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();
      console.log('API success response:', data);

      if (data.success) {
        setInviteCode(data.code);

        // Refresh the list of codes
        await fetchExistingCodes();
      } else {
        throw new Error(data.message || 'Failed to generate invite code');
      }
    } catch (error) {
      console.error('Error generating invite code:', error);
      setError('Failed to generate invite code. Please try again.');
    } finally {
      setGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      },
      err => {
        console.error('Could not copy text: ', err);
      },
    );
  };

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Check if a code is expired
  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  // Note: If needed in the future, we can compute count of valid codes here

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-full">
          <Spinner size="xl" />
        </div>
      </DashboardLayout>
    );
  }

  // Check if user is a clinic admin
  if (!(currentUser.role === UserRole.CLINIC_ADMIN || currentUser.role === UserRole.SUPER_ADMIN)) {
    return (
      <DashboardLayout>
        <div className="p-4">
          <Button color="light" onClick={handleGoBack} className="mb-4">
            <HiArrowLeft className="mr-2 h-5 w-5" />
            Back to Staff List
          </Button>
          <Card>
            <div className="text-center py-10">
              <HiExclamation className="mx-auto mb-4 h-10 w-10 text-yellow-500" />
              <h5 className="text-lg font-bold text-gray-800 mb-2">Access Denied</h5>
              <p className="text-gray-700">Only admins can generate staff invite codes.</p>
              <Button color="primary" onClick={handleGoBack} className="mt-4">
                Return to Staff List
              </Button>
            </div>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-4">
        <Button color="light" onClick={handleGoBack} className="mb-4">
          <HiArrowLeft className="mr-2 h-5 w-5" />
          Back to Staff List
        </Button>

        <div className="max-w-4xl mx-auto">
          <Card className="mb-6">
            <h5 className="text-xl font-bold text-gray-900 mb-4">Invite Teammate</h5>

            <div className="space-y-4">
              {isSuperAdmin && (
                <div>
                  <Label value="Target Clinic" />
                  <Select
                    value={selectedClinicId?.toString() ?? ''}
                    onChange={e => {
                      const v = e.target.value;
                      setSelectedClinicId(v ? Number(v) : null);
                      setAssignedRoleId('');
                      setSelectedLocationIds([]);
                    }}
                  >
                    <option value="">Select a clinic</option>
                    {clinics.map(c => (
                      <option key={c.id} value={c.id}>
                        {c.name} (ID: {c.id})
                      </option>
                    ))}
                  </Select>
                </div>
              )}
              <div>
                <Label htmlFor="invite-email" value="Teammate's Email Address" />
                <TextInput
                  id="invite-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  required
                />
              </div>

              <div>
                <Label value="Teammate permissions" />
                <div className="mt-2 space-y-3">
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="perm"
                        checked={!assignedRoleId}
                        onChange={() => setAssignedRoleId('')}
                      />
                      <div>
                        <div className="font-medium">Invite as Admin</div>
                        <div className="text-sm text-gray-500">
                          Admins will have full access including managing teammates and new
                          features.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="perm"
                        checked={!!assignedRoleId}
                        onChange={() => setAssignedRoleId(availableRoles[0]?.id || '')}
                      />
                      <div className="w-full">
                        <div className="font-medium mb-1">Invite with Restricted Access</div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div>
                            <Label value="Select Role" />
                            <Select
                              value={assignedRoleId}
                              onChange={e => setAssignedRoleId(e.target.value)}
                            >
                              <option value="">Select a role</option>
                              {availableRoles.map(r => (
                                <option key={r.id} value={r.id}>
                                  {r.name}
                                </option>
                              ))}
                            </Select>
                          </div>
                          <div>
                            <Label value="Location Access" />
                            <div className="max-h-40 overflow-y-auto border rounded p-2 space-y-1">
                              {availableLocations.map(l => (
                                <label key={l.id} className="flex items-center space-x-2 text-sm">
                                  <Checkbox
                                    checked={selectedLocationIds.includes(l.id)}
                                    onChange={() =>
                                      setSelectedLocationIds(prev =>
                                        prev.includes(l.id)
                                          ? prev.filter(x => x !== l.id)
                                          : [...prev, l.id],
                                      )
                                    }
                                  />
                                  <span>{l.name}</span>
                                </label>
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          You can further refine permissions after the teammate accepts the invite.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {error && (
              <Alert color="failure" className="mb-4">
                <div className="font-medium">Error</div>
                {error}
              </Alert>
            )}

            {!inviteCode ? (
              <div className="flex justify-center">
                <Button color="blue" onClick={handleGenerateCode} disabled={generating}>
                  {generating ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Generating...
                    </>
                  ) : (
                    'Generate Invite Code'
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="p-4 bg-gray-100 rounded-lg">
                  <p className="text-sm text-gray-700 mb-1">Invite Code:</p>
                  <div className="flex items-center">
                    <code className="bg-gray-200 px-3 py-1 rounded text-lg font-mono flex-grow">
                      {inviteCode}
                    </code>
                    <Button
                      color="light"
                      size="sm"
                      onClick={() => copyToClipboard(inviteCode)}
                      className="ml-2"
                    >
                      {copied ? (
                        <HiCheck className="h-5 w-5" />
                      ) : (
                        <HiClipboardCopy className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-100">
                  <p className="text-gray-700">
                    <strong>Note:</strong> This code will expire in 24 hours and can only be used
                    once.
                  </p>
                </div>

                <div className="flex justify-end mt-4">
                  <Button color="blue" onClick={handleGenerateCode}>
                    Generate Another Code
                  </Button>
                </div>
              </div>
            )}
          </Card>

          {/* Existing invite codes list */}
          <Card>
            <div className="flex justify-between items-center mb-4">
              <h5 className="text-xl font-bold text-gray-900">Existing Invite Codes</h5>
              <div className="flex space-x-2">
                <Button
                  color="light"
                  size="sm"
                  onClick={() => fetchExistingCodes()}
                  disabled={refreshing}
                >
                  {refreshing ? <Spinner size="sm" /> : <HiRefresh className="h-5 w-5" />}
                </Button>
              </div>
            </div>

            {existingCodes.length === 0 ? (
              <div className="text-center py-6 text-gray-500">
                No invite codes found. Generate your first code above.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table striped>
                  <Table.Head>
                    <Table.HeadCell>Code</Table.HeadCell>
                    <Table.HeadCell>Status</Table.HeadCell>
                    <Table.HeadCell>Created At</Table.HeadCell>
                    <Table.HeadCell>Expires At</Table.HeadCell>
                    <Table.HeadCell>Actions</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {existingCodes.map(code => {
                      const expired = isExpired(code.expiresAt);
                      let status = 'Active';
                      let statusColor = 'success';

                      if (code.used) {
                        status = 'Used';
                        statusColor = 'gray';
                      } else if (expired) {
                        status = 'Expired';
                        statusColor = 'failure';
                      }

                      return (
                        <Table.Row key={code.id} className="bg-white">
                          <Table.Cell className="font-medium">{code.code}</Table.Cell>
                          <Table.Cell>
                            <Badge color={statusColor as 'success' | 'gray' | 'failure'}>
                              {status}
                            </Badge>
                          </Table.Cell>
                          <Table.Cell>{formatDate(code.createdAt)}</Table.Cell>
                          <Table.Cell>{formatDate(code.expiresAt)}</Table.Cell>
                          <Table.Cell>
                            {!code.used && !expired && (
                              <Button size="xs" onClick={() => copyToClipboard(code.code)}>
                                Copy
                              </Button>
                            )}
                          </Table.Cell>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              </div>
            )}
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default InviteStaffPage;
