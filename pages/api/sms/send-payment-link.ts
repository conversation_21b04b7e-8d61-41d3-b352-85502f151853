import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  BadRequestError,
  NotFoundError,
} from '@/lib/external-api/v2';
import { z } from 'zod';
import { smsService } from '@/lib/services/sms-service';
import { getClinicBySessionId } from '@/lib/external-api/v2/utils/clinic-utils';
import logger from '@/lib/external-api/v2/utils/logger';

/**
 * @swagger
 * /api/sms/send-payment-link:
 *   post:
 *     summary: Send payment link SMS for a clinic
 *     description: Sends an SMS with a payment link for a specific clinic based on the session ID. The clinic information and payment link are retrieved using the session ID, and the payment link SMS is sent to the specified phone number.
 *     tags: [SMS]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *               - phoneNumber
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: Session ID to identify the clinic
 *               phoneNumber:
 *                 type: string
 *                 description: Phone number to send the payment link SMS to
 *     responses:
 *       200:
 *         description: Payment link SMS sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Payment link SMS sent successfully"
 *                 sessionId:
 *                   type: string
 *                   example: "12345678-1234-1234-1234-123456789012"
 *                 clinicName:
 *                   type: string
 *                   example: "University Retina Clinic"
 *                 messageSid:
 *                   type: string
 *                   description: Twilio message SID
 *                   example: "SM12345678901234567890123456789012"
 *       400:
 *         description: Bad request - invalid input or session not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - API key is missing or invalid
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

// Schema for validating the request body
const sendPaymentLinkSchema = z.object({
  sessionId: z.string().nonempty('Session ID is required'),
  phoneNumber: z.string().nonempty('Phone number is required'),
});

/**
 * Handler for POST /api/sms/send-payment-link
 * Sends an SMS with a payment link for a clinic
 */
async function sendPaymentLinkHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Validate request body
    const validationResult = sendPaymentLinkSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const { sessionId, phoneNumber } = validationResult.data;

    // Get clinic information using session ID
    const clinic = await getClinicBySessionId(sessionId);
    if (!clinic) {
      throw new NotFoundError(`Clinic not found for session ID ${sessionId}`);
    }

    // Check if clinic has a payment link configured
    if (!clinic.paymentLink) {
      throw new Error(`Clinic ${clinic.name} does not have a payment link configured`);
    }

    logger.info(
      { sessionId, phoneNumber, clinicId: clinic.id, clinicName: clinic.name },
      `Sending payment link SMS for clinic ${clinic.name}`,
    );

    // Send payment link SMS using clinic's configured payment link
    const messageSid = await smsService.sendPaymentLink(
      phoneNumber,
      clinic.name,
      clinic.paymentLink,
    );

    res.status(200).json({
      success: true,
      message: 'Payment link SMS sent successfully',
      sessionId,
      clinicName: clinic.name,
      messageSid,
    });
  } catch (error) {
    logger.error({ error }, 'Error sending payment link SMS');
    throw error;
  }
}

/**
 * Main handler for /api/sms/send-payment-link
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // POST request to send payment link
  if (req.method === 'POST') {
    await sendPaymentLinkHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
