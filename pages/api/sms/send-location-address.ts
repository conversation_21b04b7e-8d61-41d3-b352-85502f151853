import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  BadRequestError,
  NotFoundError,
} from '@/lib/external-api/v2';
import { z } from 'zod';
import { smsService } from '@/lib/services/sms-service';
import { getLocationBySessionId } from '@/lib/external-api/v2/utils/clinic-utils';
import logger from '@/lib/external-api/v2/utils/logger';
import { ensureDbInitialized } from '@/lib/middleware/db-init';
import { LocationsRepository } from '@/lib/repositories/locations-repository';
import { Location } from '../../../models/Location';

/**
 * @swagger
 * /api/sms/send-location-address:
 *   post:
 *     summary: Send location address SMS for a location
 *     description: Sends an SMS with the address for a specific location based on the session ID or location ID. The location information is retrieved using the session ID or location ID, and the location address SMS is sent to the specified phone number.
 *     tags: [SMS]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *               - phoneNumber
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: Session ID to identify the location
 *               locationId:
 *                 type: string
 *                 description: Location ID to identify the location
 *               phoneNumber:
 *                 type: string
 *                 description: Phone number to send the location address SMS to
 *     responses:
 *       200:
 *         description: Location address SMS sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Location address SMS sent successfully"
 *                 sessionId:
 *                   type: string
 *                   example: "12345678-1234-1234-1234-123456789012"
 *                 locationName:
 *                   type: string
 *                   example: "University Retina Clinic"
 *                 address:
 *                   type: string
 *                   example: "123 Main St, New York, NY 10001"
 *                 messageSid:
 *                   type: string
 *                   description: Twilio message SID
 *                   example: "SM12345678901234567890123456789012"
 *       400:
 *         description: Bad request - invalid input or session not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - API key is missing or invalid
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

// Schema for validating the request body
const sendLocationAddressSchema = z.object({
  sessionId: z.string().nonempty('Session ID is required'),
  locationId: z.string().optional(),
  phoneNumber: z.string().nonempty('Phone number is required'),
});

/**
 * Handler for POST /api/sms/send-location-address
 * Sends an SMS with the address for a location
 */
async function sendLocationAddressHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // Validate request body
    const validationResult = sendLocationAddressSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const { sessionId, locationId, phoneNumber } = validationResult.data;

    let location: Location | null = null;

    if (locationId) {
      await ensureDbInitialized();
      const locationsRepository = new LocationsRepository();
      location = await locationsRepository.findById(locationId);
      if (!location) {
        throw new NotFoundError(`Location not found for ID ${locationId}`);
      }
    } else {
      location = await getLocationBySessionId(sessionId);
      if (!location) {
        throw new NotFoundError(`Location not found for session ID ${sessionId}`);
      }
    }

    logger.info(
      { sessionId, phoneNumber, locationId: location.id, locationName: location.name },
      `Sending location address SMS for location ${location.name}`,
    );

    // Send location address SMS using location's address
    const messageSid = await smsService.sendLocationAddress(
      phoneNumber,
      location.name,
      location.address,
    );

    res.status(200).json({
      success: true,
      message: 'Location address SMS sent successfully',
      sessionId,
      locationName: location.name,
      address: location.address,
      messageSid,
    });
  } catch (error) {
    logger.error({ error }, 'Error sending location address SMS');
    throw error;
  }
}

/**
 * Main handler for /api/sms/send-location-address
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // POST request to send location address
  if (req.method === 'POST') {
    await sendLocationAddressHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
