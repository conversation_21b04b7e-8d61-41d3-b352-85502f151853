import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import logger from '@/lib/external-api/v2/utils/logger';
import { afterHoursCallsLogService, afterHoursCallsService } from '@/utils/firestore';
import { AppLinkBuilder } from '@/utils/app-link.builder';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { RepositoryManager } from '@/lib/repositories';

// Validation schema for after-hours call view request
const viewAfterHoursCallSchema = z.object({
  id: z.string().nonempty('After-hours call ID is required'),
  doctorId: z.string().nonempty('Doctor ID is required'),
});

/**
 * Handler for GET /api/calls/after-hours/view/[id]
 * Requires query parameter: d (ID of the doctor who is viewing the call)
 * Retrieves an after-hours call record by ID and marks it as reviewed by doctor
 * ONLY after successful authentication and verification
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET method
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' });
    }

    // Get after-hours call ID from the URL parameter and doctor ID from query param 'd'
    const { id, d: doctorId } = req.query;

    const validationResult = viewAfterHoursCallSchema.safeParse({ id, doctorId });
    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const afterHoursCallId = validationResult.data.id;
    const expectedDoctorId = validationResult.data.doctorId;

    // Get the after-hours call to verify it exists and get details
    const afterHoursCall = await afterHoursCallsService.getAfterHoursCallById(afterHoursCallId);
    if (!afterHoursCall) {
      return res
        .status(404)
        .json({ message: `After-hours call with ID ${afterHoursCallId} not found` });
    }

    // CRITICAL: Verify user authentication BEFORE setting the review flag
    const authenticatedUser = await verifyAuthAndGetUser(req);

    if (!authenticatedUser) {
      // User is not authenticated - redirect to login with return URL
      const appLinkBuilder = AppLinkBuilder.getInstance();
      const currentUrl = appLinkBuilder.getAfterHoursCallViewLink(
        afterHoursCallId,
        expectedDoctorId,
      );
      const loginUrl = `/login?returnUrl=${encodeURIComponent(currentUrl)}`;

      logger.info(
        {
          afterHoursCallId,
          expectedDoctorId,
          context: 'after-hours-call-view',
        },
        'Unauthenticated access attempt - redirecting to login',
      );

      return res.redirect(302, loginUrl);
    }

    // Get the MySQL user ID for the authenticated user (Firebase UID -> MySQL ID)
    let authenticatedMysqlUserId: string;
    try {
      const repositoryManager = RepositoryManager.getInstance();
      await repositoryManager.initialize();
      const mysqlUser = await repositoryManager.users.findByFirebaseUid(authenticatedUser.id);

      if (!mysqlUser) {
        logger.error(
          {
            firebaseUid: authenticatedUser.id,
            context: 'after-hours-call-view',
          },
          'MySQL user not found for authenticated Firebase UID - authorization will fail',
        );
        return res.status(403).json({
          message: 'Unauthorized - User not found in system',
        });
      }
      authenticatedMysqlUserId = mysqlUser.id;
    } catch (error) {
      logger.error(
        {
          firebaseUid: authenticatedUser.id,
          error: error instanceof Error ? error.message : String(error),
          context: 'after-hours-call-view',
        },
        'Error finding MySQL user for authenticated Firebase UID',
      );
      return res.status(500).json({ message: 'Internal server error during authorization' });
    }

    // Verify the authenticated user is the expected doctor
    // Check if the authenticated user matches either the primary doctor or backup doctor
    const isAuthorizedDoctor =
      authenticatedMysqlUserId === expectedDoctorId ||
      authenticatedMysqlUserId === afterHoursCall.doctorId ||
      authenticatedMysqlUserId === afterHoursCall.backupDoctorId;

    if (!isAuthorizedDoctor) {
      logger.warn(
        {
          authenticatedFirebaseUid: authenticatedUser.id,
          authenticatedMysqlUserId,
          expectedDoctorId,
          afterHoursCallId,
          primaryDoctorId: afterHoursCall.doctorId,
          backupDoctorId: afterHoursCall.backupDoctorId,
          context: 'after-hours-call-view',
        },
        'Unauthorized doctor access attempt',
      );

      return res.status(403).json({
        message: 'Unauthorized - You are not the assigned doctor for this call',
      });
    }

    // NOW we can safely mark the call as reviewed since doctor is authenticated and authorized
    await afterHoursCallsService.updateAfterHoursCall(afterHoursCall.id, {
      isReviewedByDoctor: true,
    });

    // Use the MySQL user ID we already retrieved for logging
    const mysqlUserId = authenticatedMysqlUserId;

    // Log the review action with the correct MySQL user ID
    try {
      await afterHoursCallsLogService.createAfterHoursCallLog({
        afterHoursCallId,
        viewedBy: mysqlUserId,
      });
    } catch (error) {
      logger.error(
        {
          afterHoursCallId,
          firebaseUid: authenticatedUser.id,
          mysqlUserId,
          error: error instanceof Error ? error.message : String(error),
          context: 'after-hours-call-view',
        },
        'Failed to create after-hours call log - continuing anyway',
      );
      // Don't fail the whole operation if logging fails
    }

    logger.info(
      {
        afterHoursCallId,
        firebaseUid: authenticatedUser.id,
        mysqlUserId,
        doctorName: authenticatedUser.name,
        context: 'after-hours-call-view',
      },
      'After-hours call successfully marked as reviewed by authenticated doctor',
    );

    const appLinkBuilder = AppLinkBuilder.getInstance();
    const callDetailsLink = appLinkBuilder.getCallDetailsLink(afterHoursCall.callId);

    // Redirect to the call details page
    return res.redirect(302, callDetailsLink);
  } catch (error) {
    logger.error(
      {
        error: error instanceof Error ? error.message : String(error),
        callId: req.query.id,
        context: 'after-hours-call-view',
      },
      'Error viewing after-hours call',
    );
    return res.status(500).json({ message: 'Internal server error' });
  }
}
