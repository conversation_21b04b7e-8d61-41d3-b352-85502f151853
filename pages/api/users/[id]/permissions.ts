import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';
import { ENABLE_NEW_RBAC } from '@/app-config';
import { FeatureKey, PermissionLevel } from '@/models/auth';
import { UserPermissionsRepository } from '@/lib/repositories/user-permissions-repository';
import { PermissionService } from '@/lib/services/permission-service';
import { z } from 'zod';
import { withAuthUser } from '@/lib/middleware/auth-user';
import { withPermission } from '@/lib/middleware/permission-middleware';

const userPermRepo = new UserPermissionsRepository();
const permSvc = new PermissionService();

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!ENABLE_NEW_RBAC) return res.status(404).json({ message: 'Not found' });
  const { id } = req.query as { id: string };
  try {
    if (req.method === 'GET') {
      // Return effective matrix for all features (global only for now)
      const entries = await Promise.all(
        (Object.values(FeatureKey) as FeatureKey[]).map(async feature => ({
          feature,
          level: await permSvc.resolveEffectiveLevel(id, feature, undefined),
        })),
      );
      return res.status(200).json({ userId: id, effective: entries });
    }
    if (req.method === 'PUT') {
      const BodySchema = z.object({
        overrides: z.array(
          z.object({
            feature: z.nativeEnum(FeatureKey),
            level: z.nativeEnum(PermissionLevel),
            locationId: z.string().nullable().optional(),
            expiresAt: z.string().nullable().optional(),
          }),
        ),
      });
      const { overrides } = BodySchema.parse(req.body);
      for (const ov of overrides) {
        await userPermRepo.upsertOverride({
          userId: id,
          feature: ov.feature,
          level: ov.level,
          locationId: ov.locationId ?? null,
          expiresAt: ov.expiresAt ? new Date(ov.expiresAt) : null,
        });
      }
      permSvc.invalidateUser(id);
      return res.status(204).end();
    }
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (e) {
    console.error(e);
    return res.status(500).json({ message: 'Internal Server Error' });
  }
}

export default withAuthUser(
  withPermission(FeatureKey.STAFF_MANAGEMENT, PermissionLevel.ADMIN, { locationFrom: 'query' })(
    handler as NextApiHandler,
  ),
);
