import type { NextApiRequest, NextApiResponse } from 'next';
import { ENABLE_NEW_RBAC } from '@/app-config';
import { withAuthUser } from '@/lib/middleware/auth-user';
import { withPermission } from '@/lib/middleware/permission-middleware';
import { FeatureKey, PermissionLevel } from '@/models/auth';
import { UserPermissionsRepository } from '@/lib/repositories/user-permissions-repository';
import { z } from 'zod';

const repo = new UserPermissionsRepository();

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!ENABLE_NEW_RBAC) return res.status(404).json({ message: 'Not found' });
  const { id } = req.query as { id: string };

  try {
    if (req.method === 'GET') {
      const roles = await repo.listUserRoleIds(id, null);
      return res.status(200).json({ userId: id, roleIds: roles });
    }

    if (req.method === 'PUT') {
      const BodySchema = z.object({
        roleIds: z.array(z.string().min(1)).max(20),
        locationId: z.string().nullable().optional(),
      });
      const { roleIds, locationId } = BodySchema.parse(req.body);
      await repo.replaceUserRoles(id, roleIds, locationId ?? null);
      return res.status(204).end();
    }

    return res.status(405).json({ message: 'Method not allowed' });
  } catch (e) {
    console.error(e);
    return res.status(500).json({ message: 'Internal Server Error' });
  }
}

export default withAuthUser(
  withPermission(FeatureKey.STAFF_MANAGEMENT, PermissionLevel.ADMIN, { locationFrom: 'query' })(
    handler,
  ),
);
