import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';
import { ENABLE_NEW_RBAC } from '@/app-config';
import { RolesRepository } from '@/lib/repositories/roles-repository';
import { PermissionsRepository } from '@/lib/repositories/permissions-repository';
import { AuditRepository } from '@/lib/repositories/audit-repository';
import { FeatureKey, PermissionLevel } from '@/models/auth';
import { z } from 'zod';
import { withAuthUser } from '@/lib/middleware/auth-user';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { withPermission } from '@/lib/middleware/permission-middleware';

const rolesRepo = new RolesRepository();
const permsRepo = new PermissionsRepository();
const auditRepo = new AuditRepository();

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!ENABLE_NEW_RBAC) return res.status(404).json({ message: 'Not found' });
  const { id } = req.query as { id: string };
  const user = await verifyAuthAndGetUser(req);
  if (!user) return res.status(401).json({ message: 'Unauthorized' });
  const actorUserId = user.id;

  try {
    if (req.method === 'GET') {
      const role = await rolesRepo.findById(id);
      if (!role) return res.status(404).json({ message: 'Not found' });
      return res.status(200).json(role);
    }
    if (req.method === 'PUT') {
      const BodySchema = z.object({
        name: z.string().min(2).max(100).optional(),
        description: z.string().max(255).optional(),
        is_template: z.boolean().optional(),
        permissions: z
          .array(
            z.object({
              feature: z.nativeEnum(FeatureKey),
              level: z.nativeEnum(PermissionLevel),
            }),
          )
          .optional(),
      });
      const { name, description, permissions, is_template } = BodySchema.parse(req.body);
      const updated = await rolesRepo.update(id, { name, description, is_template });
      if (!updated) return res.status(404).json({ message: 'Not found' });
      if (permissions) await permsRepo.upsertRolePermissions(id, permissions);
      await auditRepo.insert({
        actor_user_id: actorUserId,
        action: 'UPDATE_ROLE',
        subject_id: id,
        payload: { name, description, is_template, permissions },
      });
      return res.status(200).json(updated);
    }
    if (req.method === 'DELETE') {
      await rolesRepo.delete(id);
      await auditRepo.insert({ actor_user_id: actorUserId, action: 'DELETE_ROLE', subject_id: id });
      return res.status(204).end();
    }
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (e) {
    console.error(e);
    return res.status(500).json({ message: 'Internal Server Error' });
  }
}

export default withAuthUser(
  withPermission(FeatureKey.STAFF_MANAGEMENT, PermissionLevel.WRITE, { locationFrom: 'query' })(
    handler as NextApiHandler,
  ),
);
