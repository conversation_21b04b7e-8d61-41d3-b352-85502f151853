import { randomUUID } from 'crypto';

import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';
import { ENABLE_NEW_RBAC } from '@/app-config';
import { RolesRepository } from '@/lib/repositories/roles-repository';
import { PermissionsRepository } from '@/lib/repositories/permissions-repository';
import { FeatureKey, PermissionLevel } from '@/models/auth';
import { AuditRepository } from '@/lib/repositories/audit-repository';
import { z } from 'zod';
import { withAuthUser } from '@/lib/middleware/auth-user';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { withPermission } from '@/lib/middleware/permission-middleware';

const rolesRepo = new RolesRepository();
const permsRepo = new PermissionsRepository();
const auditRepo = new AuditRepository();

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!ENABLE_NEW_RBAC) return res.status(404).json({ message: 'Not found' });

  const user = await verifyAuthAndGetUser(req);
  if (!user) return res.status(401).json({ message: 'Unauthorized' });
  const actorUserId: string = user.id;
  const clinicId: number | null = user.clinicId ?? null;

  try {
    if (req.method === 'GET') {
      const items = await rolesRepo.findByClinic(clinicId);
      return res.status(200).json(items);
    }

    if (req.method === 'POST') {
      const BodySchema = z.object({
        name: z.string().min(2).max(100),
        description: z.string().max(255).optional(),
        permissions: z
          .array(
            z.object({
              feature: z.nativeEnum(FeatureKey),
              level: z.nativeEnum(PermissionLevel),
            }),
          )
          .min(1),
      });
      const { name, description, permissions } = BodySchema.parse(req.body);

      const id = randomUUID();
      const created = await rolesRepo.create({
        id,
        clinic_id: clinicId,
        name,
        description: description ?? null,
        is_system: false,
        is_template: false,
        created_by: actorUserId,
      });
      await permsRepo.upsertRolePermissions(created.id, permissions);
      await auditRepo.insert({
        actor_user_id: actorUserId,
        action: 'CREATE_ROLE',
        subject_id: created.id,
        payload: { name, description, permissions },
      });
      return res.status(201).json(created);
    }

    return res.status(405).json({ message: 'Method not allowed' });
  } catch (e) {
    console.error(e);
    return res.status(500).json({ message: 'Internal Server Error' });
  }
}

export default withAuthUser(
  withPermission(FeatureKey.STAFF_MANAGEMENT, PermissionLevel.WRITE, { locationFrom: 'query' })(
    handler as NextApiHandler,
  ),
);
