import type { NextApiRequest, NextApiResponse } from 'next';
import { mysqlService } from '@/lib/database/mysql-service';
import { ENABLE_NEW_RBAC } from '@/app-config';

export default async function handler(_req: NextApiRequest, res: NextApiResponse) {
  if (!ENABLE_NEW_RBAC) return res.status(404).json({ message: 'Not found' });
  try {
    const rows = await mysqlService.query(
      `SELECT r.*, GROUP_CONCAT(CONCAT(rp.feature, ':', rp.level) ORDER BY rp.feature SEPARATOR ',') as grants
       FROM roles r LEFT JOIN role_permissions rp ON rp.role_id = r.id
       WHERE r.clinic_id IS NULL AND r.is_template = TRUE
       GROUP BY r.id
       ORDER BY r.name ASC`,
      [],
    );
    res.status(200).json(rows);
  } catch (e) {
    console.error(e);
    res.status(500).json({ message: 'Internal Server Error' });
  }
}
