import { randomUUID } from 'crypto';

import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';
import { ENABLE_NEW_RBAC } from '@/app-config';
import { RolesRepository } from '@/lib/repositories/roles-repository';
import { PermissionsRepository } from '@/lib/repositories/permissions-repository';
import { AuditRepository } from '@/lib/repositories/audit-repository';
import { z } from 'zod';
import { withAuthUser } from '@/lib/middleware/auth-user';
import { withPermission } from '@/lib/middleware/permission-middleware';
import { FeatureKey, PermissionLevel } from '@/models/auth';

const rolesRepo = new RolesRepository();
const permsRepo = new PermissionsRepository();
const auditRepo = new AuditRepository();

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!ENABLE_NEW_RBAC) return res.status(404).json({ message: 'Not found' });
  if (req.method !== 'POST') return res.status(405).json({ message: 'Method not allowed' });
  try {
    type ReqWithUser = NextApiRequest & { user?: { id: string } };
    const user = (req as ReqWithUser).user;
    if (!user?.id) return res.status(401).json({ message: 'Unauthorized' });
    const BodySchema = z.object({
      sourceRoleId: z.string().min(1),
      name: z.string().min(2).max(100),
      clinicId: z.number().nullable().optional(),
    });
    const { sourceRoleId, name, clinicId } = BodySchema.parse(req.body);
    const source = await rolesRepo.findById(sourceRoleId);
    if (!source) return res.status(404).json({ message: 'Source role not found' });
    const id = randomUUID();
    const created = await rolesRepo.create({
      id,
      clinic_id: clinicId ?? source.clinic_id ?? null,
      name,
      description: source.description,
      is_system: false,
      is_template: false,
      created_by: user.id,
    });
    // Copy permissions
    const grants = (await permsRepo.getRolePermissions([sourceRoleId])).map(g => ({
      feature: g.feature,
      level: g.level,
    }));
    await permsRepo.upsertRolePermissions(created.id, grants);
    await auditRepo.insert({
      actor_user_id: user.id,
      action: 'CLONE_ROLE',
      subject_id: created.id,
      payload: { sourceRoleId, name },
    });
    return res.status(201).json(created);
  } catch (e) {
    console.error(e);
    return res.status(500).json({ message: 'Internal Server Error' });
  }
}

export default withAuthUser(
  withPermission(FeatureKey.STAFF_MANAGEMENT, PermissionLevel.WRITE, { locationFrom: 'query' })(
    handler as NextApiHandler,
  ),
);
