import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';
import { withPermission } from '@/lib/middleware/permission-middleware';
import { FeatureKey, PermissionLevel } from '@/models/auth';

async function handler(_req: NextApiRequest, res: NextApiResponse) {
  res.status(200).json({ ok: true });
}

export default withPermission(FeatureKey.DASHBOARD, PermissionLevel.READ, {
  locationFrom: 'query',
})(handler as NextApiHandler);
