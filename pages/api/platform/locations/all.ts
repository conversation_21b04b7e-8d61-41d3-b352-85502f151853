import { NextApiRequest, NextApiResponse } from 'next';

import admin from '@/utils/firebase-admin';
import { PermissionService } from '@/lib/services/permission-service';
import { FeatureKey, PermissionLevel, User, UserRole } from '@/models/auth';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

type LocationRow = {
  id: string;
  clinicId: number;
  practiceId: string;
  name: string;
  address: string;
  phone?: string | null;
  practiceName?: string;
  isActive?: boolean;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' });
    }

    // Authenticate
    const user = (await verifyAuthAndGetUser(req)) as User | null;
    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // SUPER_ADMIN allowed without additional DB checks
    if (user.role !== UserRole.SUPER_ADMIN) {
      const svc = new PermissionService();
      const level = await svc.resolveEffectiveLevel(user.id, FeatureKey.PLATFORM_ADMIN);
      if (level !== PermissionLevel.ADMIN) {
        return res.status(403).json({ message: 'Forbidden' });
      }
    }

    const db = admin.firestore();
    const snap = await db.collection('locations').orderBy('name').get();
    const rows: LocationRow[] = snap.docs.map(d => {
      const data = d.data() as Record<string, unknown>;
      return {
        id: d.id,
        clinicId: Number(data.clinicId ?? 0),
        practiceId: String(data.practiceId ?? ''),
        name: String(data.name ?? ''),
        address: String(data.address ?? ''),
        phone: (data.phone as string | null) ?? null,
        practiceName: (data.practiceName as string | undefined) || undefined,
        isActive: (data.isActive as boolean | undefined) ?? true,
      };
    });

    return res.status(200).json(rows);
  } catch (error) {
    console.error('Error listing all locations:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
