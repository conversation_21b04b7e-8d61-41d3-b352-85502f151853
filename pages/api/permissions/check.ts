import type { NextApiRequest, NextApiResponse } from 'next';
import { ENABLE_NEW_RBAC } from '@/app-config';
import { FeatureKey } from '@/models/auth';
import { PermissionService } from '@/lib/services/permission-service';

const svc = new PermissionService();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!ENABLE_NEW_RBAC) return res.status(404).json({ message: 'Not found' });
  if (req.method !== 'POST') return res.status(405).json({ message: 'Method not allowed' });
  try {
    const { userId, feature, locationId } = req.body as {
      userId: string;
      feature: FeatureKey;
      locationId?: string;
    };
    if (!userId || !feature) return res.status(400).json({ message: 'Missing userId or feature' });
    const level = await svc.resolveEffectiveLevel(userId, feature, locationId);
    return res.status(200).json({ level });
  } catch (e) {
    console.error(e);
    res.status(500).json({ message: 'Internal Server Error' as const });
  }
}
