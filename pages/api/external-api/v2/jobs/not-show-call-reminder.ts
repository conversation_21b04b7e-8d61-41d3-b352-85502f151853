import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

import logger from '@/lib/external-api/v2/utils/logger';
import {
  AppointmentStatus,
  createApiHand<PERSON>,
  ensureProvidersInitialized,
  getProviderFromRequest,
} from '@/lib/external-api/v2';
import { callService } from '@/lib/services/call-service';
import { Scheduler } from '@/utils/scheduler';
import { noShowService } from '@/utils/firestore';
import { createJobName } from '@/models/NoShowJob';
import { NOT_SHOW_REMINDER_CALL_RECORDING_ENABLED } from '@/app-config';

const notShowCallReminderBodySchema = z.object({
  phoneNumber: z.string().nonempty('Phone number is required'),
  patientId: z.string().nonempty('Patient ID is required'),
  externalAppointmentId: z.string().nonempty('External appointment ID is required'),
  dryRun: z.boolean().optional(),
  redirectPhoneNumber: z.string().optional(),
});

const deleteJob = async (req: NextApiRequest) => {
  const scheduler = Scheduler.getInstance();
  const jobName = scheduler.getJobNameFromRequest(req);
  if (jobName) {
    await scheduler.deleteJob(jobName);
  } else {
    logger.warn(
      { context: `NotShowCallReminderJob.handler` },
      'Can not delete job, no job name found in request headers',
    );
  }
};

const saveCallId = async (callSid: string, jobName: string) => {
  try {
    logger.info(
      {
        context: `NotShowCallReminderJob.handler`,
        jobName,
        callSid,
      },
      'Saving call ID',
    );

    await noShowService.updateJob(jobName, {
      callSid,
    });

    logger.info(
      {
        context: `NotShowCallReminderJob.handler`,
        jobName,
        callSid,
      },
      'Call ID saved',
    );
  } catch (error) {
    logger.error(
      {
        context: `NotShowCallReminderJob.handler`,
        jobName,
        callSid,
        innerError: error instanceof Error ? error.message : String(error),
      },
      'Error saving call ID',
    );
  }
};

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  logger.info(
    {
      context: `NotShowCallReminderJob.handler`,
    },
    'Starting "Not show" call reminder job',
  );

  const validationResult = notShowCallReminderBodySchema.safeParse(req.body);
  if (!validationResult.success) {
    const errors = validationResult.error.flatten().fieldErrors;
    logger.error(
      {
        context: `NotShowCallReminderJob.handler`,
        errors,
      },
      'Invalid request body',
    );
    return res.status(400).json({ message: `Invalid request body`, errors });
  }

  const { phoneNumber, dryRun, redirectPhoneNumber, patientId, externalAppointmentId } =
    validationResult.data;

  try {
    const provider = getProviderFromRequest(req);
    const [patient, appointment] = await Promise.all([
      provider.getPatientService().getPatientByIdWithoutInsurance(patientId),
      provider.getAppointmentService().getAppointmentById(externalAppointmentId),
    ]);

    if (!appointment) {
      logger.error(
        {
          context: `NotShowCallReminderJob.handler`,
          externalAppointmentId,
        },
        'Appointment not found',
      );
      return res.status(400).json({ message: `Appointment not found` });
    }
    if (!patient) {
      logger.error(
        {
          context: `NotShowCallReminderJob.handler`,
          patientId,
        },
        'Patient not found',
      );
      return res.status(400).json({ message: `Patient not found` });
    }

    const location = await provider.getLocationService().getLocationById(appointment.locationId);
    if (!location) {
      logger.error(
        {
          context: `NotShowCallReminderJob.handler`,
          locationId: appointment!.locationId,
        },
        'Location not found',
      );
      return res.status(400).json({ message: `Location not found` });
    }

    const pendingAppointments = await provider
      .getAppointmentService()
      .getAppointmentsByStatus(AppointmentStatus.PENDING, {
        locationId: location.providerInfo.externalId,
        patientId,
      });

    if (pendingAppointments.items.length > 0) {
      logger.info(
        {
          context: `NotShowCallReminderJob.handler`,
          locationId: location.providerInfo.externalId,
          patientId,
          pendingAppointments,
        },
        'The appointment has been rescheduled, skipping the call',
      );
      res.status(200).json({ message: 'Call skipped, appointment has been rescheduled' });
      return;
    }

    // Remove the timezone from the start time 2025-05-20T14:30:00-04:00 => 2025-05-20T14:30:00
    const noTzTime = appointment.startTime.replace(/-\d{2}:\d{2}$/, '');
    const appointmentDay = dayjs(noTzTime).format('MMMM DD');
    const appointmentTime = dayjs(noTzTime).format('hh:mm a');

    const callSid = await callService.makeCall({
      isRecordingEnabled: NOT_SHOW_REMINDER_CALL_RECORDING_ENABLED,
      event: 'no-show',
      toPhoneNumber: phoneNumber,
      dryRun,
      redirectPhoneNumber,
      callContext: {
        patientId: patientId,
        patientFirstName: patient.firstName,
        patientLastName: patient.lastName,
        patientDateOfBirth: patient.dateOfBirth,
        patientPhoneNumber: phoneNumber,
        patientEmail: patient.email,
        patientGender: patient.gender,
        patientAddress: JSON.stringify(patient.address),
        locationId: location.id,
        noShowLocationName: location.name,
        noShowAppointmentId: externalAppointmentId,
        noShowAppointmentTime: appointmentTime,
        noShowAppointmentDay: appointmentDay,
        isAfterHours: 'false',
        isNoShowEvent: 'true',
        is_new_patient: 'false',
        patient_request_type: 'book_new',
      },
    });

    await saveCallId(callSid, createJobName(externalAppointmentId));
    logger.info(
      {
        context: `NotShowCallReminderJob.handler`,
        callSid,
      },
      '"Not show" call reminder job completed',
    );

    res.status(200).json({ message: 'OK', callSid });
  } catch (error) {
    logger.error(
      {
        context: `NotShowCallReminderJob.handler`,
        innerError: error,
      },
      'Error making a call',
    );
    return res.status(500).json({ message: 'Internal server error' });
  } finally {
    deleteJob(req);
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
