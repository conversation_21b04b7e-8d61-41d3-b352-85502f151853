import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

import { create<PERSON>pi<PERSON>and<PERSON>, ensureProvidersInitialized } from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';
import { LLMClient } from '@/utils/llm/llm.client';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import { getDialogflowConversation, extractTranscriptFromConversation } from '@/lib/dialogflow';
import { DialogflowAuthService } from '@/lib/dialogflow/auth';
import { formatDialogflowDuration } from '@/utils/call-duration-utils';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { Scheduler } from '@/utils/scheduler';
import { AppLinkBuilder } from '@/utils/app-link.builder';
import { CallType } from '@/models/CallTypes';
import { OfficeHoursService } from '@/lib/services/office-hours';
import { LocationService } from '@/lib/services/locationService';

const callClassificationUpdateJobBodySchema = z.object({
  callId: z.string(),
  dryRun: z.boolean().optional(),
});

/**
 * Schedule a call classification update job to run 20 minutes after call creation
 * @param params.callId - The ID of the call to classify
 * @param params.executeInNextMinutes - The number of minutes to execute the job in (default: 20)
 * @returns The name of the scheduled job
 */
export const scheduleCallClassificationUpdate = async ({
  callId,
  executeInNextMinutes = 20,
}: {
  callId: string;
  executeInNextMinutes?: number;
}): Promise<{ jobName: string }> => {
  const linkBuilder = AppLinkBuilder.getInstance();
  const scheduler = Scheduler.getInstance();
  const jobName = `call-classification-update-${callId}`;
  const executionTime = dayjs.utc().add(executeInNextMinutes, 'minutes').toISOString();

  logger.info(
    {
      context: `CallClassificationUpdateJob.scheduleCallClassificationUpdate`,
      jobName,
      executionTime,
      callId,
    },
    'Scheduling call classification update job',
  );

  try {
    scheduler.createJob({
      name: jobName,
      executionTime,
      httpTarget: {
        uri: linkBuilder.getCallClassificationUpdateEndpoint(),
        jsonPayload: {
          callId,
        },
      },
    });
  } catch (schedulingError) {
    logger.error(
      {
        context: `CallClassificationUpdateJob.scheduleCallClassificationUpdate`,
        jobName,
        executionTime,
        callId,
        error: schedulingError instanceof Error ? schedulingError.message : String(schedulingError),
      },
      'Failed to schedule call classification update job',
    );
    throw schedulingError;
  }

  logger.info(
    {
      context: `CallClassificationUpdateJob.scheduleCallClassificationUpdate`,
      jobName,
      executionTime,
      callId,
    },
    'Call classification update job scheduled',
  );

  return { jobName };
};

const deleteJob = async (req: NextApiRequest): Promise<void> => {
  const scheduler = Scheduler.getInstance();
  const jobName = scheduler.getJobNameFromRequest(req);
  if (jobName) {
    await scheduler.deleteJob(jobName);
  } else {
    logger.warn(
      { context: `CallClassificationUpdateJob.handler` },
      'Cannot delete job, no job name found in request headers',
    );
  }
};

/**
 * Handler for the call classification update job
 * Updates empty call records with LLM-classified types and fresh GCP conversation data
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  res.setTimeout(900000); // 15 minutes timeout
  res.setHeader('Content-Type', 'application/json');

  // Handle health check/preflight requests
  if (req.method === 'GET') {
    res.status(200).json({
      status: 'ok',
      endpoint: 'call-classification-update',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Only accept POST requests for actual job execution
  if (req.method !== 'POST') {
    res.status(405).json({ error: `Method ${req.method} not allowed` });
    return;
  }

  logger.info(
    {
      context: `CallClassificationUpdateJob.handler`,
      method: req.method,
      headers: req.headers,
    },
    'Starting call classification update job',
  );

  try {
    const validationResult = callClassificationUpdateJobBodySchema.safeParse(req.body);
    if (!validationResult.success) {
      const errors = validationResult.error.flatten().fieldErrors;
      logger.error(
        {
          context: `CallClassificationUpdateJob.handler`,
          errors,
        },
        'Invalid request body',
      );
      res.status(400).json({ message: `Invalid request body`, errors });
      return;
    }

    const { callId, dryRun = false } = validationResult.data;

    logger.info(
      {
        context: `CallClassificationUpdateJob.handler`,
        callId,
        dryRun,
      },
      `Processing call classification update for call ${callId}`,
    );

    // 1. Get the call from the database
    const call = await callsService.getCallById(callId);
    if (!call) {
      logger.error(
        {
          context: `CallClassificationUpdateJob.handler`,
          callId,
        },
        'Call not found',
      );
      res.status(404).json({ message: 'Call not found' });
      return;
    }

    logger.info(
      {
        context: `CallClassificationUpdateJob.handler`,
        callId,
        currentType: call.type,
        currentCallTypes: call.callTypes,
        sessionId: call.sessionId,
        hasTranscription: !!call.transcription,
      },
      `Found call record for classification update ${callId}`,
    );

    // 2. Fetch fresh conversation data from GCP to get duration and transcription
    let transcriptionData = {
      transcription: call.transcription || '',
      transcriptionWithAudio: call.transcriptionWithAudio || '',
      duration: call.duration || '0 min',
    };

    if (call.sessionId) {
      try {
        logger.info(
          {
            context: `CallClassificationUpdateJob.handler`,
            callId,
            sessionId: call.sessionId,
          },
          `Fetching fresh conversation data from GCP for call ${callId}`,
        );

        // Get GCP API credentials
        const projectId = process.env.GCP_PROJECT_ID;
        const locationId = process.env.GCP_LOCATION_ID || 'global';
        const agentId = process.env.GCP_AGENT_ID;

        if (!projectId || !agentId) {
          throw new Error('Missing GCP configuration (GCP_PROJECT_ID or GCP_AGENT_ID)');
        }

        // Get access token for Dialogflow API
        const accessToken = await DialogflowAuthService.getAccessToken();
        if (!accessToken) {
          throw new Error('Failed to obtain access token for Dialogflow API');
        }

        // Fetch conversation data directly from Dialogflow API
        const conversation = await getDialogflowConversation({
          projectId,
          locationId,
          agentId,
          sessionId: call.sessionId,
          accessToken,
        });

        // Extract transcript from conversation
        const dialogflowTranscript = extractTranscriptFromConversation(conversation);

        // Extract duration directly from conversation object
        let dialogflowDuration = '0 min';
        if (conversation.duration) {
          dialogflowDuration = formatDialogflowDuration(conversation.duration);
          logger.info(
            {
              context: `CallClassificationUpdateJob.handler`,
              callId,
              duration: conversation.duration,
              formatted: dialogflowDuration,
            },
            'Extracted duration from DialogflowConversation',
          );
        }

        // Get transcription with audio using GCP storage service (minimized for better performance)
        let transcriptionWithAudio = '';
        try {
          const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;
          if (bucketName) {
            const storageService = getGcpStorageService();
            // Use minimized version to only get text and recordUrl, avoiding memory overflow
            const interactions = await storageService.getTranscriptWithAudioRecords(
              bucketName,
              call.sessionId,
              undefined,
              true, // minimized = true for better performance and memory usage
            );
            if (interactions && interactions.length > 0) {
              transcriptionWithAudio = JSON.stringify(interactions);
            }
          }
        } catch (audioError) {
          logger.warn(
            {
              context: `CallClassificationUpdateJob.handler`,
              callId,
              sessionId: call.sessionId,
              error: audioError instanceof Error ? audioError.message : String(audioError),
            },
            'Failed to get transcription with audio (continuing with text-only transcription)',
          );
        }

        // Update transcription data with fresh GCP data
        transcriptionData = {
          transcription: dialogflowTranscript,
          transcriptionWithAudio,
          duration: dialogflowDuration,
        };

        logger.info(
          {
            context: `CallClassificationUpdateJob.handler`,
            callId,
            hasTranscription: !!transcriptionData.transcription,
            hasAudio: !!transcriptionData.transcriptionWithAudio,
            duration: dialogflowDuration,
          },
          'Successfully fetched fresh data from GCP DialogflowConversation',
        );
      } catch (error) {
        logger.warn(
          {
            context: `CallClassificationUpdateJob.handler`,
            callId,
            sessionId: call.sessionId,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to fetch fresh conversation data, using existing data',
        );
      }
    } else {
      logger.info(
        {
          context: `CallClassificationUpdateJob.handler`,
          callId,
        },
        'No sessionId available, cannot fetch GCP conversation data',
      );
    }

    // 3. Prepare call object with fresh transcription data for LLM
    const callForClassification = {
      ...call,
      transcription: transcriptionData.transcription,
      transcriptionWithAudio: transcriptionData.transcriptionWithAudio,
      duration: transcriptionData.duration,
    };

    // 4. Use LLM to classify call types
    const llmClient = LLMClient.getInstance();

    // Check if call has AFTER_HOURS type that should be preserved or if it should be detected
    const preserveTypes: CallType[] = [];

    // Check existing AFTER_HOURS type
    if (
      callForClassification.callTypes &&
      callForClassification.callTypes.includes(CallType.AFTER_HOURS)
    ) {
      preserveTypes.push(CallType.AFTER_HOURS);
    }
    // Auto-detect AFTER_HOURS based on call time and office hours
    else if (
      callForClassification.date &&
      callForClassification.locationId &&
      callForClassification.clinicId
    ) {
      try {
        logger.info(
          {
            callId: callForClassification.id,
            callDate: callForClassification.date,
            locationId: callForClassification.locationId,
            clinicId: callForClassification.clinicId,
          },
          'Attempting after-hours detection for call (job)',
        );

        const location = await LocationService.getLocationById(
          callForClassification.locationId.toString(),
          callForClassification.clinicId,
        );

        logger.info(
          {
            callId: callForClassification.id,
            locationId: location?.id,
            hasOfficeHours: !!location?.officeHours,
            hasTimeZone: !!location?.timeZone,
            timeZone: location?.timeZone,
            officeHours: location?.officeHours,
          },
          'Location data retrieved for after-hours detection (job)',
        );

        if (location?.officeHours && location?.timeZone) {
          const officeHoursStatus = OfficeHoursService.checkOfficeHours(
            location.officeHours,
            location.timeZone,
            new Date(callForClassification.date),
          );

          logger.info(
            {
              callId: callForClassification.id,
              callDate: callForClassification.date,
              isOpen: officeHoursStatus.isOpen,
              currentStatus: officeHoursStatus.currentStatus,
              timeZone: location.timeZone,
            },
            'Office hours status check completed (job)',
          );

          if (!officeHoursStatus.isOpen) {
            preserveTypes.push(CallType.AFTER_HOURS);
            logger.info(
              {
                callId: callForClassification.id,
                callDate: callForClassification.date,
                officeStatus: officeHoursStatus.currentStatus,
              },
              'Auto-detected AFTER_HOURS call based on call time and office hours (job)',
            );
          }
        } else {
          logger.warn(
            {
              callId: callForClassification.id,
              locationId: location?.id,
              hasOfficeHours: !!location?.officeHours,
              hasTimeZone: !!location?.timeZone,
            },
            'Missing office hours or timezone configuration for after-hours detection (job)',
          );
        }
      } catch (error) {
        logger.error(
          {
            callId: callForClassification.id,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to check office hours for after-hours detection (job)',
        );
      }
    } else {
      logger.warn(
        {
          callId: callForClassification.id,
          hasDate: !!callForClassification.date,
          hasLocationId: !!callForClassification.locationId,
          hasClinicId: !!callForClassification.clinicId,
        },
        'Missing call date, location ID, or clinic ID for after-hours detection (job)',
      );
    }

    const classifiedTypes = await llmClient.classifyCallTypes(callForClassification, preserveTypes);

    if (!classifiedTypes || classifiedTypes.length === 0) {
      logger.warn(
        {
          context: `CallClassificationUpdateJob.handler`,
          callId,
        },
        `LLM classification failed or returned no types for call ${callId}`,
      );

      // If LLM classification fails, we can still update with fresh transcription data
      if (!dryRun) {
        await callsService.updateCall(callId, {
          transcription: transcriptionData.transcription,
          transcriptionWithAudio: transcriptionData.transcriptionWithAudio,
          duration: transcriptionData.duration,
        });

        logger.info(
          {
            context: `CallClassificationUpdateJob.handler`,
            callId,
          },
          'Updated call with fresh transcription data only (no classification)',
        );
      }

      res.status(200).json({
        message: 'Call updated with transcription data, but classification failed',
        callId,
        dryRun,
        transcriptionUpdated: true,
        classificationFailed: true,
      });
      return;
    }

    logger.info(
      {
        context: `CallClassificationUpdateJob.handler`,
        callId,
        classifiedTypes,
      },
      `LLM classified call with ${classifiedTypes.length} types for call ${callId}`,
    );

    // 5. Determine primary type (first in array) and build callTypes array
    const primaryType = classifiedTypes[0];
    let updatedCallTypes = [primaryType];

    // Merge additional types if there are multiple
    if (classifiedTypes.length > 1) {
      for (let i = 1; i < classifiedTypes.length; i++) {
        updatedCallTypes = mergeCallTypes(updatedCallTypes, classifiedTypes[i]);
      }
    }

    // 6. Update the call in the database (unless dry run)
    const updateData = {
      type: primaryType,
      callTypes: updatedCallTypes,
      transcription: transcriptionData.transcription,
      transcriptionWithAudio: transcriptionData.transcriptionWithAudio,
      duration: transcriptionData.duration,
    };

    if (!dryRun) {
      await callsService.updateCall(callId, updateData);

      logger.info(
        {
          context: `CallClassificationUpdateJob.handler`,
          callId,
          oldType: call.type,
          newType: primaryType,
          oldCallTypes: call.callTypes,
          newCallTypes: updatedCallTypes,
          oldDuration: call.duration,
          newDuration: transcriptionData.duration,
        },
        `Successfully updated call with classification and fresh data from GCP for call ${callId}`,
      );
    } else {
      logger.info(
        {
          context: `CallClassificationUpdateJob.handler`,
          callId,
          dryRun: true,
        },
        'Dry run mode - would have updated call with classification data',
      );
    }

    res.status(200).json({
      message: 'Call classification update completed successfully',
      callId,
      dryRun,
      classification: {
        classifiedTypes,
        primaryType,
        finalCallTypes: updatedCallTypes,
      },
      changes: {
        type: {
          old: call.type,
          new: primaryType,
        },
        callTypes: {
          old: call.callTypes,
          new: updatedCallTypes,
        },
        duration: {
          old: call.duration,
          new: transcriptionData.duration,
        },
      },
    });
    return;
  } catch (error) {
    logger.error(
      {
        context: `CallClassificationUpdateJob.handler`,
        error: error instanceof Error ? error.message : String(error),
      },
      'Error in call classification update job',
    );

    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return;
  } finally {
    // Always attempt to clean up the job, but don't let it block the response
    try {
      await deleteJob(req);
    } catch (cleanupError) {
      logger.warn(
        {
          context: `CallClassificationUpdateJob.handler`,
          error: cleanupError instanceof Error ? cleanupError.message : String(cleanupError),
        },
        'Failed to clean up scheduled job, but continuing',
      );
    }
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
