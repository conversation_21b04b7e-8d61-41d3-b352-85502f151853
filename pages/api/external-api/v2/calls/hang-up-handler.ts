import { z } from 'zod';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

import { createApiHandler, ensureProvidersInitialized } from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callSessionsService } from '@/utils/firestore';
import { callService } from '@/lib/services/call-service';
import { AppLinkBuilder } from '@/utils/app-link.builder';

const inboundHangUpCallSchema = z.object({
  Called: z.string().optional(),
  ToState: z.string().optional(),
  VirtualAgentStatus: z.string().optional(),
  CallerCountry: z.string().optional(),
  Direction: z.enum(['inbound', 'outbound-api']).optional(),
  CallerState: z.string().optional(),
  ToZip: z.string().optional(),
  VirtualAgentProviderData: z.string().optional(),
  CallSid: z.string().optional(),
  To: z.string().optional(),
  CallerZip: z.string().optional(),
  ToCountry: z.string().optional(),
  CallStatus: z.string().optional(),
  From: z.string().optional(),
  AccountSid: z.string().optional(),
  CalledCountry: z.string().optional(),
  CallerCity: z.string().optional(),
  ToCity: z.string().optional(),
  FromCountry: z.string().optional(),
  Caller: z.string().optional(),
  FromCity: z.string().optional(),
  CalledState: z.string().optional(),
  FromZip: z.string().optional(),
  VirtualAgentProvider: z.string().optional(),
  FromState: z.string().optional(),
});

type TransferToStaffParameters = {
  type: 'transfer-to-staff';
  phoneNumber: string;
};

type AgentPauseParametersType = TransferToStaffParameters;

type VirtualAgentProviderData = {
  ConversationId: string;
  EndUserId: string;
  FeatureUsage: Array<{
    feature: string;
    quantity: number;
  }>;
  PauseParameters?: AgentPauseParametersType;
  IntentConfidence: number;
  IntentId: string;
  IntentDisplayName: string;
};

const callEndConversation = (params: {
  sessionId: string;
  callerPhone: string | undefined;
  callSid: string | undefined;
}): void => {
  const { sessionId, callerPhone, callSid } = params;
  const endpoint = AppLinkBuilder.getInstance().getEndConversationNoAuthEndpoint();
  const body = {
    sessionInfo: {
      session: `/sessions/${sessionId}`,
      parameters: {
        'telephony-caller-id': callerPhone,
        providerCallId: callSid,
      },
    },
  };

  logger.info(
    {
      context: `InboundHangUpCall.handler`,
      sessionId,
      endpoint,
      body,
    },
    'Calling end conversation endpoint',
  );

  // Don't wait for the response
  axios
    .post(endpoint, body)
    .then(response => {
      logger.info(
        {
          context: `InboundHangUpCall.handler`,
          sessionId,
          responseStatus: response.status,
        },
        'Call end conversation response',
      );
    })
    .catch(innerError => {
      logger.error(
        {
          context: `InboundHangUpCall.handler`,
          sessionId,
          innerError,
        },
        'Error calling end conversation endpoint',
      );
    });
};

const tryToEndConversation = async (params: {
  ConversationId: string;
  Caller: string | undefined;
  CallSid: string | undefined;
  VirtualAgentProviderData: string;
}): Promise<void> => {
  const { ConversationId, Caller, CallSid, VirtualAgentProviderData } = params;
  if (!ConversationId || ConversationId.trim() === '') {
    logger.error(
      {
        context: `InboundHangUpCall.handler`,
        VirtualAgentProviderData,
      },
      'ConversationId not found in VirtualAgentProviderData',
    );
    return;
  }

  if (!ConversationId.includes('/conversations/')) {
    logger.error(
      {
        context: `InboundHangUpCall.handler`,
        ConversationId,
      },
      'Invalid ConversationId format',
    );
    return;
  }

  // ConversationId = "projects/frontdesk-454309/locations/global/conversations/REZDWP2gHocONBy8qtSdmtW8fb-g6Ml82k1ifcMBy8Y9htza"
  const sessionId = ConversationId.split('/conversations/')[1];
  const callSession = await callSessionsService.getCallSessionBySessionId(sessionId);
  if (!callSession) {
    logger.error(
      {
        context: `InboundHangUpCall.handler`,
        sessionId,
      },
      'Call session not found',
    );
    return;
  }

  if (callSession.status === 'completed') {
    logger.info(
      {
        context: `InboundHangUpCall.handler`,
        sessionId,
      },
      'Call session already completed',
    );
    return;
  }

  callEndConversation({
    sessionId,
    callSid: CallSid,
    callerPhone: Caller,
  });
};

const processRequest = async (body: unknown): Promise<{ xml: string } | undefined> => {
  const validationResult = inboundHangUpCallSchema.safeParse(body);
  if (!validationResult.success) {
    const errors = validationResult.error.flatten().fieldErrors;
    logger.error(
      {
        context: `InboundHangUpCall.handler`,
        errors,
      },
      'Invalid request body',
    );
    return;
  }

  const { VirtualAgentProviderData, Caller, CallSid } = validationResult.data;

  if (!VirtualAgentProviderData) {
    logger.info(
      {
        context: `InboundHangUpCall.handler`,
        CallSid,
      },
      'VirtualAgentProviderData not found',
    );
    return;
  }

  const { ConversationId, PauseParameters } = JSON.parse(
    VirtualAgentProviderData,
  ) as VirtualAgentProviderData;

  try {
    await tryToEndConversation({
      ConversationId,
      Caller,
      CallSid,
      VirtualAgentProviderData,
    });
  } catch (innerError) {
    logger.error(
      {
        context: `InboundHangUpCall.handler`,
        innerError,
      },
      'Error ending conversation',
    );
  } finally {
    // If we have a PauseParameters, we need to transfer the call to the staff.
    // Override hang-up response with a dial rule to transfer the call to the staff.
    if (PauseParameters?.type === 'transfer-to-staff' && PauseParameters.phoneNumber) {
      logger.info(
        {
          context: `InboundHangUpCall.handler`,
          phoneNumber: PauseParameters.phoneNumber,
        },
        'Transferring call to staff',
      );

      const xml = callService
        .createDialNumberRule({
          phoneNumber: PauseParameters.phoneNumber,
        })
        .toString();

      return { xml };
    }
  }
};

/**
 * Twilio 'hang-up' webhook handler. It's called by Twilio when a call is hung up either by the caller or the agent.
 * The webhook returns an XML response with TwiML instructions on how to end the call.
 */
async function httpHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const sendXmlResponse = (xml: string, params?: { status?: number }) => {
    const { status = 200 } = params || {};
    res.setHeader('Content-Type', 'text/xml').status(status).send(xml);
  };

  const hangUpResponse = () => {
    const twiML = callService.createEndCallRule().toString();
    sendXmlResponse(twiML);
  };

  try {
    const response = await processRequest(req.body);
    if (response?.xml) {
      sendXmlResponse(response?.xml);
    } else {
      hangUpResponse();
    }
  } catch (innerError) {
    logger.error(
      {
        context: `InboundHangUpCall.handler`,
        innerError,
      },
      'Error processing request',
    );
    hangUpResponse();
  }
}

// Export with API key validation middleware
export default createApiHandler(httpHandler, {
  middleware: [ensureProvidersInitialized],
});
