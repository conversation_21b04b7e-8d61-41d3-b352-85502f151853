import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { updateAppointmentSchema } from '@/lib/external-api/v2/validators';
import { callSessionsService } from '@/utils/firestore';
import { CallType } from '@/models/CallTypes';
import logger from '../../../../../../lib/external-api/v2/utils/logger';
import { eventEmitter, EVENTS } from '@/lib/external-api/v2/utils/events';
import { ensureDbInitialized } from '@/lib/middleware/db-init';
import { LocationsRepository } from '@/lib/repositories/locations-repository';

/**
 * @swagger
 * /api/external-api/v2/appointments/{id}/change:
 *   parameters:
 *     - in: path
 *       name: id
 *       required: true
 *       description: ID of the appointment to change
 *       schema:
 *         type: string
 *   post:
 *     summary: Change appointment data
 *     description: |
 *       Changes appointment data by creating a new appointment with updated fields and canceling the original.
 *       This is a workaround for the Nextech API limitation where you can only update the status of an appointment.
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               patientId:
 *                 type: string
 *                 description: Patient ID
 *               practitionerId:
 *                 type: string
 *                 description: Practitioner ID
 *               locationId:
 *                 type: string
 *                 description: Location ID
 *               clinicId:
 *                 type: string
 *                 description: Clinic ID
 *               startTime:
 *                 type: string
 *                 format: date-time
 *                 description: Start time in ISO format (YYYY-MM-DDTHH:MM:SS)
 *               endTime:
 *                 type: string
 *                 format: date-time
 *                 description: End time in ISO format (YYYY-MM-DDTHH:MM:SS)
 *               type:
 *                 type: string
 *                 description: Appointment type ID
 *               reason:
 *                 type: string
 *                 description: Reason for the appointment
 *               notes:
 *                 type: string
 *                 description: Additional notes
 *               sessionId:
 *                 type: string
 *                 description: Current session ID for updating call session information
 *     responses:
 *       200:
 *         description: Successfully changed appointment
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Appointment'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * Handler for POST /api/external-api/v2/appointments/[id]/change
 * Changes appointment data by creating a new appointment with updated fields and canceling the original
 */
async function changeAppointmentHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const appointmentId = req.query.id as string;
  if (!appointmentId || appointmentId.trim() === '') {
    throw new BadRequestError('Invalid or missing appointment ID');
  }

  const validationResult = updateAppointmentSchema.safeParse(req.body);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid request body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  // Ensure at least one field is provided for update
  if (Object.keys(req.body).length === 0) {
    throw new BadRequestError('At least one field must be provided for update');
  }

  // Validate appointment start time is not in the past if it's being updated
  if (validationResult.data.startTime) {
    const appointmentStartTime = new Date(validationResult.data.startTime);
    const now = new Date();
    if (appointmentStartTime < now) {
      throw new BadRequestError('Appointment start time cannot be in the past', {
        code: 'INVALID_APPOINTMENT_TIME',
        message: 'The appointment start time must be in the future',
        suggestion: 'Please select a future date and time for the appointment',
      });
    }
  }

  const provider = getProviderFromRequest(req);
  const appointmentService = provider.getAppointmentService();

  const existingAppointment = await appointmentService.getAppointmentById(appointmentId);
  if (!existingAppointment) {
    throw new NotFoundError('Appointment not found');
  }

  const changedAppointment = await appointmentService.changeAppointment(
    appointmentId,
    validationResult.data,
  );

  // Override locationName using locationRepository
  await ensureDbInitialized();
  const locationsRepository = new LocationsRepository();

  try {
    const location = await locationsRepository.findById(changedAppointment.locationId);
    if (location?.name) {
      changedAppointment.locationName = location.name;
    }
  } catch (error) {
    logger.warn(
      { error, locationId: changedAppointment.locationId },
      `Failed to fetch location name for changed appointment ${appointmentId}`,
    );
    // Continue with original locationName if fetch fails
  }

  // Only update the current session with the call type
  try {
    // Get the current session ID from the request if available
    const currentSessionId = req.body.sessionId || req.query.sessionId;

    if (currentSessionId) {
      // Only update the current session that triggered the rescheduling
      await callSessionsService.addOrUpdateCallSession(currentSessionId as string, {
        appointmentId: changedAppointment.id, // Link to the new appointment
        callType: CallType.RESCHEDULE, // Set call type to RESCHEDULE
      });

      logger.info(
        {
          sessionId: currentSessionId,
          oldAppointmentId: appointmentId,
          newAppointmentId: changedAppointment.id,
          callType: CallType.RESCHEDULE,
        },
        `Updated current call session ${currentSessionId} with new appointment ID and set call type to RESCHEDULE`,
      );

      // Emit the appointment:created event with the appointment as payload
      // This will trigger the email notifications
      eventEmitter.emit(EVENTS.APPOINTMENT.CREATED, {
        sessionId: currentSessionId,
        appointment: changedAppointment,
      });
    } else {
      logger.info(
        { appointmentId, newAppointmentId: changedAppointment.id },
        `No current session ID provided for rescheduled appointment ${appointmentId}`,
      );
    }
  } catch (sessionError) {
    logger.error(
      { error: sessionError, appointmentId, newAppointmentId: changedAppointment.id },
      `Failed to update call session for changed appointment ${appointmentId}`,
    );
    // Continue even if session update fails
  }

  res.status(200).json(changedAppointment);
}

/**
 * Main handler for /api/external-api/v2/appointments/[id]/change
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Only allow POST method
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    return;
  }

  await changeAppointmentHandler(req, res);
}

// Export the handler wrapped by createApiHandler with middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, ensureProvidersInitialized],
});
