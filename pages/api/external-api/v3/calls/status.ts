import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';

import { createApiHandler, ensureProvidersInitialized } from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';

const twilioCallStatusSchema = z.object({
  CallSid: z.string(),
  Direction: z.string().optional(), // 'inbound', 'outbound-api'
  CallStatus: z.string().optional(), // 'queued', 'initiated', 'ringing', 'in-progress', 'completed', 'busy', 'failed', 'no-answer', 'canceled'
  CallDuration: z.string().optional(),
  Caller: z.string(),
  From: z.string(),
  Called: z.string(),
  To: z.string(),
});

const handleInboundCallCompleted = async (
  payload: z.infer<typeof twilioCallStatusSchema>,
): Promise<{ status: number; message: string }> => {
  const { CallSid: callId, CallDuration: callDuration } = payload;
  const call = await callsService.getCallById(callId);
  if (!call) {
    logger.error(
      { context: 'TwilioCallStatusHandler.handleInboundCallCompleted', callId },
      'Call not found',
    );
    return { status: 404, message: `Call not found` };
  }

  await callsService.updateCall(callId, {
    duration: `${callDuration} sec`,
  });

  logger.info(
    {
      context: 'TwilioCallStatusHandler.handleInboundCallCompleted',
      callId,
      callDuration,
    },
    'Call updated',
  );

  return { status: 200, message: `Call updated` };
};

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const validationResult = twilioCallStatusSchema.safeParse(req.body);
  if (!validationResult.success) {
    logger.error(
      {
        context: 'TwilioCallStatusHandler.invalidPayload',
        fieldErrors: validationResult.error.flatten().fieldErrors,
      },
      'Twilio call status webhook body validation failed',
    );
    res.status(400).json({ message: `Invalid request payload` });
    return;
  }

  const { Direction, CallStatus } = validationResult.data;
  try {
    let response: { status: number; message: string } | undefined;
    // Handle inbound call completed
    if (Direction === 'inbound' && CallStatus === 'completed') {
      response = await handleInboundCallCompleted(validationResult.data);
    }

    res.status(response?.status ?? 200).json({ message: response?.message ?? `OK` });
  } catch (innerError) {
    logger.error(
      {
        context: 'TwilioCallStatusHandler.unhandledException',
        innerError: String(innerError),
        innerErrorStack: innerError instanceof Error ? innerError.stack : undefined,
      },
      'Error handling Twilio call status webhook',
    );
    res.status(500).json({ message: `Internal server error` });
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
