// ADK Heather save voicemail handler endpoint

import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';

import { createApiHandler, ensureProvidersInitialized } from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';

const saveVoicemailSchema = z.object({
  CallSid: z.string(),
  RecordingUrl: z.string().optional(),
  RecordingStatus: z.string(),
  RecordingDuration: z.string().optional(),
});

const handleSaveVoicemail = async (
  callId: string,
  validationResult: z.infer<typeof saveVoicemailSchema>,
): Promise<void> => {
  const { RecordingUrl } = validationResult;
  logger.info(
    {
      context: `AdkSaveVoicemail.handler`,
      callId,
      voicemailUrl: RecordingUrl,
    },
    'Saving voicemail',
  );

  if (!RecordingUrl) {
    logger.error('Recording URL is required', validationResult);
    return;
  }

  await callsService.updateCall(callId, {
    voicemailUrl: RecordingUrl,
  });

  logger.info(
    {
      context: `AdkSaveVoicemail.handler`,
      callId,
      voicemailUrl: RecordingUrl,
    },
    'Voicemail saved',
  );
};

/**
 * Twilio ADK 'save-voicemail' webhook handler. It's called by Twilio when a voicemail is saved.
 * The webhook returns an XML response with TwiML instructions on how to end the call.
 */
async function httpHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const validationResult = saveVoicemailSchema.safeParse(req.body);
    if (!validationResult.success) {
      logger.error(
        'Save voicemail validation failed:',
        validationResult.error.flatten().fieldErrors,
      );
      res.status(400).json({
        message: 'Save voicemail validation failed',
        errors: validationResult.error.flatten().fieldErrors,
      });
      return;
    }
    if (validationResult.data.RecordingStatus === 'failed') {
      logger.info('Recording status is failed', validationResult.data);
      res.status(200).json({
        message: 'Recording status is failed',
      });
      return;
    }
    if (validationResult.data.RecordingStatus === 'completed') {
      logger.info({ context: 'AdkSaveVoicemail.handler' }, 'Recording status is completed');
      const callId = validationResult.data.CallSid;
      if (!callId) {
        logger.error(
          {
            context: `AdkSaveVoicemail.handler`,
          },
          'Call ID is required',
        );
        res.status(400).json({
          message: 'Call ID is required',
        });
        return;
      }

      const adkCall = await callsService.getCallById(callId as string);
      if (!adkCall) {
        logger.error(
          {
            context: `AdkSaveVoicemail.handler`,
            callId,
          },
          'Call not found',
        );
        res.status(404).json({
          message: 'Call not found',
        });
        return;
      }

      await handleSaveVoicemail(callId as string, validationResult.data);
      res.status(200).json({
        message: 'Voicemail saved',
      });
      return;
    } else {
      logger.info({ context: 'AdkSaveVoicemail.handler' }, 'Recording status is not completed');
      res.status(200).json({
        message: 'Recording status is not completed',
      });
      return;
    }
  } catch (innerError) {
    logger.error(
      {
        context: `AdkSaveVoicemail.handler`,
        innerError: String(innerError),
        innerErrorStack: innerError instanceof Error ? innerError.stack : undefined,
      },
      'Error processing request',
    );
    res.status(500).json({
      message: 'Error processing request',
    });
  }
}

// Export with API key validation middleware
export default createApiHandler(httpHandler, {
  middleware: [ensureProvidersInitialized],
});
