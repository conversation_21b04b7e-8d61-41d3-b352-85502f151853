// ADK Heather save conversation dialogs endpoint

import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { createApiHandler, ensureProvidersInitialized } from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';

dayjs.extend(utc);

const dialogSchema = z.object({
  role: z.enum(['user', 'agent']),
  text: z.string(),
  timestamp: z.string(),
});

const saveConversationDialogsSchema = z.object({
  dialogs: z.array(dialogSchema),
});

const saveConversationDialogs = async (
  query: Record<string, unknown>,
  body: Record<string, unknown>,
): Promise<{ status: number; message?: string }> => {
  const callId = query.id as string | undefined;
  if (!callId) {
    logger.error(
      {
        context: `AdkSaveConversationDialogs.handler`,
      },
      'Call ID is required',
    );
    return { status: 400, message: 'Call ID is required' };
  }

  const validationResult = saveConversationDialogsSchema.safeParse(body);
  if (!validationResult.success) {
    logger.error(
      {
        context: `AdkSaveConversationDialogs.handler`,
        errors: validationResult.error.flatten().fieldErrors,
      },
      'Invalid request payload',
    );
    return { status: 400, message: 'Invalid request payload' };
  }
  const { dialogs } = validationResult.data;
  if (dialogs.length === 0) {
    logger.error(
      {
        context: `AdkSaveConversationDialogs.handler`,
        callId,
      },
      'No dialogs provided',
    );
    return { status: 200, message: 'There is nothing to save. Dialogs are empty.' };
  }

  const call = await callsService.getCallById(callId);
  if (!call) {
    logger.warn(
      {
        context: `AdkSaveConversationDialogs.handler`,
        callId,
      },
      'Call not found',
    );
    return { status: 404, message: 'Call not found' };
  }

  const transcriptions = dialogs
    .reduce(
      (accumulator, currentValue, currentIndex) => {
        if (currentIndex === 0) {
          accumulator.push(currentValue);
        } else {
          // Group agent's dialogs together as a single entry
          const previousValue = accumulator[accumulator.length - 1];
          if (
            previousValue.role === currentValue.role &&
            dayjs
              .utc(currentValue.timestamp)
              .diff(dayjs.utc(previousValue.timestamp), 'millisecond') < 1000
          ) {
            previousValue.text += currentValue.text;
          } else {
            accumulator.push(currentValue);
          }
        }

        return accumulator;
      },
      [] as z.infer<typeof dialogSchema>[],
    )
    .map(
      dialog => `${dialog.role === 'user' ? 'Patient' : 'Heather'}: ${dialog.text?.trim() || ''}`,
    );

  await callsService.updateCall(callId, { transcription: transcriptions.join('\n') });
  logger.info(
    {
      context: `AdkSaveConversationDialogs.handler`,
      callId,
      turns: transcriptions.length,
    },
    'Conversation dialogs saved',
  );

  return { status: 200, message: 'Conversation dialogs saved' };
};

/**
 * ADK 'save-conversation-dialogs' endpoint. It's called by ADK server when a conversation dialogs are saved.
 * The endpoint returns an JSON response with the conversation dialogs.
 */
async function httpHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const result = await saveConversationDialogs(req.query, req.body);
    res.status(result.status || 200).json({ message: result.message || 'OK' });
  } catch (innerError) {
    logger.error(
      {
        context: `AdkSaveConversationDialogs.handler`,
        innerError: String(innerError),
        innerErrorStack: innerError instanceof Error ? innerError.stack : undefined,
      },
      'Error processing request',
    );
    res.status(500).json({
      message: 'Error processing request',
    });
  }
}

// Export with API key validation middleware
export default createApiHandler(httpHandler, {
  middleware: [ensureProvidersInitialized],
});
