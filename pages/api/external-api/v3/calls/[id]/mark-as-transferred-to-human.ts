import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';

import {
  BadRequestError,
  create<PERSON>pi<PERSON><PERSON><PERSON>,
  ensureProvidersInitialized,
  NotFoundError,
} from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';
import { CallType } from '@/models/CallTypes';
import { updateCallSessionType } from '@/lib/external-api/v2/utils/call-type-utils';
import { TransferClassificationService } from '@/utils/transfer-classification-service';

const markAsTransferredToHumanSchema = z.object({
  callId: z.string(),
  sessionId: z.string().optional(),
  useSmartClassification: z.boolean().optional().default(true), // Enable smart classification by default
});

async function httpHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const validationResult = markAsTransferredToHumanSchema.safeParse(req.body);
  if (!validationResult.success) {
    logger.warn(
      'Mark as transferred to human validation failed:',
      validationResult.error.flatten().fieldErrors,
    );
    throw new BadRequestError('Invalid body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const { callId, sessionId, useSmartClassification } = validationResult.data;
  const call = await callsService.getCallById(callId);
  if (!call) {
    logger.error({ callId }, 'Call not found');
    throw new NotFoundError('Call not found');
  }

  // Use smart classification if enabled (default)
  let transferType = CallType.TRANSFER_TO_HUMAN; // Default fallback
  let classificationReason = 'Legacy transfer endpoint - no classification applied';

  if (useSmartClassification) {
    transferType = TransferClassificationService.classifyTransfer(call);
    classificationReason = TransferClassificationService.getClassificationReason(
      transferType,
      call,
    );

    logger.info(
      {
        callId: call.id,
        originalType: CallType.TRANSFER_TO_HUMAN,
        classifiedType: transferType,
        reason: classificationReason,
      },
      'Smart transfer classification applied',
    );
  }

  await callsService.updateCall(call.id, {
    type: transferType,
  });

  // Update call session if sessionId provided
  if (sessionId) {
    await updateCallSessionType(sessionId, transferType, 'mark-as-transferred-to-human');
  }

  logger.info(
    {
      callId: call.id,
      sessionId,
      transferType,
      useSmartClassification,
      reason: classificationReason,
    },
    'Call updated with transfer classification',
  );

  res.status(200).end();
}

// Export with API key validation middleware
export default createApiHandler(httpHandler, {
  middleware: [ensureProvidersInitialized],
});
