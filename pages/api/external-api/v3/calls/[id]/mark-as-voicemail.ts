import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';

import {
  BadRequestError,
  create<PERSON>pi<PERSON>and<PERSON>,
  ensureProvidersInitialized,
  NotFoundError,
} from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';
import { CallType } from '@/models/CallTypes';

const markAsVoicemailSchema = z.object({
  callId: z.string(),
});

async function httpHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const validationResult = markAsVoicemailSchema.safeParse(req.body);
  if (!validationResult.success) {
    logger.warn(
      'Mark as voicemail validation failed:',
      validationResult.error.flatten().fieldErrors,
    );
    throw new BadRequestError('Invalid body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const { callId } = validationResult.data;
  const call = await callsService.getCallById(callId);
  if (!call) {
    logger.error({ callId }, 'Call not found');
    throw new NotFoundError('Call not found');
  }

  await callsService.updateCall(call.id, {
    type: CallType.VOICEMAIL,
    callTypes: [CallType.VOICEMAIL],
  });

  logger.info(
    {
      callId: call.id,
    },
    'Call updated with voicemail',
  );

  res.status(200).end();
}

// Export with API key validation middleware
export default createApiHandler(httpHandler, {
  middleware: [ensureProvidersInitialized],
});
