// AD<PERSON> Heather hang up handler endpoint

import { NextApiRequest, NextApiResponse } from 'next';

import { createA<PERSON><PERSON>and<PERSON>, ensureProvidersInitialized } from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';
import { callService } from '@/lib/services/call-service';
import { CallType } from '@/models/CallTypes';
import { LocationService } from '@/lib/services/locationService';
import { TRANSFER_TO_CLINIC_REDIRECT_NUMBER } from '@/app-config';
import { Call } from '@/models/Call';

const handleHangUp = async (callId: string, adkCall: Call): Promise<{ xml: string }> => {
  logger.info(
    {
      context: `InboundHangUpCall.handler`,
      callId,
      adkCall,
    },
    'ADK call found',
  );
  const callType = Number(adkCall.type);
  const hangUpXml = callService.createEndCallRule().toString();
  const redirectNumber: string | undefined = TRANSFER_TO_CLINIC_REDIRECT_NUMBER;
  if (callType === CallType.TRANSFER_TO_HUMAN) {
    logger.info({ context: `InboundHangUpCall.handler`, adkCall }, 'Transferring call to staff');
    const location = await LocationService.getLocationById(
      adkCall.locationId.toString(),
      adkCall.clinicId,
    );

    const actualPhoneNumber = redirectNumber || location?.phone;
    if (!actualPhoneNumber) {
      logger.info(
        { context: `InboundHangUpCall.handler`, adkCall },
        `No phone number found for location ${adkCall.locationId}`,
      );
      return { xml: hangUpXml };
    }

    const xml = callService
      .createDialNumberRule({
        phoneNumber: actualPhoneNumber,
      })
      .toString();

    return { xml };
  }

  if (callType === CallType.VOICEMAIL) {
    logger.info({ context: `InboundHangUpCall.handler`, callId }, 'Transferring call to voicemail');
    const xml = callService.createRecordingRule(callId).toString();

    return { xml };
  }

  if (callType === CallType.TRANSFER_TO_CLINIC) {
    logger.info({ context: `InboundHangUpCall.handler`, adkCall }, 'Transferring call to clinic');

    if (!adkCall.transferToLocationId) {
      logger.error(
        { context: `InboundHangUpCall.handler`, adkCall },
        `transferToLocationId not set in the call ${adkCall.id}`,
      );

      return { xml: hangUpXml };
    }

    logger.info(
      {
        context: `InboundHangUpCall.handler`,
        transferToLocationId: adkCall.transferToLocationId,
        clinicId: adkCall.clinicId,
      },
      `Searching for clinic location`,
    );
    const location = await LocationService.getLocationById(
      adkCall.transferToLocationId.toString(),
      adkCall.clinicId,
    );
    if (!location) {
      logger.info(
        { context: `InboundHangUpCall.handler`, adkCall },
        `Location ${adkCall.transferToLocationId} not found`,
      );

      return { xml: hangUpXml };
    }
    logger.info(
      {
        context: `InboundHangUpCall.handler`,
        location,
      },
      `Location found`,
    );
    if (!location.phone) {
      logger.info(
        { context: `InboundHangUpCall.handler`, adkCall },
        `Location ${adkCall.transferToLocationId} has no phone number`,
      );
      return { xml: hangUpXml };
    }

    const actualPhoneNumber = redirectNumber || location.phone;
    if (!actualPhoneNumber) {
      logger.info(
        { context: `InboundHangUpCall.handler`, adkCall },
        `No phone number found for location ${adkCall.transferToLocationId}`,
      );
      return { xml: hangUpXml };
    }

    logger.info(
      {
        context: `InboundHangUpCall.handler`,
        actualPhoneNumber,
      },
      `Transferring call to clinic`,
    );
    const xml = callService
      .createDialNumberRule({
        phoneNumber: actualPhoneNumber,
      })
      .toString();

    return { xml };
  }

  const xml = callService.createEndCallRule().toString();
  return { xml };
};

/**
 * Twilio ADK 'hang-up' webhook handler. It's called by Twilio when a call is hung up either by the caller or the agent.
 * The webhook returns an XML response with TwiML instructions on how to end the call.
 */
async function httpHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const sendXmlResponse = (xml: string, params?: { status?: number }) => {
    const { status = 200 } = params || {};
    res.setHeader('Content-Type', 'text/xml').status(status).send(xml);
  };

  const hangUpResponse = () => {
    const twiML = callService.createEndCallRule().toString();
    sendXmlResponse(twiML);
  };

  try {
    const { id: callId } = req.query;
    if (!callId) {
      logger.error(
        {
          context: `AdkInboundHangUpCall.handler`,
        },
        'Call ID is required',
      );
      hangUpResponse();
      return;
    }

    const adkCall = await callsService.getCallById(callId as string);
    if (!adkCall) {
      logger.error(
        {
          context: `AdkInboundHangUpCall.handler`,
          callId,
        },
        'Call not found',
      );
      hangUpResponse();
      return;
    }

    const { xml } = await handleHangUp(callId as string, adkCall);
    sendXmlResponse(xml);
  } catch (innerError) {
    logger.error(
      {
        context: `AdkInboundHangUpCall.handler`,
        innerError: String(innerError),
        innerErrorStack: innerError instanceof Error ? innerError.stack : undefined,
      },
      'Error processing request',
    );
    hangUpResponse();
  }
}

// Export with API key validation middleware
export default createApiHandler(httpHandler, {
  middleware: [ensureProvidersInitialized],
});
