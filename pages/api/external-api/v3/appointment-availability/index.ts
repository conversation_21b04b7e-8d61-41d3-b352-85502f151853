import { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, validate<PERSON><PERSON><PERSON><PERSON>, getProvider<PERSON>romRequest } from '@/lib/external-api/v2';
import { appointmentAvailabilityQuerySchema } from '@/lib/external-api/v2/validators';
import { NextechAppointmentTypes } from '@/models/AppointmentTypes';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import { CallType } from '@/models/CallTypes';
import { updateCallSessionType } from '@/lib/external-api/v2/utils/call-type-utils';
import { LocationsRepository } from '@/lib/repositories/locations-repository';
import { ensureDbInitialized } from '@/lib/middleware/db-init';
import { AgentLocationMappingService } from '@/lib/services/agent-location-mapping';
import { callsService } from '@/utils/firestore';

/**
 * @swagger
 * /api/external-api/v3/appointment-availability:
 *   get:
 *     summary: Get appointment availability
 *     description: Retrieve available slots for appointment booking based on criteria
 *     tags: [External API v3]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: appointmentTypeId
 *         required: false
 *         description: ID of the appointment type to check availability for
 *       - in: query
 *         name: practitionerId
 *         required: false
 *         description: Provider ID to check availability for
 *       - in: query
 *         name: locationId
 *         required: false
 *         description: Location ID to check availability for
 *       - in: query
 *         name: startDate
 *         required: false
 *         description: Start date for availability check (YYYY-MM-DD). Default - today
 *       - in: query
 *         name: endDate
 *         required: false
 *         description: End date for availability check (YYYY-MM-DD). Default - today
 *       - in: query
 *         name: sessionId
 *         required: false
 *         description: Current session ID for updating call session information
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of available slots
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       startDateTime:
 *                         type: string
 *                         format: date-time
 *                         description: Start time of the available slot in ISO format
 *                         example: "2025-04-10T07:15:00-04:00"
 *                       endDateTime:
 *                         type: string
 *                         format: date-time
 *                         description: End time of the available slot in ISO format
 *                         example: "2025-04-10T07:30:00-04:00"
 *                       practitionerId:
 *                         type: string
 *                         description: ID of the practitioner for this slot
 *                         example: "110"
 *                       practitionerName:
 *                         type: string
 *                         description: Name of the practitioner for this slot
 *                         example: "Meena George"
 *                       locationId:
 *                         type: string
 *                         description: ID of the location for this slot
 *                         example: "118"
 *                       locationName:
 *                         type: string
 *                         description: Name of the location for this slot
 *                         example: "URMA - Lombard"
 *                       appointmentTypeId:
 *                         type: string
 *                         description: ID of the appointment type for this slot
 *                         example: "18"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalCount:
 *                       type: integer
 *                       description: Total number of items available
 *                       example: 25
 *                     limit:
 *                       type: integer
 *                       description: Number of items per page
 *                       example: 10
 *                     offset:
 *                       type: integer
 *                       description: Starting position for pagination
 *                       example: 0
 *                     hasMore:
 *                       type: boolean
 *                       description: Whether there are more items available
 *                       example: true
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for GET /api/external-api/v3/appointment-availability
 * Retrieves available appointment slots based on criteria
 */
async function getAppointmentAvailabilityHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // Get provider from request
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    const defaultAppointmentTypeId = NextechAppointmentTypes.FOLLOW_UP;

    // Keep these as defaults for backward compatibility
    let practitionerId = '110'; // "Meena George"
    let locationId = URMA_LOMBARD_LOCATION_ID; // "URMA - Lombard"

    let locationIds: string[] | undefined = undefined;

    // Set default dates if not provided
    req.query.startDate = req.query.startDate || new Date().toISOString().split('T')[0];
    req.query.endDate = req.query.endDate || new Date().toISOString().split('T')[0];

    // Validate query parameters using Zod
    const validatedQuery = appointmentAvailabilityQuerySchema.parse(req.query);

    if (validatedQuery.sessionId) {
      try {
        const calls = await callsService.getCallsBySessionId(validatedQuery.sessionId);
        const call = calls.length > 0 ? calls[0] : null;

        if (!call) {
          return res.status(500).json({
            message: `Call not found for session ID ${validatedQuery.sessionId}`,
          });
        }

        if (!call.agentId) {
          return res.status(500).json({
            message: `Agent ID not found for call ID ${call.id}`,
          });
        }

        const mapping = await AgentLocationMappingService.getMappingByAgentId(call.agentId);

        if (mapping?.defaultPractitionerId) {
          practitionerId = mapping.defaultPractitionerId;
        }

        if (mapping?.locationId) {
          locationId = mapping.locationId;
        }

        if (mapping?.locationIds) {
          locationIds = mapping.locationIds;
        }
      } catch (error) {
        console.error(`Error getting mapping for session ID ${validatedQuery.sessionId}: ${error}`);
      }
    }

    if (!locationId) {
      return res.status(500).json({
        message: `Location ID not found for session ID ${validatedQuery.sessionId}`,
      });
    }

    if (!practitionerId) {
      return res.status(500).json({
        message: `Practitioner ID not found for session ID ${validatedQuery.sessionId}`,
      });
    }

    // Get available slots with the provided filters
    const availableSlots = await appointmentService.getAvailableSlots({
      appointmentTypeId: validatedQuery.appointmentTypeId || defaultAppointmentTypeId,
      startDate: validatedQuery.startDate,
      endDate: validatedQuery.endDate,
      practitionerId: validatedQuery.practitionerId || practitionerId,
      locationId: validatedQuery.locationId || locationId,
      // If locationId is provided, don't use locationIds
      locationIds: validatedQuery.locationId ? undefined : locationIds,
    });

    await ensureDbInitialized();
    const locationsRepository = new LocationsRepository();

    // Extract unique location IDs from available slots
    const uniqueLocationIds = Array.from(
      new Set(availableSlots.items.map(slot => slot.locationId)),
    );

    // Fetch all locations in a single batch and create a map for efficient lookup
    const locationNameMap = await locationsRepository.getLocationNamesMap(uniqueLocationIds);

    // Transform practitioner names to include "Doctor" prefix and correct location names
    const transformedSlots = {
      ...availableSlots,
      items: availableSlots.items.map(slot => ({
        ...slot,
        practitionerName: `Doctor ${slot.practitionerName}`,
        locationName: locationNameMap.get(slot.locationId) ?? slot.locationName,
      })),
    };

    // Update call session with LOOKUP type if sessionId provided
    const { sessionId } = req.query;
    if (typeof sessionId === 'string' && sessionId.trim() !== '') {
      await updateCallSessionType(sessionId, CallType.LOOKUP, 'Appointment Availability Lookup');
    }

    // Return the transformed available slots
    return res.status(200).json(transformedSlots);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v3/appointment-availability
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // GET request for appointment availability
  if (req.method === 'GET') {
    await getAppointmentAvailabilityHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.status(405).json({ message: 'Method not allowed' });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
