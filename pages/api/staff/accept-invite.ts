import type { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { mysqlService } from '@/lib/database/mysql-service';
import { UsersRepository } from '@/lib/repositories/users-repository';
import { UserPermissionsRepository } from '@/lib/repositories/user-permissions-repository';
import { UserRole } from '@/models/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') return res.status(405).json({ message: 'Method not allowed' });

  const { code, email } = req.body as { code?: string; email?: string };
  if (!code || !email) return res.status(400).json({ message: 'code and email are required' });

  try {
    // Load invite from Firestore
    const db = admin.firestore();
    const snap = await db.collection('staffInviteCodes').where('code', '==', code).limit(1).get();
    if (snap.empty) return res.status(404).json({ message: 'Invalid invite code' });
    const doc = snap.docs[0];
    const data = doc.data() as {
      used?: boolean;
      expiresAt?: FirebaseFirestore.Timestamp;
      clinicId: number;
      email?: string | null;
      assignedRoleId?: string | null;
      assignedLocationIds?: string[];
    };
    if (data.used) return res.status(400).json({ message: 'Invite already used' });
    if (data.expiresAt && data.expiresAt.toDate() < new Date())
      return res.status(400).json({ message: 'Invite expired' });
    if (data.email && data.email.toLowerCase() !== email.toLowerCase())
      return res.status(400).json({ message: 'Invite is restricted to a different email' });

    // Initialize DB
    await mysqlService.initialize();
    const usersRepo = new UsersRepository();
    const upr = new UserPermissionsRepository();

    // Ensure user exists in MySQL (upsert basic profile with clinic)
    // Find Firebase user by email
    const authUser = await admin
      .auth()
      .getUserByEmail(email)
      .catch(() => null);
    if (!authUser) return res.status(400).json({ message: 'Email must be registered in auth' });

    const userId = authUser.uid;
    const existing = await usersRepo.findById(userId, { forceMySQL: true });
    if (!existing) {
      await usersRepo.create(
        {
          email,
          name: authUser.displayName || email,
          role: UserRole.STAFF,
          clinicId: data.clinicId,
          canTakeAppointments: false,
          locationIds: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        { generateEntityId: () => userId },
      );
    } else if (existing.clinicId == null) {
      await usersRepo.update(userId, { clinicId: data.clinicId, canTakeAppointments: false });
    }

    // Assign role and locations if provided
    if (data.assignedRoleId) {
      await upr.replaceUserRoles(userId, [data.assignedRoleId], null);
    }
    if (Array.isArray(data.assignedLocationIds) && data.assignedLocationIds.length > 0) {
      // Store on user record's locationIds
      const merged = Array.from(
        new Set([...(existing?.locationIds || []), ...data.assignedLocationIds]),
      );
      // Set current location if not set
      const currentLoc = existing?.currentLocationId || merged[0];
      await usersRepo.update(userId, { locationIds: merged, currentLocationId: currentLoc });
    }

    // Mark invite as used
    await doc.ref.update({ used: true, usedAt: new Date() });

    return res.status(200).json({ success: true });
  } catch (e) {
    console.error('Accept invite failed', e);
    return res.status(500).json({ message: 'Internal Server Error' });
  } finally {
    try {
      await mysqlService.close();
    } catch {}
  }
}
