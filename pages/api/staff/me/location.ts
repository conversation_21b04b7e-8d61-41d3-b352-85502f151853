import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserLocationService } from '@/lib/services/userLocationService';
import { User } from '@/models/auth';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';
import { ensureDbInitialized } from '@/lib/middleware/db-init';

/**
 * @swagger
 * /api/staff/me/location:
 *   get:
 *     summary: Get current user's location context
 *     tags: [Staff Location]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User's location context
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 currentLocation:
 *                   $ref: '#/components/schemas/Location'
 *                   description: Currently selected location (if any)
 *                 availableLocations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *                   description: All locations user has access to
 *                 availablePractices:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Practice'
 *                   description: All practices derived from user's locations
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Switch user's current location
 *     tags: [Staff Location]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - locationId
 *             properties:
 *               locationId:
 *                 type: string
 *                 description: ID of the location to switch to
 *     responses:
 *       200:
 *         description: Location switched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 currentLocation:
 *                   $ref: '#/components/schemas/Location'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user doesn't have access to location
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */

interface SwitchLocationRequest {
  locationId: string;
}

interface UserLocationContextResponse {
  currentLocation?: Location;
  availableLocations: Location[];
  availablePractices: Practice[];
}

interface SwitchLocationResponse {
  success: boolean;
  user: User;
  currentLocation?: Location;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  try {
    await ensureDbInitialized();
    if (req.method === 'GET') {
      return await handleGetLocationContext(req, res, user as User);
    } else if (req.method === 'PUT' || req.method === 'POST') {
      return await handleSwitchLocation(req, res, user as User);
    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Error in staff location API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve user's location context
 */
async function handleGetLocationContext(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
): Promise<void> {
  try {
    const context = await UserLocationService.getUserLocationContext(user.id!);

    const response: UserLocationContextResponse = {
      currentLocation: context.currentLocation,
      availableLocations: context.availableLocations,
      availablePractices: context.availablePractices,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting user location context:', error);

    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve location context',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle PUT request to switch user's current location
 */
async function handleSwitchLocation(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
): Promise<void> {
  try {
    // Validate request body
    const { locationId }: SwitchLocationRequest = req.body;

    if (!locationId || typeof locationId !== 'string' || locationId.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'locationId is required and must be a non-empty string',
      });
    }

    // Switch user's location
    const updatedUser = await UserLocationService.switchUserLocation(
      user.id!,
      locationId.trim(),
      user,
    );

    // Get the new current location details
    const context = await UserLocationService.getUserLocationContext(user.id!);

    const response: SwitchLocationResponse = {
      success: true,
      user: updatedUser,
      currentLocation: context.currentLocation,
      message: 'Location switched successfully',
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error switching user location:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('does not have access') ||
        error.message.includes('does not belong')
      ) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('Invalid') || error.message.includes('required')) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to switch location',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
