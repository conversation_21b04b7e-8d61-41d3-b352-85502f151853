import { NextApiRequest, NextApiResponse } from 'next';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { FeatureKey, PermissionLevel, UserRole } from '@/models/auth';
import { PermissionService } from '@/lib/services/permission-service';
import {
  verifyAuthAndGetUser,
  verifyFirebaseIdToken,
  getUserFromFirestore,
} from '@/utils/firebase-admin';
import { generateRandomCode } from '@/utils/common';
import { mailService } from '@/utils/firestore';

// Initialize Firebase Admin SDK (server-side only)
if (!getApps().length) {
  try {
    initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('Firebase Admin initialized for generate-invite-code endpoint');
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
  }
}

// Get admin Firestore instance
const db = getFirestore();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('Handling generate-invite-code request');

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Support both token in header and standard Firebase auth
  let user;
  const authHeader = req.headers.authorization || '';

  if (authHeader.startsWith('Bearer ')) {
    // Extract token
    const idToken = authHeader.split('Bearer ')[1];
    console.log('Got Bearer token, verifying...');

    // Verify token
    try {
      const decodedToken = await verifyFirebaseIdToken(idToken);
      if (decodedToken) {
        console.log('Token verified for:', decodedToken.uid);
        user = await getUserFromFirestore(decodedToken.uid);
      }
    } catch (error) {
      console.error('Error verifying token:', error);
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
      });
    }
  } else {
    // Try normal session-based auth
    user = await verifyAuthAndGetUser(req);
  }

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized',
    });
  }

  // If this request is for inviting an Account Owner, enforce platform permission
  if (req.body?.inviteAccountOwner === true) {
    const svc = new PermissionService();
    const level = await svc.resolveEffectiveLevel(user.id, FeatureKey.INVITE_ACCOUNT_OWNER);
    const rank = (lvl: PermissionLevel): number => {
      switch (lvl) {
        case PermissionLevel.NONE:
          return 0;
        case PermissionLevel.READ:
          return 1;
        case PermissionLevel.WRITE:
          return 2;
        case PermissionLevel.ADMIN:
          return 3;
        default:
          return 0;
      }
    };
    if (rank(level) < rank(PermissionLevel.ADMIN)) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only SUPER_ADMIN can invite account owners',
      });
    }
  }

  const clinicId = req.body.clinicId || user.clinicId;
  const email: string | undefined = req.body.email;
  const assignedRoleId: string | undefined = req.body.assignedRoleId;
  const assignedLocationIds: string[] | undefined = req.body.assignedLocationIds;

  if (!clinicId) {
    return res.status(400).json({
      success: false,
      message: 'Bad request: Missing clinic ID',
    });
  }

  // Only allow clinic admins to generate codes for their own clinic
  // SUPER_ADMIN can generate for any clinic
  if (user.role === UserRole.CLINIC_ADMIN && user.clinicId !== Number(clinicId)) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: You can only generate invite codes for your own clinic',
    });
  }

  try {
    const code = generateRandomCode();

    // Set expiration date (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    // Create code document
    const codeRef = db.collection('staffInviteCodes').doc();
    await codeRef.set({
      code,
      clinicId: Number(clinicId),
      used: false,
      expiresAt,
      createdBy: user.id,
      createdAt: new Date(),
      // Optional targeting info for onboarding
      email: email || null,
      assignedRoleId: assignedRoleId || null,
      assignedLocationIds: Array.isArray(assignedLocationIds) ? assignedLocationIds : [],
    });

    console.log('Successfully generated code:', code, 'for clinic:', clinicId);

    // Send invite email via Firebase mailService if email provided
    if (email) {
      try {
        const origin = req.headers.origin || 'http://localhost:3000';
        const params = new URLSearchParams({ code });
        if (email) params.set('email', email);
        const inviteLink = `${origin}/invite/accept?${params.toString()}`;
        const clinicName = await (async () => {
          const clinicDoc = await db.collection('clinics').doc(String(clinicId)).get();
          const clinicData = clinicDoc.data() as Record<string, unknown> | undefined;
          const nameCandidate = clinicData?.name;
          if (typeof nameCandidate === 'string' && nameCandidate.trim().length > 0) {
            return nameCandidate;
          }
          return `Practice ${String(clinicId)}`;
        })();
        await mailService.sendTemplatedEmail({
          to: [email],
          template: 'invite-staff',
          data: {
            link: inviteLink,
            code,
            // Fetch the clinic name for a friendly email instead of numeric ID
            clinicName,
            supportTeamEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
            privacyPolicyUrl: process.env.PRIVACY_POLICY_URL || 'https://frontdesk.doctor/privacy',
            termsOfServiceUrl: process.env.TERMS_OF_SERVICE_URL || 'https://frontdesk.doctor/terms',
          },
        });
      } catch (e) {
        console.error('Failed to enqueue invite email', e);
      }
    }

    return res.status(200).json({
      success: true,
      message: 'Invite code generated successfully',
      code,
      expiresAt,
      id: codeRef.id,
      assignedRoleId: assignedRoleId ?? null,
      assignedLocationIds: Array.isArray(assignedLocationIds) ? assignedLocationIds : [],
      email: email ?? null,
    });
  } catch (error: unknown) {
    console.error('Error generating invite code:', error);

    return res.status(500).json({
      success: false,
      message: 'Server error generating invite code',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
