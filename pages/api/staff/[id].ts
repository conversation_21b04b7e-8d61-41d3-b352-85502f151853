import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';
import { UsersRepository } from '@/lib/repositories/users-repository';
import { mysqlService } from '@/lib/database/mysql-service';
import { ensureDbInitialized } from '@/lib/middleware/db-init';
import admin from '@/utils/firebase-admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await ensureDbInitialized();

  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Special case for /api/staff/me endpoint
  if (req.method === 'GET' && req.query.id === 'me') {
    // Return the authenticated user from MySQL (fallback to Firebase details if not present)
    const repo = new UsersRepository();
    const dbUser = await repo.findById(user.id);
    if (!dbUser) {
      return res.status(200).json({
        id: user.id,
        name: user.name ?? 'User',
        email: user.email ?? '',
        role: user.role,
        clinicId: user.clinicId ?? null,
      });
    }
    return res.status(200).json(dbUser);
  }

  const repo = new UsersRepository();
  const targetId = String(req.query.id);

  if (req.method === 'GET') {
    // Regular case for specific user ID – read from MySQL
    const targetUser = await repo.findById(targetId);
    if (!targetUser) return res.status(404).json({ message: 'User not found' });

    // Check if requesting user has permission to view this user
    if (
      user.role !== UserRole.SUPER_ADMIN &&
      user.role !== UserRole.CLINIC_ADMIN &&
      user.id !== targetUser.id
    ) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    return res.status(200).json(targetUser);
  }

  if (req.method === 'DELETE') {
    // Disallow self-delete via this endpoint
    if (targetId === 'me' || targetId === user.id) {
      return res.status(400).json({ message: 'Cannot delete the current authenticated user' });
    }

    const targetUser = await repo.findById(targetId);
    if (!targetUser) return res.status(404).json({ message: 'User not found' });

    const isSuperAdmin = user.role === UserRole.SUPER_ADMIN;
    const isClinicAdmin = user.role === UserRole.CLINIC_ADMIN;

    if (!isSuperAdmin && !isClinicAdmin) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    if (isClinicAdmin) {
      // Clinic admins can only delete users in their clinic and not admins
      if (targetUser.clinicId !== user.clinicId) {
        return res.status(403).json({ message: 'Forbidden: different clinic' });
      }
      if (targetUser.role === UserRole.CLINIC_ADMIN || targetUser.role === UserRole.SUPER_ADMIN) {
        return res.status(403).json({ message: 'Forbidden: cannot delete admin users' });
      }
    }

    try {
      // Clean up RBAC associations
      await mysqlService.query('DELETE FROM user_roles WHERE user_id = ?', [targetId]);
      await mysqlService.query('DELETE FROM user_permission_overrides WHERE user_id = ?', [
        targetId,
      ]);

      // Delete the user (dual delete: MySQL + Firestore)
      await repo.delete(targetId);

      // Attempt to delete from Firebase Auth (ignore if not found)
      try {
        await admin.auth().deleteUser(targetId);
      } catch (authErr) {
        const msg = authErr instanceof Error ? authErr.message : String(authErr);
        if (!msg.includes('auth/user-not-found')) {
          console.warn('Failed to delete user from Firebase Auth:', msg);
        }
      }

      return res.status(204).end();
    } catch (e) {
      console.error('Failed to delete user', e);
      return res.status(500).json({ message: 'Internal Server Error' });
    }
  }

  return res.status(405).json({ message: 'Method not allowed' });
}
