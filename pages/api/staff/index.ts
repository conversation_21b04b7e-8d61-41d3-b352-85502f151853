import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';
import admin from 'firebase-admin';
import { getRepositories } from '@/lib/repositories';

interface FirestoreUser {
  id: string;
  name?: string;
  email?: string;
  role: string | UserRole;
  clinicId: number | null;
  createdAt: Date | admin.firestore.Timestamp;
  updatedAt: Date | admin.firestore.Timestamp;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Verify authentication using Firebase Auth
  const user = (await verifyAuthAndGetUser(req)) as FirestoreUser;
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Normalize roles for comparison (handle potential case sensitivity issues)
  const userRole = typeof user.role === 'string' ? user.role.toUpperCase() : user.role;

  // Compare with enum values and string literals
  const isAdmin = userRole === UserRole.CLINIC_ADMIN || userRole === UserRole.SUPER_ADMIN;
  const isSuperAdmin = userRole === UserRole.SUPER_ADMIN;

  // Only clinic admin or super admin can access staff list
  if (!isAdmin) {
    return res.status(403).json({
      message: 'Forbidden. Only admins can access staff list',
      debug: {
        userRole: user.role,
        allowedRoles: [UserRole.CLINIC_ADMIN, UserRole.SUPER_ADMIN],
      },
    });
  }

  try {
    // Get initialized repositories
    const repoManager = getRepositories();
    await repoManager.initialize();
    const repo = repoManager.users;
    const upr = repoManager.userPermissions;
    // Super admin: fetch all; Clinic admin: filter by clinicId
    const where = isSuperAdmin || user.clinicId === null ? {} : { clinicId: user.clinicId };
    const result = await repo.findMany({
      where,
      orderBy: [{ field: 'name', direction: 'asc' }],
      limit: 500,
    });
    // Attach RBAC roles (names)
    const userIds = result.items.map(u => u.id);
    const roleRows = await upr.getRolesForUsers(userIds);
    const userIdToRoles: Record<string, string[]> = {};
    for (const row of roleRows) {
      if (!userIdToRoles[row.user_id]) userIdToRoles[row.user_id] = [];
      userIdToRoles[row.user_id].push(row.role_name);
    }
    const staffWithRoles = result.items.map(u => ({
      ...u,
      rbacRoles: userIdToRoles[u.id] || [],
    }));
    return res.status(200).json({ success: true, staff: staffWithRoles });
  } catch (error) {
    console.error('Error fetching MySQL users:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching staff data',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
