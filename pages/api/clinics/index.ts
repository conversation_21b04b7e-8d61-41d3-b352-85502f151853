import { NextApiRequest, NextApiResponse } from 'next';

import admin from '@/utils/firebase-admin';
import { PermissionService } from '@/lib/services/permission-service';
import { FeatureKey, PermissionLevel, User, UserRole } from '@/models/auth';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

type ClinicRow = {
  id: string;
  name: string;
  address?: string | null;
  phone?: string | null;
  logoUrl?: string | null;
  createdAt?: string;
  updatedAt?: string;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' });
    }

    // Authenticate
    const user = (await verifyAuthAndGetUser(req)) as User | null;
    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // SUPER_ADMIN allowed without extra permission resolution; otherwise require PLATFORM_ADMIN:ADMIN
    if (user.role !== UserRole.SUPER_ADMIN) {
      const svc = new PermissionService();
      const level = await svc.resolveEffectiveLevel(user.id, FeatureKey.PLATFORM_ADMIN);
      if (level !== PermissionLevel.ADMIN) {
        return res.status(403).json({ message: 'Forbidden' });
      }
    }

    const db = admin.firestore();
    const snap = await db.collection('clinics').orderBy('name').get();
    const rows: ClinicRow[] = snap.docs.map(d => {
      const data = d.data() as Record<string, unknown>;
      return {
        id: d.id,
        name: String(data.name ?? ''),
        address: (data.address as string | null) ?? null,
        phone: (data.phone as string | null) ?? null,
        logoUrl: (data.logoUrl as string | null) ?? null,
        createdAt: data.createdAt ? String(data.createdAt) : undefined,
        updatedAt: data.updatedAt ? String(data.updatedAt) : undefined,
      };
    });

    return res.status(200).json(rows);
  } catch (error) {
    console.error('Error listing clinics:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
