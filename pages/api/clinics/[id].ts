import { NextApiRequest, NextApiResponse } from 'next';

import admin from '@/utils/firebase-admin';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { PermissionService } from '@/lib/services/permission-service';
import { FeatureKey, PermissionLevel, User, UserRole } from '@/models/auth';

type UpdateClinicRequest = {
  name?: string;
  address?: string | null;
  phone?: string | null;
  logoUrl?: string | null;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id } = req.query;
    if (!id || Array.isArray(id)) {
      return res.status(400).json({ message: 'Invalid clinic id' });
    }

    const user = (await verifyAuthAndGetUser(req)) as User | null;
    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // SUPER_ADMIN or PLATFORM_ADMIN(ADMIN) required
    const svc = new PermissionService();
    const level = await svc.resolveEffectiveLevel(user.id, FeatureKey.PLATFORM_ADMIN);
    if (user.role !== UserRole.SUPER_ADMIN && level !== PermissionLevel.ADMIN) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    const db = admin.firestore();
    const clinicRef = db.collection('clinics').doc(String(id));

    if (req.method === 'GET') {
      const doc = await clinicRef.get();
      if (!doc.exists) {
        return res.status(404).json({ message: 'Clinic not found' });
      }
      return res.status(200).json({ id: doc.id, ...doc.data() });
    }

    if (req.method === 'PUT') {
      const { name, address, phone, logoUrl }: UpdateClinicRequest = req.body || {};
      const updates: Record<string, unknown> = { updatedAt: new Date() };
      if (name !== undefined) {
        if (typeof name !== 'string' || name.trim().length === 0) {
          return res.status(400).json({ message: 'name must be a non-empty string' });
        }
        updates.name = name.trim();
      }
      if (address !== undefined) {
        if (address !== null && typeof address !== 'string') {
          return res.status(400).json({ message: 'address must be string or null' });
        }
        updates.address = address === null ? null : address.trim();
      }
      if (phone !== undefined) {
        if (phone !== null && typeof phone !== 'string') {
          return res.status(400).json({ message: 'phone must be string or null' });
        }
        updates.phone = phone === null ? null : phone.trim();
      }
      if (logoUrl !== undefined) {
        if (logoUrl !== null && typeof logoUrl !== 'string') {
          return res.status(400).json({ message: 'logoUrl must be string or null' });
        }
        updates.logoUrl = logoUrl === null ? null : logoUrl.trim();
      }

      await clinicRef.update(updates);
      const updated = await clinicRef.get();
      return res.status(200).json({ id: updated.id, ...updated.data() });
    }

    return res.status(405).json({ message: 'Method not allowed' });
  } catch (error) {
    console.error('Error in clinic detail API:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
