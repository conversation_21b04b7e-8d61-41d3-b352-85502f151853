import { NextApiRequest, NextApiResponse } from 'next';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { FeatureKey, PermissionLevel, UserRole } from '@/models/auth';
import { withPermission } from '@/lib/middleware/permission-middleware';
import { withAuthUser } from '@/lib/middleware/auth-user';
import { AuditRepository } from '@/lib/repositories/audit-repository';

// Initialize Firebase Admin SDK (server-side only)
if (!getApps().length) {
  initializeApp({
    credential: cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
  });
}

// Get admin Firestore instance
const db = getFirestore();

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Auth is ensured by withAuthUser; still verify token for admin creation activities
    const authHeader = req.headers.authorization || '';
    const idToken = authHeader.split('Bearer ')[1];
    const auth = getAuth();
    const decodedToken = idToken ? await auth.verifyIdToken(idToken) : { uid: 'unknown' };

    // Permission is enforced by wrapper (PLATFORM_ADMIN or CLINIC_CREATE at ADMIN via SUPER_ADMIN bypass)

    const {
      clinicName,
      clinicLogoUrl,
      clinicAddress,
      clinicPhone,
      adminEmail,
      adminPassword,
      adminName,
    } = req.body;

    // Validate required fields
    if (!clinicName || !adminEmail || !adminPassword || !adminName) {
      return res.status(400).json({
        success: false,
        message:
          'Missing required fields: clinicName, adminEmail, adminPassword, and adminName are required',
      });
    }

    // Create the clinic document
    const clinicRef = db.collection('clinics').doc();
    const clinicId = clinicRef.id;

    await clinicRef.set({
      name: clinicName,
      logoUrl: clinicLogoUrl || null,
      address: clinicAddress || null,
      phone: clinicPhone || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Create the clinic admin user
    let adminUser;
    try {
      // Check if user already exists
      adminUser = await auth.getUserByEmail(adminEmail);

      // User exists, update their role and clinic ID
      await auth.setCustomUserClaims(adminUser.uid, {
        role: UserRole.CLINIC_ADMIN,
        clinicId,
      });
    } catch (error: unknown) {
      console.error('Error creating clinic admin:', error);
      // User doesn't exist, create a new one
      adminUser = await auth.createUser({
        email: adminEmail,
        password: adminPassword,
        displayName: adminName,
      });

      // Set custom claims
      await auth.setCustomUserClaims(adminUser.uid, {
        role: UserRole.CLINIC_ADMIN,
        clinicId,
      });
    }

    // Create or update the admin user document
    await db.collection('users').doc(adminUser.uid).set({
      email: adminEmail,
      name: adminName,
      role: UserRole.CLINIC_ADMIN,
      clinicId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Audit log
    try {
      const auditRepo = new AuditRepository();
      await auditRepo.insert({
        actor_user_id: decodedToken.uid,
        action: 'CREATE_CLINIC',
        subject_id: clinicId,
        payload: { clinicName, adminEmail },
      });
    } catch (e) {
      console.warn('Failed to write audit log for CREATE_CLINIC', e);
    }

    return res.status(200).json({
      success: true,
      message: 'Clinic and admin created successfully',
      clinicId,
      adminId: adminUser.uid,
    });
  } catch (error: unknown) {
    console.error('Error creating clinic:', error);

    return res.status(500).json({
      success: false,
      message: 'Server error creating clinic',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

export default withAuthUser(
  withPermission(FeatureKey.CLINIC_CREATE, PermissionLevel.ADMIN)(handler),
);
