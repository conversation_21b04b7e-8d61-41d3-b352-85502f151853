import { useRouter } from 'next/router';
import { useEffect } from 'react';

export default function AcceptInviteRedirect() {
  const router = useRouter();
  useEffect(() => {
    if (!router.isReady) return;
    const code = typeof router.query.code === 'string' ? router.query.code : '';
    const email = typeof router.query.email === 'string' ? router.query.email : '';
    const params = new URLSearchParams();
    if (code) params.set('inviteCode', code);
    if (email) params.set('email', email);
    const target = `/login${params.toString() ? `?${params.toString()}` : ''}#register`;
    router.replace(target);
  }, [router, router.isReady, router.query.code, router.query.email]);
  return null;
}
