// Authentication-related types and interfaces

// User roles in the system
export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  CLINIC_ADMIN = 'CLINIC_ADMIN',
  STAFF = 'STAFF',
}

// RBAC feature keys and levels
export enum FeatureKey {
  BILLING = 'BILLING',
  ANSWERING_SERVICE = 'ANSWERING_SERVICE',
  STAFF_MANAGEMENT = 'STAFF_MANAGEMENT',
  CALL_LOGS = 'CALL_LOGS',
  DASHBOARD = 'DASHBOARD',
  NO_SHOW = 'NO_SHOW',
  BULK_SMS = 'BULK_SMS',
  PRACTICE_MANAGEMENT = 'PRACTICE_MANAGEMENT',
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  ADMIN_TOOLS = 'ADMIN_TOOLS',
  // Platform-level features (SUPER_ADMIN only)
  PLATFORM_ADMIN = 'PLATFORM_ADMIN',
  CLINIC_CREATE = 'CLINIC_CREATE',
  LOCATION_CREATE = 'LOCATION_CREATE',
  INVITE_ACCOUNT_OWNER = 'INVITE_ACCOUNT_OWNER',
}

export enum PermissionLevel {
  NONE = 'NONE',
  READ = 'READ',
  WRITE = 'WRITE',
  ADMIN = 'ADMIN',
}

export interface UserPreferences {
  isVoiceMailNotificationsEnabled?: boolean;
  isAppointmentNotificationsEnabled?: boolean;
  isIncomingCallNotificationsEnabled?: boolean;
  isUrgentAfterHoursConsultationEnabled?: boolean;
}

// User interface for authentication
export interface User {
  id: string;
  email: string;
  phone?: string;
  name: string;
  role: UserRole;
  specialty?: string;
  clinicId: number | null; // null for super-admin
  profilePicture?: string;
  canTakeAppointments: boolean; // Flag indicating if user can have appointments scheduled with them
  /** Array of accessible location IDs */
  locationIds?: string[]; // Made optional for backward compatibility
  /** Currently selected location ID */
  currentLocationId?: string;
  /** Optional array of practice IDs (derived from locations) */
  practiceIds?: string[];
  preferences?: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Helper methods for User access checking
 */
export class UserHelper {
  /**
   * Check if user has access to a specific location
   */
  static hasLocationAccess(user: User, locationId: string): boolean {
    return user.locationIds?.includes(locationId) || false;
  }

  /**
   * Get practice IDs from user's accessible locations
   * Note: This would typically require location data to derive practice IDs
   */
  static getPracticeIds(user: User, locations: { id: string; practiceId: string }[]): string[] {
    const practiceIds = new Set<string>();

    user.locationIds?.forEach(locationId => {
      const location = locations.find(loc => loc.id === locationId);
      if (location) {
        practiceIds.add(location.practiceId);
      }
    });

    return Array.from(practiceIds);
  }

  /**
   * Check if user has access to a specific location (alias for hasLocationAccess)
   */
  static hasAccessToLocation(user: User, locationId: string): boolean {
    return UserHelper.hasLocationAccess(user, locationId);
  }

  /**
   * Check if user has access to any location within a practice
   */
  static hasAccessToPractice(
    user: User,
    practiceId: string,
    locations: { id: string; practiceId: string }[],
  ): boolean {
    return (
      user.locationIds?.some(locationId => {
        const location = locations.find(loc => loc.id === locationId);
        return location?.practiceId === practiceId;
      }) || false
    );
  }
}

// Interface for clinic document
export interface Clinic {
  id: number;
  name: string;
  logoUrl?: string;
  address?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Interface for staff invite codes
export interface StaffInviteCode {
  id: string;
  code: string;
  clinicId: number;
  used: boolean;
  expiresAt: Date;
  createdBy: string; // user ID of the clinic admin
  createdAt: Date;
  usedAt?: Date;
  // New RBAC metadata
  assignedRoleId?: string;
  assignedLocationIds?: string[];
}

// Type for Firebase auth custom claims
export type CustomClaims = {
  role: UserRole;
  clinicId: number | null;
};
