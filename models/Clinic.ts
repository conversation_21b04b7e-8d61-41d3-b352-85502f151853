/**
 * @swagger
 * components:
 *   schemas:
 *     Clinic:
 *       type: object
 *       required:
 *         - id
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier
 *         practiceId:
 *           type: integer
 *           nullable: true
 *           description: Reference to parent practice
 *         name:
 *           type: string
 *           description: Name of the clinic
 *         description:
 *           type: string
 *           nullable: true
 *           description: Optional description of the clinic
 *         paymentLink:
 *           type: string
 *           nullable: true
 *           description: URL for payment processing
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * Represents a clinic entity
 * A clinic is a medical facility that can contain multiple practices and locations
 */
export interface Clinic {
  /** Unique identifier */
  id: string;
  /** Reference to parent practice */
  practiceId?: number;
  /** Name of the clinic */
  name: string;
  /** Optional description of the clinic */
  description?: string;
  /** URL for payment processing */
  paymentLink?: string;
  /** Creation timestamp */
  createdAt: Date;
  /** Last update timestamp */
  updatedAt: Date;
}
