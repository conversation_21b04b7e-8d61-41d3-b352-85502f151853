FIREBASE_PROJECT_ID="frontdesk-454309"
FIREBASE_CLIENT_EMAIL="<EMAIL>"
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# External API access
EXTERNAL_SERVICE_API_KEY="EftiRBYLAT3qMdnkUQ4cOsuP7U08nyiM8kUxmqfLoLdSVFhJtbWHS66K2DXPP8ce"

NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyAzX1UN0bDLYfN5ntjndql5uwWI2n1h7NQ"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="frontdesk-454309.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="frontdesk-454309"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="frontdesk-454309.firebasestorage.app"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="************"
NEXT_PUBLIC_FIREBASE_APP_ID="1:************:web:f940398aeb1b1261c31cc7"
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID="G-LXDYPBS7TT"

# Nextech API Credentials
NEXTECH_GROUP_ID="d898cfbe-8dbd-445c-8a32-71300b731a1d"
NEXTECH_CLIENT_ID="2390b7ae-ea13-4adf-89b4-d09e9b8930c8"
NEXTECH_CLIENT_SECRET="****************************************"
NEXTECH_RESOURCE="3676d991-6539-461e-b2cc-d38a1fe63f30"
NEXTECH_BASE_URL="https://api.pm.nextech.com/api"
NEXTECH_PRACTICE_ID="d898cfbe-8dbd-445c-8a32-71300b731a1d"

# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=c56f6109161c01977d38d6e37cc034ef
TWILIO_PHONE_NUMBER=+***********

# GCP Conversational Agent
GCP_PROJECT_ID="frontdesk-454309"
GCP_LOCATION_ID="global"
GCP_AGENT_ID="edcb5e7c-94dc-4871-9a22-c109f6014f69"
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GCP_SERVICE_ACCOUNT_CLIENT_EMAIL="<EMAIL>"
GCP_AUDIO_BUCKET_NAME="conversational-agent-heather-uretina-v2"

# MySQL DEV Database Connection
# MYSQL_HOST=************
# MYSQL_PORT=3306
# MYSQL_USER=root
# MYSQL_PASSWORD=uS40UqTCjKsN50ZEANrN
# MYSQL_DATABASE=frontdesk
# MYSQL_SSL=false

# MySQL PROD Database Connection
MYSQL_HOST=***********
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=AN0Z4NUC0uKqRu5jSETS
MYSQL_DATABASE=frontdesk
MYSQL_SSL=false

