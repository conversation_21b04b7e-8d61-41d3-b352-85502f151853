import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

type RequestWithAuth = NextApiRequest & {
  user?: { id: string };
  auth?: { userId: string };
};

export function withAuth<PERSON>ser(handler: NextApiHandler): NextApiHandler {
  return async function wrapped(req: NextApiRequest, res: NextApiResponse) {
    const user = await verifyAuthAndGetUser(req);
    if (!user) return res.status(401).json({ message: 'Unauthorized' });
    const r = req as RequestWithAuth;
    r.user = { id: user.id } as { id: string };
    r.auth = { userId: user.id };
    return handler(req, res);
  };
}
