import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';
import { FeatureKey, PermissionLevel } from '@/models/auth';
import { PermissionService } from '../services/permission-service';
import { ensureDbInitialized } from './db-init';

export function withPermission(
  feature: FeatureKey,
  minLevel: PermissionLevel,
  opts?: { locationFrom?: 'query' | 'body' | 'header'; locationHeaderKey?: string },
) {
  const svc = new PermissionService();
  return function wrap(handler: NextApiHandler): NextApiHandler {
    return async function wrapped(req: NextApiRequest, res: NextApiResponse) {
      try {
        // Ensure MySQL pool is ready before checking permissions
        // In test environment, skip initialization errors to allow unit tests to run without a DB
        try {
          await ensureDbInitialized();
        } catch (error) {
          if (process.env.NODE_ENV !== 'test') {
            throw error;
          }
        }

        type ReqWithAuth = NextApiRequest & {
          user?: { id?: string };
          auth?: { userId?: string };
          userId?: string;
        };
        const r = req as ReqWithAuth;
        const userId = r.user?.id || r.auth?.userId || r.userId;
        if (!userId) {
          return res.status(401).json({ message: 'Unauthorized' });
        }

        let locationId: string | undefined;
        const source = opts?.locationFrom;
        if (source === 'query') locationId = (req.query.locationId as string) || undefined;
        if (source === 'body') {
          const body = req.body as unknown;
          if (
            body &&
            typeof body === 'object' &&
            'locationId' in (body as Record<string, unknown>) &&
            typeof (body as { locationId?: unknown }).locationId === 'string'
          ) {
            locationId = (body as { locationId?: string }).locationId;
          }
        }
        if (source === 'header') {
          const headerKey = (opts?.locationHeaderKey || 'x-location-id').toLowerCase();
          const headerVal = req.headers[headerKey];
          locationId = (Array.isArray(headerVal) ? headerVal[0] : headerVal) as string | undefined;
        }

        const level = await svc.resolveEffectiveLevel(userId, feature, locationId);
        const ok = rank(level) >= rank(minLevel);
        if (!ok) {
          // Structured denial log
          console.warn(
            JSON.stringify({
              type: 'permission_denied',
              userId,
              feature,
              required: minLevel,
              actual: level,
              locationId: locationId ?? null,
              path: req.url,
              method: req.method,
            }),
          );
          return res
            .status(403)
            .json({ message: 'Forbidden', feature, required: minLevel, actual: level });
        }

        return handler(req, res);
      } catch (e) {
        console.error('Permission middleware error', e);
        return res.status(500).json({ message: 'Internal Server Error' });
      }
    };
  };
}

function rank(level: PermissionLevel): number {
  switch (level) {
    case PermissionLevel.NONE:
      return 0;
    case PermissionLevel.READ:
      return 1;
    case PermissionLevel.WRITE:
      return 2;
    case PermissionLevel.ADMIN:
      return 3;
    default:
      return 0;
  }
}
