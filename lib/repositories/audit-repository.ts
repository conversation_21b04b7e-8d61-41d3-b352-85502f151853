import { mysqlService } from '../database/mysql-service';

export type AuditLogRecord = {
  id?: number;
  actor_user_id: string;
  action: string;
  subject_id?: string | null;
  payload?: unknown;
  created_at?: Date;
};

export class AuditRepository {
  async insert(log: AuditLogRecord): Promise<void> {
    await mysqlService.query(
      `INSERT INTO role_audit_logs (actor_user_id, action, subject_id, payload) VALUES (?, ?, ?, ?)`,
      [log.actor_user_id, log.action, log.subject_id ?? null, JSON.stringify(log.payload ?? null)],
    );
  }
}
