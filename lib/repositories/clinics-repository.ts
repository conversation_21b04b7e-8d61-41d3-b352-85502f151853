import { BaseRepository } from '../database/base-repository';
import { Clinic } from '@/models/Clinic';
import { PaginatedResult } from '../database/unit-of-work';

/**
 * Clinics Repository - handles dual-database operations for Clinic entities
 */
export class ClinicsRepository extends BaseRepository<Clinic> {
  protected tableName = 'clinics';
  protected collectionName = 'clinics';

  constructor() {
    super('clinics', 'clinics', {
      id: 'uuid',
      practiceId: 'practice_id',
      name: 'name',
      description: 'description',
      paymentLink: 'payment_link',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Schema mapping for MySQL columns
   */
  protected getSchemaMapping(): Record<string, string> {
    return {
      id: 'uuid',
      practiceId: 'practice_id',
      name: 'name',
      description: 'description',
      paymentLink: 'payment_link',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    };
  }

  /**
   * Entity validation
   */
  public validateEntity(entity: Partial<Clinic>): boolean {
    const errors: string[] = [];

    if (!entity.id || entity.id.trim().length === 0) {
      errors.push('Clinic ID is required');
    }

    if (!entity.name || entity.name.trim().length === 0) {
      errors.push('Clinic name is required');
    }

    if (entity.paymentLink !== undefined && entity.paymentLink) {
      try {
        new URL(entity.paymentLink);
      } catch {
        errors.push('Payment link must be a valid URL');
      }
    }

    if (errors.length > 0) {
      throw new Error(`Clinic validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Convert MySQL row to Clinic entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): Clinic {
    let createdAt = new Date();
    if (row.created_at) {
      if (row.created_at instanceof Date) {
        createdAt = row.created_at;
      } else if (typeof row.created_at === 'string') {
        createdAt = new Date(row.created_at);
      }
    }

    let updatedAt = new Date();
    if (row.updated_at) {
      if (row.updated_at instanceof Date) {
        updatedAt = row.updated_at;
      } else if (typeof row.updated_at === 'string') {
        updatedAt = new Date(row.updated_at);
      }
    }

    return {
      id: row.uuid as string,
      practiceId: row.practice_id ? Number(row.practice_id) : undefined,
      name: row.name as string,
      description: (row.description as string) || undefined,
      paymentLink: (row.payment_link as string) || undefined,
      createdAt,
      updatedAt,
    };
  }

  /**
   * Convert Clinic entity to MySQL data
   */
  protected entityToMysqlData(entity: Clinic): Record<string, unknown> {
    return {
      uuid: entity.id,
      practice_id: entity.practiceId || null,
      name: entity.name,
      description: entity.description || null,
      payment_link: entity.paymentLink || null,
      created_at: entity.createdAt || new Date(),
      updated_at: entity.updatedAt || new Date(),
    };
  }

  /**
   * Clinic-specific query methods
   */

  /**
   * Find clinic by ID (which maps to database uuid)
   */
  async findById(id: string): Promise<Clinic | null> {
    const result = await this.findMany({ where: { id }, limit: 1 });
    return result.items[0] || null;
  }

  /**
   * Find clinics by practice ID
   */
  async findByPracticeId(practiceId: number): Promise<PaginatedResult<Clinic>> {
    return this.findMany({
      where: { practiceId },
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
  }

  /**
   * Find clinics by name (exact match)
   */
  async findByName(name: string): Promise<PaginatedResult<Clinic>> {
    return this.findMany({
      where: { name },
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
  }

  /**
   * Check if clinic name exists (for validation)
   */
  async isNameUnique(name: string, excludeId?: string): Promise<boolean> {
    // Note: Current implementation has limitations - it will only check for exact name matches
    // For full validation, this would need custom SQL implementation
    const result = await this.findMany({ where: { name }, limit: 1 });
    if (result.items.length === 0) {
      return true;
    }

    // If we found a clinic with the same name, check if it's the same one we're updating
    if (excludeId && result.items[0].id === excludeId) {
      return true;
    }

    return false;
  }

  /**
   * Check if clinic ID exists (for validation)
   */
  async isIdUnique(id: string, excludeId?: string): Promise<boolean> {
    // Note: Current implementation has limitations - it will only check for exact ID matches
    // For full validation, this would need custom SQL implementation
    const result = await this.findMany({ where: { id }, limit: 1 });
    if (result.items.length === 0) {
      return true;
    }

    // If we found a clinic with the same ID, check if it's the same one we're updating
    if (excludeId && result.items[0].id === excludeId) {
      return true;
    }

    return false;
  }
}
