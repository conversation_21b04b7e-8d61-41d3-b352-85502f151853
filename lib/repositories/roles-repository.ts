import { mysqlService } from '../database/mysql-service';

export type RoleRecord = {
  id: string;
  clinic_id: number | null;
  name: string;
  description: string | null;
  is_system: boolean;
  is_template: boolean;
  created_by: string;
  created_at: Date;
  updated_at: Date;
};

export class RolesRepository {
  async create(role: Omit<RoleRecord, 'created_at' | 'updated_at'>): Promise<RoleRecord> {
    const sql = `INSERT INTO roles (id, clinic_id, name, description, is_system, is_template, created_by)
                 VALUES (?, ?, ?, ?, ?, ?, ?)`;
    await mysqlService.query(sql, [
      role.id,
      role.clinic_id,
      role.name,
      role.description,
      role.is_system,
      role.is_template,
      role.created_by,
    ]);
    return (await this.findById(role.id)) as RoleRecord;
  }

  async findById(id: string): Promise<RoleRecord | null> {
    const sql = `SELECT * FROM roles WHERE id = ? LIMIT 1`;
    const rows = await mysqlService.query<RoleRecord>(sql, [id]);
    return rows[0] || null;
  }

  async findByClinic(clinicId: number | null): Promise<RoleRecord[]> {
    if (clinicId === null) {
      const sql = `SELECT * FROM roles WHERE clinic_id IS NULL`;
      return mysqlService.query<RoleRecord>(sql, []);
    }
    const sql = `SELECT * FROM roles WHERE clinic_id = ? OR clinic_id IS NULL`;
    return mysqlService.query<RoleRecord>(sql, [clinicId]);
  }

  async update(
    id: string,
    updates: Partial<Pick<RoleRecord, 'name' | 'description' | 'is_template'>>,
  ): Promise<RoleRecord | null> {
    const sets: string[] = [];
    const params: unknown[] = [];
    if (updates.name !== undefined) {
      sets.push('name = ?');
      params.push(updates.name);
    }
    if (updates.description !== undefined) {
      sets.push('description = ?');
      params.push(updates.description);
    }
    if (updates.is_template !== undefined) {
      sets.push('is_template = ?');
      params.push(updates.is_template);
    }
    if (sets.length === 0) return this.findById(id);
    const sql = `UPDATE roles SET ${sets.join(', ')} WHERE id = ?`;
    params.push(id);
    await mysqlService.query(sql, params);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    const sql = `DELETE FROM roles WHERE id = ?`;
    await mysqlService.query(sql, [id]);
  }

  async findByIds(ids: string[]): Promise<RoleRecord[]> {
    if (ids.length === 0) return [];
    const placeholders = ids.map(() => '?').join(',');
    const sql = `SELECT * FROM roles WHERE id IN (${placeholders})`;
    return mysqlService.query<RoleRecord>(sql, ids);
  }
}
