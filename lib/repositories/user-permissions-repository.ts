import { mysqlService } from '../database/mysql-service';
import { FeatureKey, PermissionLevel } from '@/models/auth';

export type UserRoleRecord = {
  id: number;
  user_id: string;
  role_id: string;
  location_id: string | null;
};

export type UserOverrideRecord = {
  id: number;
  user_id: string;
  feature: FeatureKey;
  level: PermissionLevel;
  location_id: string | null;
  expires_at: Date | null;
  created_at: Date;
};

export class UserPermissionsRepository {
  async getUserRoles(userId: string, locationId: string | null): Promise<UserRoleRecord[]> {
    const sql = `SELECT id, user_id, role_id, location_id FROM user_roles
                 WHERE user_id = ? AND (location_id <=> ?)`; // NULL-safe equality
    return mysqlService.query<UserRoleRecord>(sql, [userId, locationId]);
  }

  async getUserRolesGlobal(userId: string): Promise<UserRoleRecord[]> {
    const sql = `SELECT id, user_id, role_id, location_id FROM user_roles
                 WHERE user_id = ? AND location_id IS NULL`;
    return mysqlService.query<UserRoleRecord>(sql, [userId]);
  }

  async getUserOverrides(
    userId: string,
    feature: FeatureKey,
    locationId: string | null,
    now: Date = new Date(),
  ): Promise<UserOverrideRecord[]> {
    const sql = `SELECT * FROM user_permission_overrides
                 WHERE user_id = ? AND feature = ?
                   AND (location_id <=> ?)
                   AND (expires_at IS NULL OR expires_at > ?)`;
    return mysqlService.query<UserOverrideRecord>(sql, [userId, feature, locationId, now]);
  }

  async listOverridesForUser(userId: string): Promise<UserOverrideRecord[]> {
    const sql = `SELECT * FROM user_permission_overrides WHERE user_id = ?`;
    return mysqlService.query<UserOverrideRecord>(sql, [userId]);
  }

  async listUserRoleIds(userId: string, locationId: string | null = null): Promise<string[]> {
    const sql = `SELECT role_id FROM user_roles WHERE user_id = ? AND (location_id <=> ?)`;
    const rows = await mysqlService.query<{ role_id: string }>(sql, [userId, locationId]);
    return rows.map(r => r.role_id);
  }

  async replaceUserRoles(
    userId: string,
    roleIds: string[],
    locationId: string | null = null,
  ): Promise<void> {
    // Remove existing roles for scope
    const deleteSql = `DELETE FROM user_roles WHERE user_id = ? AND (location_id <=> ?)`;
    await mysqlService.query(deleteSql, [userId, locationId]);

    if (roleIds.length === 0) return;

    // Insert new roles
    const valuesSql = roleIds.map(() => '(?, ?, ?)').join(',');
    const insertSql = `INSERT INTO user_roles (user_id, role_id, location_id) VALUES ${valuesSql}`;
    const params: unknown[] = [];
    roleIds.forEach(rid => {
      params.push(userId, rid, locationId);
    });
    await mysqlService.query(insertSql, params);
  }

  async getRolesForUsers(
    userIds: string[],
  ): Promise<Array<{ user_id: string; role_id: string; role_name: string }>> {
    if (userIds.length === 0) return [];
    const placeholders = userIds.map(() => '?').join(',');
    const sql = `SELECT ur.user_id, r.id AS role_id, r.name AS role_name
                 FROM user_roles ur
                 JOIN roles r ON r.id = ur.role_id
                 WHERE ur.user_id IN (${placeholders})`;
    return mysqlService.query<{ user_id: string; role_id: string; role_name: string }>(
      sql,
      userIds,
    );
  }

  async upsertOverride(params: {
    userId: string;
    feature: FeatureKey;
    level: PermissionLevel;
    locationId?: string | null;
    expiresAt?: Date | null;
  }): Promise<void> {
    const sql = `INSERT INTO user_permission_overrides (user_id, feature, level, location_id, expires_at)
                 VALUES (?, ?, ?, ?, ?)
                 ON DUPLICATE KEY UPDATE level = VALUES(level), expires_at = VALUES(expires_at)`;
    await mysqlService.query(sql, [
      params.userId,
      params.feature,
      params.level,
      params.locationId ?? null,
      params.expiresAt ?? null,
    ]);
  }
}
