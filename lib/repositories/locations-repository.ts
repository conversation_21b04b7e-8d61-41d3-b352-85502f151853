import { BaseRepository } from '../database/base-repository';
import { Location } from '../../models/Location';
import { DualDatabaseUtils } from '../database/dual-database-service';
import * as admin from 'firebase-admin';

/**
 * Location entity repository with dual-database support
 * Handles MySQL-first reads with Firestore fallback
 */
export class LocationsRepository extends BaseRepository<Location> {
  constructor() {
    super('locations', 'locations', {
      id: 'id',
      clinicId: 'clinic_id',
      practiceId: 'practice_id',
      name: 'name',
      address: 'address',
      phone: 'phone',
      timeZone: 'time_zone',
      isActive: 'is_active',
      practiceName: 'practice_name',
      officeHours: 'office_hours', // JSON field
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: Location): Record<string, unknown> {
    return {
      clinicId: entity.clinicId,
      practiceId: entity.practiceId,
      name: entity.name,
      address: entity.address,
      phone: entity.phone || null,
      timeZone: entity.timeZone,
      isActive: entity.isActive,
      practiceName: entity.practiceName,
      officeHours: entity.officeHours || {},
      createdAt:
        entity.createdAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.createdAt)
          : admin.firestore.Timestamp.now(),
      updatedAt:
        entity.updatedAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.updatedAt)
          : admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): Location {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      clinicId: data.clinicId as number,
      practiceId: data.practiceId as string,
      name: data.name as string,
      address: data.address as string,
      phone: data.phone as string | undefined,
      timeZone: data.timeZone as string,
      isActive: data.isActive as boolean,
      practiceName: data.practiceName as string,
      officeHours: data.officeHours as
        | Record<string, { start: string; end: string } | null>
        | undefined,
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
      updatedAt: DualDatabaseUtils.timestampToDate(data.updatedAt) || new Date(),
    };
  }

  /**
   * Convert MySQL row data to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): Location {
    const createdAt = row.created_at;
    const updatedAt = row.updated_at;

    let parsedCreatedAt: Date;
    let parsedUpdatedAt: Date;

    if (createdAt instanceof Date) {
      parsedCreatedAt = createdAt;
    } else if (typeof createdAt === 'string') {
      parsedCreatedAt = new Date(createdAt);
    } else {
      parsedCreatedAt = new Date();
    }

    if (updatedAt instanceof Date) {
      parsedUpdatedAt = updatedAt;
    } else if (typeof updatedAt === 'string') {
      parsedUpdatedAt = new Date(updatedAt);
    } else {
      parsedUpdatedAt = new Date();
    }

    // Accept either `id` or `uuid` for the primary key
    const rowId = (row.id || (row as Record<string, unknown>).uuid) as string;

    return {
      id: rowId,
      clinicId: row.clinic_id as number,
      practiceId: row.practice_id as string,
      name: row.name as string,
      address: row.address as string,
      phone: row.phone as string | undefined,
      timeZone: row.time_zone as string,
      isActive: Boolean(row.is_active),
      practiceName: (row.practice_name as string) || '',
      officeHours: this.parseJsonField(row.office_hours, undefined),
      createdAt: parsedCreatedAt,
      updatedAt: parsedUpdatedAt,
    };
  }

  /**
   * Custom validation for Location entities
   */
  protected customValidateEntity(entity: Partial<Location>): boolean {
    const errors: string[] = [];

    if (!entity.clinicId || entity.clinicId <= 0) {
      errors.push('Valid clinic ID is required');
    }

    if (!entity.practiceId?.trim()) {
      errors.push('Practice ID is required');
    }

    if (!entity.name?.trim()) {
      errors.push('Location name is required');
    }

    if (!entity.address?.trim()) {
      errors.push('Address is required');
    }

    if (!entity.timeZone?.trim()) {
      errors.push('Time zone is required');
    }

    if (entity.isActive === undefined || entity.isActive === null) {
      errors.push('Active status is required');
    }

    if (errors.length > 0) {
      throw new Error(`Location validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Parse JSON field helper
   */
  private parseJsonField<T>(value: string | unknown, defaultValue: T): T {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value) as T;
      } catch {
        return defaultValue;
      }
    }
    // If the value is already an object (e.g., MySQL JSON column automatically parsed), return it directly
    if (typeof value === 'object' && value !== null) {
      return value as T;
    }
    return defaultValue;
  }

  /**
   * Find locations by clinic ID
   */
  async findByClinicId(clinicId: number, limit = 100): Promise<Location[]> {
    const result = await this.findMany({
      where: { clinicId },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find locations by practice ID
   */
  async findByPracticeId(practiceId: string, limit = 100): Promise<Location[]> {
    const result = await this.findMany({
      where: { practiceId },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find active locations
   */
  async findActive(limit = 100): Promise<Location[]> {
    const result = await this.findMany({
      where: { isActive: true },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find active locations by clinic
   */
  async findActiveByClinic(clinicId: number, limit = 100): Promise<Location[]> {
    const result = await this.findMany({
      where: { clinicId, isActive: true },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find active locations by practice
   */
  async findActiveByPractice(practiceId: string, limit = 100): Promise<Location[]> {
    const result = await this.findMany({
      where: { practiceId, isActive: true },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find locations by time zone
   */
  async findByTimeZone(timeZone: string, limit = 100): Promise<Location[]> {
    const result = await this.findMany({
      where: { timeZone },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Search locations by name (partial match)
   */
  async searchByName(name: string, limit = 20): Promise<Location[]> {
    // Get locations and filter in memory for now
    const result = await this.findMany({
      where: {},
      limit: limit * 5, // Get more to filter
      orderBy: [{ field: 'name', direction: 'asc' }],
    });

    return result.items
      .filter(location => location.name.toLowerCase().includes(name.toLowerCase()))
      .slice(0, limit);
  }

  /**
   * Search locations by address (partial match)
   */
  async searchByAddress(address: string, limit = 20): Promise<Location[]> {
    // Get locations and filter in memory for now
    const result = await this.findMany({
      where: {},
      limit: limit * 5, // Get more to filter
      orderBy: [{ field: 'name', direction: 'asc' }],
    });

    return result.items
      .filter(location => location.address.toLowerCase().includes(address.toLowerCase()))
      .slice(0, limit);
  }

  /**
   * Update location active status
   */
  async updateActiveStatus(locationId: string, isActive: boolean): Promise<Location> {
    return this.update(locationId, { isActive });
  }

  /**
   * Update location office hours
   */
  async updateOfficeHours(
    locationId: string,
    officeHours: Record<string, { start: string; end: string } | null>,
  ): Promise<Location> {
    return this.update(locationId, { officeHours });
  }

  /**
   * Add or update office hours for a specific day
   */
  async setDayOfficeHours(
    locationId: string,
    dayOfWeek: string,
    hours: { start: string; end: string } | null,
  ): Promise<Location> {
    const location = await this.findById(locationId);
    if (!location) {
      throw new Error(`Location not found: ${locationId}`);
    }

    const currentOfficeHours = location.officeHours || {};
    const updatedOfficeHours = {
      ...currentOfficeHours,
      [dayOfWeek]: hours,
    };

    return this.update(locationId, { officeHours: updatedOfficeHours });
  }

  /**
   * Get location names map for a list of location IDs
   * @param locationIds Array of unique location IDs
   * @returns Map of locationId to locationName
   */
  async getLocationNamesMap(locationIds: string[]): Promise<Map<string, string>> {
    const locationNameMap = new Map<string, string>();

    if (locationIds.length === 0) {
      return locationNameMap;
    }

    try {
      const locations = await Promise.all(
        locationIds.map(async locationId => {
          try {
            const location = await this.findById(locationId);
            return { locationId, name: location?.name };
          } catch (error) {
            console.warn(`Failed to fetch location name for locationId ${locationId}:`, error);
            return { locationId, name: undefined };
          }
        }),
      );

      // Populate the map with location names
      locations.forEach(({ locationId, name }) => {
        if (name) {
          locationNameMap.set(locationId, name);
        }
      });
    } catch (error) {
      console.error('Failed to fetch location names in batch:', error);
    }

    return locationNameMap;
  }

  /**
   * Get location statistics
   */
  async getLocationStats(): Promise<{
    totalLocations: number;
    activeLocations: number;
    inactiveLocations: number;
    locationsByPractice: Record<string, number>;
    locationsByTimeZone: Record<string, number>;
  }> {
    const locations = await this.findMany({
      where: {},
      limit: 1000,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });

    const totalLocations = locations.items.length;
    const activeLocations = locations.items.filter(loc => loc.isActive).length;
    const inactiveLocations = totalLocations - activeLocations;

    // Group by practice
    const locationsByPractice: Record<string, number> = {};
    locations.items.forEach(location => {
      const practice = location.practiceName || 'Unknown';
      locationsByPractice[practice] = (locationsByPractice[practice] || 0) + 1;
    });

    // Group by time zone
    const locationsByTimeZone: Record<string, number> = {};
    locations.items.forEach(location => {
      const timeZone = location.timeZone || 'Unknown';
      locationsByTimeZone[timeZone] = (locationsByTimeZone[timeZone] || 0) + 1;
    });

    return {
      totalLocations,
      activeLocations,
      inactiveLocations,
      locationsByPractice,
      locationsByTimeZone,
    };
  }

  /**
   * Get locations with their user count
   */
  async getLocationsWithUserCount(): Promise<Array<Location & { userCount: number }>> {
    const locations = await this.findActive(1000);

    // For now, return locations with userCount as 0
    // TODO: Implement proper user counting when user repository is integrated
    return locations.map(location => ({
      ...location,
      userCount: 0,
    }));
  }

  /**
   * Validate office hours format
   */
  private isValidOfficeHours(
    officeHours: Record<string, { start: string; end: string } | null>,
  ): boolean {
    const validDays = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;

    for (const [day, hours] of Object.entries(officeHours)) {
      if (!validDays.includes(day.toLowerCase())) {
        return false;
      }

      if (hours !== null) {
        if (!hours.start || !hours.end) {
          return false;
        }

        if (!timeRegex.test(hours.start) || !timeRegex.test(hours.end)) {
          return false;
        }
      }
    }

    return true;
  }
}
