import { mysqlService } from '../database/mysql-service';
import { FeatureKey, PermissionLevel } from '@/models/auth';

export type RolePermissionRecord = {
  id: number;
  role_id: string;
  feature: FeatureKey;
  level: PermissionLevel;
  created_at: Date;
};

export class PermissionsRepository {
  async upsertRolePermissions(
    roleId: string,
    grants: Array<{ feature: FeatureKey; level: PermissionLevel }>,
  ): Promise<void> {
    // Simple approach: delete and insert to keep consistent
    await mysqlService.query('DELETE FROM role_permissions WHERE role_id = ?', [roleId]);
    if (grants.length === 0) return;
    const valuesSql = grants.map(() => '(?, ?, ?)').join(',');
    const params: unknown[] = [];
    grants.forEach(g => {
      params.push(roleId, g.feature, g.level);
    });
    await mysqlService.query(
      `INSERT INTO role_permissions (role_id, feature, level) VALUES ${valuesSql}`,
      params,
    );
  }

  async getRolePermissions(roleIds: string[]): Promise<RolePermissionRecord[]> {
    if (roleIds.length === 0) return [];
    const placeholders = roleIds.map(() => '?').join(',');
    const sql = `SELECT * FROM role_permissions WHERE role_id IN (${placeholders})`;
    return mysqlService.query<RolePermissionRecord>(sql, roleIds);
  }
}
