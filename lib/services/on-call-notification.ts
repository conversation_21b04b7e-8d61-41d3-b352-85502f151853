import admin from 'firebase-admin';
import { smsService } from './sms-service';
import { OnCallSchedule } from '@/models/OnCallSchedule';
import { OnCallNotification, NotificationStatus } from '@/models/OnCallNotification';
import { CallType, getCallTypeName } from '@/models/CallTypes';
import logger from '../external-api/v2/utils/logger';
import { ON_CALL_NOTIFICATION_CONFIG } from '@/app-config';
import { AfterHoursCall } from '@/models/AfterHoursCall';
import { AppLinkBuilder } from '@/utils/app-link.builder';

/**
 * Interface for notification data
 */
export interface OnCallNotificationData {
  schedule: OnCallSchedule;
  callInfo: {
    sessionId: string;
    callerPhone?: string;
    callType: CallType;
    triggerEvent?: string;
    locationName?: string;
  };
}

/**
 * Interface for notification configuration
 */
export interface NotificationConfig {
  enabled: boolean;
  afterHoursEnabled: boolean;
  delayMinutes: number;
  maxRetries: number;
  cooldownMinutes: number;
  allowedCallTypes: CallType[];
}

/**
 * Service for handling on-call doctor SMS notifications
 */
export class OnCallNotificationService {
  private static readonly COLLECTION_NAME = 'on-call-notifications';
  private static get db() {
    return admin.firestore();
  }

  /**
   * Send SMS notification to on-call doctor
   */
  static async notifyOnCallDoctor(data: OnCallNotificationData): Promise<string> {
    try {
      // Check if notifications are enabled
      if (!this.isNotificationEnabled()) {
        logger.info('On-call notifications are disabled');
        return 'disabled';
      }

      // Check rate limiting and cooldown
      if (await this.isRateLimited(data.schedule.doctorId)) {
        logger.warn(`Rate limited for doctor ${data.schedule.doctorId}`);
        return 'rate_limited';
      }

      // Ensure doctor's phone number is present
      if (!data.schedule.doctorPhone) {
        throw new Error(`Missing phone number for on-call doctor ${data.schedule.doctorId}`);
      }

      // Build notification message
      const message = this.buildNotificationMessage(data);

      // Send SMS via Twilio
      const messageSid = await smsService.sendSms(data.schedule.doctorPhone, message);

      // Log the notification
      await this.logNotification({
        scheduleId: data.schedule.id,
        callSessionId: data.callInfo.sessionId,
        doctorId: data.schedule.doctorId,
        clinicId: data.schedule.clinicId,
        notificationTime: new Date(),
        smsMessageId: messageSid,
        status: 'sent',
        callType: data.callInfo.callType.toString(),
        callerPhone: data.callInfo.callerPhone,
        retryCount: 0,
      });

      logger.info(
        {
          messageSid,
          doctorId: data.schedule.doctorId,
          sessionId: data.callInfo.sessionId,
        },
        'On-call notification sent successfully',
      );

      return messageSid;
    } catch (error) {
      // Log failed notification
      await this.logNotification({
        scheduleId: data.schedule.id,
        callSessionId: data.callInfo.sessionId,
        doctorId: data.schedule.doctorId,
        clinicId: data.schedule.clinicId,
        notificationTime: new Date(),
        status: 'failed',
        callType: data.callInfo.callType.toString(),
        callerPhone: data.callInfo.callerPhone,
        errorMessage: error instanceof Error ? error.message : String(error),
        retryCount: 0,
      });

      logger.error(
        {
          error,
          doctorId: data.schedule.doctorId,
          sessionId: data.callInfo.sessionId,
        },
        'Failed to send on-call notification',
      );

      throw error;
    }
  }

  /**
   * Send SMS notification to on-call doctor for after-hours call
   */
  static async notifyOnCallDoctorForAfterHoursCall(
    data: OnCallNotificationData,
    afterHoursCall: AfterHoursCall,
  ): Promise<string> {
    try {
      // Check if notifications are enabled
      if (!this.isAfterHoursNotificationEnabled()) {
        logger.info('After-hours notifications are disabled');
        return 'disabled';
      }

      // Check rate limiting and cooldown
      // if (await this.isRateLimited(data.schedule.doctorId)) {
      //   logger.warn(`Rate limited for doctor ${data.schedule.doctorId}`);
      //   return 'rate_limited';
      // }

      // Ensure doctor's phone number is present
      if (!data.schedule.doctorPhone) {
        throw new Error(`Missing phone number for on-call doctor ${data.schedule.doctorId}`);
      }

      // Build notification message
      const message = this.buildAfterHoursNotificationMessageForDoctor(
        afterHoursCall,
        data.schedule.doctorId,
      );

      // Send SMS via Twilio
      const messageSid = await smsService.sendSms(data.schedule.doctorPhone, message);

      // Log the notification
      await this.logNotification({
        scheduleId: data.schedule.id,
        callSessionId: data.callInfo.sessionId,
        doctorId: data.schedule.doctorId,
        clinicId: data.schedule.clinicId,
        notificationTime: new Date(),
        smsMessageId: messageSid,
        status: 'sent',
        callType: data.callInfo.callType.toString(),
        callerPhone: data.callInfo.callerPhone,
        retryCount: 0,
      });

      logger.info(
        {
          messageSid,
          doctorId: data.schedule.doctorId,
          sessionId: data.callInfo.sessionId,
        },
        'On-call notification sent successfully',
      );

      return messageSid;
    } catch (error) {
      // Log failed notification
      await this.logNotification({
        scheduleId: data.schedule.id,
        callSessionId: data.callInfo.sessionId,
        doctorId: data.schedule.doctorId,
        clinicId: data.schedule.clinicId,
        notificationTime: new Date(),
        status: 'failed',
        callType: data.callInfo.callType.toString(),
        callerPhone: data.callInfo.callerPhone,
        errorMessage: error instanceof Error ? error.message : String(error),
        retryCount: 0,
      });

      logger.error(
        {
          error,
          doctorId: data.schedule.doctorId,
          sessionId: data.callInfo.sessionId,
        },
        'Failed to send on-call notification for after-hours call',
      );

      throw error;
    }
  }

  /**
   * Send SMS notification to on-call doctor for after-hours call
   */
  static async notifyPatientForAfterHoursCall(
    data: OnCallNotificationData,
    afterHoursCall: AfterHoursCall,
  ): Promise<string> {
    try {
      // Check if notifications are enabled
      if (!this.isAfterHoursNotificationEnabled()) {
        logger.info('After-hours notifications are disabled');
        return 'disabled';
      }

      // Ensure patient's phone number is present
      if (!afterHoursCall.patientPhoneNumber) {
        throw new Error(
          `Missing phone number for patient for after-hours call ${afterHoursCall.id}`,
        );
      }

      // Build notification message
      const message = this.buildAfterHoursNotificationMessageForPatient();

      // Send SMS via Twilio
      const messageSid = await smsService.sendSms(afterHoursCall.patientPhoneNumber, message);

      logger.info(
        {
          messageSid,
          patientPhoneNumber: afterHoursCall.patientPhoneNumber,
          sessionId: data.callInfo.sessionId,
        },
        'After-hours call notification for patient sent successfully',
      );

      return messageSid;
    } catch (error) {
      logger.error(
        {
          error,
          patientPhoneNumber: afterHoursCall.patientPhoneNumber,
          sessionId: data.callInfo.sessionId,
        },
        'Failed to send after-hours call notification for patient',
      );

      throw error;
    }
  }

  /**
   * Build SMS notification message
   * Uses location timezone from database for time display
   */
  private static buildNotificationMessage(data: OnCallNotificationData): string {
    const { schedule, callInfo } = data;
    const callerInfo = callInfo.callerPhone ? `from ${callInfo.callerPhone}` : '';

    // Use location's timezone from database for time display in SMS
    const timeStr = new Date().toLocaleTimeString('en-US', {
      timeZone: schedule.timezone, // This comes from location.timezone in database
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    const locationInfo = callInfo.locationName || 'your location';

    return `🏥 After-Hours Call Alert

      Dr. ${schedule.doctorName}, you have an incoming call ${callerInfo} at ${timeStr}.

      Location: ${locationInfo}
      Call Type: ${this.formatCallType(callInfo.callType)}

      Please check the staff portal for details or call back if needed.

      To stop these notifications, contact your clinic administrator.`;
  }

  /**
   * Build SMS notification message for after-hours call for doctor
   */
  private static buildAfterHoursNotificationMessageForDoctor(
    afterHoursCall: AfterHoursCall,
    doctorId: string,
  ): string {
    const appLinkBuilder = AppLinkBuilder.getInstance();
    const callDetailsLink = appLinkBuilder.getAfterHoursCallViewLink(afterHoursCall.id, doctorId);

    return [
      `You have a message from patient named ${afterHoursCall.patientFullName} who would like to speak with you.`,
      `Please click on below link to see more details: ${callDetailsLink}`,
      `The patient can be reached by dialing: ${afterHoursCall.patientPhoneNumber}`,
    ].join('\n');
  }

  /**
   * Build SMS notification message for after-hours call for patient
   */
  private static buildAfterHoursNotificationMessageForPatient(): string {
    return [
      `Thanks for calling University Retina and Macula Associates Answering Service.`,
      `We have received your request to contact the on-call physician.`,
      `On-call physician should get back to you shortly`,
    ].join(' ');
  }

  /**
   * Format call type for display
   */
  private static formatCallType(callType: CallType): string {
    return getCallTypeName(callType);
  }

  /**
   * Check if notifications are enabled
   */
  private static isNotificationEnabled(): boolean {
    return this.getNotificationConfig().enabled;
  }

  /**
   * Check if after-hours notifications are enabled
   */
  private static isAfterHoursNotificationEnabled(): boolean {
    return this.getNotificationConfig().afterHoursEnabled;
  }

  /**
   * Get notification configuration
   */
  private static getNotificationConfig(): NotificationConfig {
    return ON_CALL_NOTIFICATION_CONFIG;
  }

  /**
   * Check if doctor is rate limited
   */
  private static async isRateLimited(doctorId: string): Promise<boolean> {
    const config = this.getNotificationConfig();
    const cooldownTime = new Date(Date.now() - config.cooldownMinutes * 60 * 1000);

    const recentNotifications = await this.db
      .collection(this.COLLECTION_NAME)
      .where('doctorId', '==', doctorId)
      .where('createdAt', '>', admin.firestore.Timestamp.fromDate(cooldownTime))
      .limit(1)
      .get();

    return !recentNotifications.empty;
  }

  /**
   * Log notification to database
   */
  private static async logNotification(
    notificationData: Omit<OnCallNotification, 'id' | 'createdAt'>,
  ): Promise<OnCallNotification> {
    const doc = await this.db.collection(this.COLLECTION_NAME).add({
      ...notificationData,
      createdAt: admin.firestore.Timestamp.now(),
    });

    return {
      ...notificationData,
      id: doc.id,
      createdAt: new Date(),
    };
  }

  /**
   * Retry failed notification
   */
  static async retryNotification(notificationId: string): Promise<void> {
    try {
      // Get original notification
      const notificationDoc = await this.db
        .collection(this.COLLECTION_NAME)
        .doc(notificationId)
        .get();

      if (!notificationDoc.exists) {
        throw new Error(`Notification ${notificationId} not found`);
      }

      const notification = {
        id: notificationDoc.id,
        ...notificationDoc.data(),
      } as OnCallNotification;

      // Check retry count
      const config = this.getNotificationConfig();
      if (notification.retryCount >= config.maxRetries) {
        throw new Error(`Maximum retry attempts (${config.maxRetries}) exceeded`);
      }

      // Note: In a full implementation, you would fetch the original schedule data
      // and reconstruct the notification data for retry. For now, we just update the status.

      // Resend notification (this is a simplified version)
      // In a full implementation, you'd reconstruct the full notification data
      logger.info({ notificationId }, `Retrying notification ${notificationId}`);

      // Update retry count and status
      await this.db
        .collection(this.COLLECTION_NAME)
        .doc(notificationId)
        .update({
          retryCount: notification.retryCount + 1,
          status: 'retrying',
          updatedAt: admin.firestore.Timestamp.now(),
        });
    } catch (error) {
      logger.error({ error, notificationId }, `Failed to retry notification ${notificationId}`);
      throw error;
    }
  }

  /**
   * Get notification history for a doctor
   */
  static async getNotificationHistory(
    doctorId: string,
    limit: number = 50,
  ): Promise<OnCallNotification[]> {
    try {
      const query = this.db
        .collection(this.COLLECTION_NAME)
        .where('doctorId', '==', doctorId)
        .orderBy('createdAt', 'desc')
        .limit(limit);

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        notificationTime: doc.data().notificationTime?.toDate?.() || new Date(),
      })) as OnCallNotification[];
    } catch (error) {
      logger.error(
        { error, doctorId },
        `Failed to get notification history for doctor ${doctorId}`,
      );
      throw error;
    }
  }

  /**
   * Update notification status (called by Twilio webhook)
   */
  static async updateNotificationStatus(
    messageSid: string,
    status: NotificationStatus,
  ): Promise<void> {
    try {
      // Find notification by messageSid
      const query = this.db
        .collection(this.COLLECTION_NAME)
        .where('smsMessageId', '==', messageSid)
        .limit(1);

      const snapshot = await query.get();

      if (snapshot.empty) {
        logger.warn({ messageSid }, `Notification not found for message SID ${messageSid}`);
        return;
      }

      const doc = snapshot.docs[0];
      await doc.ref.update({
        status,
        updatedAt: admin.firestore.Timestamp.now(),
      });

      logger.info(
        { messageSid, status },
        `Updated notification status for message ${messageSid} to ${status}`,
      );
    } catch (error) {
      logger.error(
        { error, messageSid, status },
        `Failed to update notification status for message ${messageSid}`,
      );
      throw error;
    }
  }

  /**
   * Get notification statistics for a clinic
   */
  static async getNotificationStats(
    clinicId: number,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{
    totalSent: number;
    delivered: number;
    failed: number;
    pending: number;
    retrying: number;
    deliveryRate: number;
  }> {
    try {
      let query = this.db.collection(this.COLLECTION_NAME).where('clinicId', '==', clinicId);

      if (startDate) {
        query = query.where('createdAt', '>=', admin.firestore.Timestamp.fromDate(startDate));
      }
      if (endDate) {
        query = query.where('createdAt', '<=', admin.firestore.Timestamp.fromDate(endDate));
      }

      const snapshot = await query.get();
      const notifications = snapshot.docs.map(doc => doc.data());

      const stats = {
        totalSent: 0,
        delivered: 0,
        failed: 0,
        pending: 0,
        retrying: 0,
        deliveryRate: 0,
      };

      notifications.forEach(notification => {
        const status = notification.status as NotificationStatus;
        switch (status) {
          case 'sent':
            stats.totalSent++;
            break;
          case 'delivered':
            stats.delivered++;
            break;
          case 'failed':
            stats.failed++;
            break;
          case 'pending':
            stats.pending++;
            break;
          case 'retrying':
            stats.retrying++;
            break;
        }
      });

      stats.totalSent = notifications.length;
      stats.deliveryRate = stats.totalSent > 0 ? (stats.delivered / stats.totalSent) * 100 : 0;

      return stats;
    } catch (error) {
      logger.error(
        { error, clinicId, startDate, endDate },
        'Failed to get notification statistics',
      );
      throw error;
    }
  }
}
