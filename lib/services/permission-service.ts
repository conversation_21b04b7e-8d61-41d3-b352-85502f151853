import { FeatureKey, PermissionLevel } from '../../models/auth';
import { RolesRepository } from '../repositories/roles-repository';
import { UsersRepository } from '../repositories/users-repository';
import { UserPermissionsRepository } from '../repositories/user-permissions-repository';
import { PermissionsRepository } from '../repositories/permissions-repository';

function levelRank(level: PermissionLevel): number {
  switch (level) {
    case PermissionLevel.NONE:
      return 0;
    case PermissionLevel.READ:
      return 1;
    case PermissionLevel.WRITE:
      return 2;
    case PermissionLevel.ADMIN:
      return 3;
    default:
      return 0;
  }
}

export class PermissionService {
  private userPermissionsRepo = new UserPermissionsRepository();
  private permissionsRepo = new PermissionsRepository();
  private rolesRepo = new RolesRepository();
  private usersRepo = new UsersRepository();

  // Simple in-memory cache: key -> PermissionLevel, expires at timestamp
  private cache = new Map<string, { level: PermissionLevel; expiresAt: number }>();
  private cacheTtlMs = 60 * 1000; // 60 seconds

  async resolveEffectiveLevel(
    userId: string,
    feature: FeatureKey,
    locationId?: string,
  ): Promise<PermissionLevel> {
    // Map token UID to DB user ID if needed (firebase_uid fallback)
    const effectiveUserId = await this.mapToDatabaseUserId(userId);

    // SUPER_ADMIN bypass: if user has a system role named 'SUPER_ADMIN' with clinic_id IS NULL,
    // grant ADMIN for any feature and ignore scoping
    const isSuperAdmin = await this.isUserSuperAdmin(effectiveUserId);
    if (isSuperAdmin) {
      return PermissionLevel.ADMIN;
    }
    const cacheKey = `${effectiveUserId}:${feature}:${locationId || 'global'}`;
    const cached = this.cache.get(cacheKey);
    const now = Date.now();
    if (cached && cached.expiresAt > now) {
      return cached.level;
    }

    // 1) Overrides first (location-specific, then global)
    const overrideScoped = await this.userPermissionsRepo.getUserOverrides(
      effectiveUserId,
      feature,
      locationId ?? null,
    );
    if (overrideScoped.length > 0) {
      return this.setCache(cacheKey, overrideScoped[0].level);
    }
    const overrideGlobal = await this.userPermissionsRepo.getUserOverrides(
      effectiveUserId,
      feature,
      null,
    );
    if (overrideGlobal.length > 0) {
      return this.setCache(cacheKey, overrideGlobal[0].level);
    }

    // 2) Roles at location
    const rolesAtLocation = await this.userPermissionsRepo.getUserRoles(
      effectiveUserId,
      locationId ?? null,
    );
    const roleIdsScoped = rolesAtLocation.map(r => r.role_id);
    const scopedPermissions = await this.permissionsRepo.getRolePermissions(roleIdsScoped);
    let best: PermissionLevel | undefined;
    scopedPermissions.forEach(p => {
      if (p.feature === feature) {
        if (!best || levelRank(p.level) > levelRank(best)) best = p.level;
      }
    });

    // 3) Global roles
    const globalRoles = await this.userPermissionsRepo.getUserRolesGlobal(effectiveUserId);
    const roleIdsGlobal = globalRoles.map(r => r.role_id);
    const globalPermissions = await this.permissionsRepo.getRolePermissions(roleIdsGlobal);
    globalPermissions.forEach(p => {
      if (p.feature === feature) {
        if (!best || levelRank(p.level) > levelRank(best)) best = p.level;
      }
    });

    return this.setCache(cacheKey, best ?? PermissionLevel.NONE);
  }

  private setCache(key: string, level: PermissionLevel): PermissionLevel {
    this.cache.set(key, { level, expiresAt: Date.now() + this.cacheTtlMs });
    return level;
  }

  // Optional invalidation for writes; can be expanded to targeted keys
  invalidateUser(userId: string): void {
    const prefix = `${userId}:`;
    // Avoid iterating MapIterator directly to support older targets
    Array.from(this.cache.keys()).forEach(cacheKey => {
      if (cacheKey.startsWith(prefix)) {
        this.cache.delete(cacheKey);
      }
    });
  }

  private async isUserSuperAdmin(userId: string): Promise<boolean> {
    try {
      const roles = await this.userPermissionsRepo.getUserRolesGlobal(userId);
      if (roles.length === 0) return false;
      const roleIds = roles.map(r => r.role_id);
      const roleRows = await this.rolesRepo.findByIds(roleIds);
      return roleRows.some(
        r => r.is_system === true && r.clinic_id === null && r.name === 'SUPER_ADMIN',
      );
    } catch {
      return false;
    }
  }

  private async mapToDatabaseUserId(userId: string): Promise<string> {
    try {
      const foundById = await this.usersRepo.findById(userId);
      if (foundById) return foundById.id;
    } catch {}
    try {
      const byFirebase = await this.usersRepo.findByFirebaseUid(userId);
      if (byFirebase) return byFirebase.id;
    } catch {}
    return userId;
  }
}
