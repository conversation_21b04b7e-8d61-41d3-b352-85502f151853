import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isoWeek from 'dayjs/plugin/isoWeek';

import { DEFAULT_TIMEZONE } from '@/app-config';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isoWeek);

/**
 * Timezone utilities for handling appointment times
 */

/**
 * Gets the next business day in the given timezone
 *
 * @param timeZone The timezone to get the next business day for (default is Chicago)
 * @returns The next business day
 */
export function getNextBusinessDay(timeZone = DEFAULT_TIMEZONE): dayjs.Dayjs {
  const now = dayjs().utc().tz(timeZone);
  let nextBusinessDay = now.add(1, 'day');
  // If the next business day is a weekend, add 1 day until it's a weekday, (1 = Monday, 7 = Sunday)
  while (nextBusinessDay.isoWeekday() === 6 || nextBusinessDay.isoWeekday() === 7) {
    nextBusinessDay = nextBusinessDay.add(1, 'day');
  }

  return nextBusinessDay;
}

/**
 * Determines if a date is during Daylight Saving Time in Chicago (Central Time)
 *
 * @param date The date to check
 * @returns true if the date is during DST, false otherwise
 */
export function isChicagoDST(date: Date): boolean {
  // Get the UTC offset in minutes for this date in Chicago
  // Create a date string with Chicago timezone identifier
  const tz = 'America/Chicago';
  const chicagoDate = dayjs(date).tz(tz, true);

  // Standard time offset for Chicago is UTC-6 (360 minutes)
  // DST offset for Chicago is UTC-5 (300 minutes)
  // If the offset is less than standard time, it's DST
  const startOfTheYear = dayjs(date).tz(tz, true).startOf('year');
  const januaryOffset = startOfTheYear.utcOffset() * -1;
  const dateOffset = chicagoDate.utcOffset() * -1;

  return dateOffset < januaryOffset;
}

/**
 * Ensures a date-time string has the correct timezone offset for Chicago (Central Time)
 *
 * @param dateTimeStr The date-time string to format (ISO format)
 * @returns The date-time string with the correct Chicago timezone offset
 */
export function ensureChicagoTimezone(dateTimeStr: string): string {
  // If the string is empty, return it as is
  if (!dateTimeStr) {
    return dateTimeStr;
  }

  // If the string already has a timezone offset, return it as is
  if (dateTimeStr.endsWith('Z') || dateTimeStr.match(/[+-]\d{2}:\d{2}$/)) {
    return dateTimeStr;
  }

  // Parse the date
  const date = new Date(dateTimeStr);

  // Determine if it's during DST
  const isDST = isChicagoDST(date);

  // Format the date with the correct timezone offset
  // Remove any existing timezone information first
  const baseDateTime = dateTimeStr.replace(/Z|[+-]\d{2}:\d{2}$/, '');

  // Add the appropriate timezone offset
  return `${baseDateTime}${isDST ? '-05:00' : '-06:00'}`;
}

/**
 * Ensures both start and end times have the correct timezone offset for Chicago
 *
 * @param startTime The start time string
 * @param endTime The end time string
 * @returns An object with formatted start and end times
 */
export function ensureAppointmentTimezones(
  startTime: string,
  endTime: string,
): { startTime: string; endTime: string } {
  return {
    startTime: ensureChicagoTimezone(startTime),
    endTime: ensureChicagoTimezone(endTime),
  };
}

/**
 * Parse ISO date string to formatted date and time without timezone conversion
 * Directly extracts date and time components from an ISO string like "2025-04-12T09:00:00-04:00"
 *
 * @param isoDateString The ISO date string to parse
 * @returns An object with formatted date and time strings
 */
export function parseISODateString(isoDateString: string): { date: string; time: string } {
  // Extract date parts (YYYY-MM-DD)
  const datePart = isoDateString.split('T')[0];
  const [year, month, day] = datePart.split('-');

  // Convert month number to month name
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const monthName = monthNames[parseInt(month) - 1];

  // Format date as "Month Day, Year"
  const date = `${monthName} ${parseInt(day)}, ${year}`;

  // Extract time parts (HH:MM)
  const timePart = isoDateString.split('T')[1].substring(0, 5);
  const [hourStr, minute] = timePart.split(':');
  const hour = parseInt(hourStr);

  // Format time as "H:MM AM/PM"
  const period = hour >= 12 ? 'PM' : 'AM';
  const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM
  const time = `${hour12}:${minute} ${period}`;

  return { date, time };
}

/**
 * Formats an ISO date string to a simple "DD/MM/YYYY HH:MM" format
 *
 * @param isoDateString The ISO date string to format (e.g. "2025-04-27T07:30:00-04:00")
 * @returns Formatted date-time string (e.g. "27/04/2025 07:30")
 */
export function formatSimpleDateTime(isoDateString: string): string {
  if (!isoDateString) {
    return '';
  }

  // Parse just the components we need without timezone conversion
  const [datePart, timePart] = isoDateString.split('T');

  if (!datePart || !timePart) {
    return '';
  }

  // Extract date components (YYYY-MM-DD)
  const [year, month, day] = datePart.split('-').map(part => part.trim());

  // Extract time components (HH:MM) - take just the first 5 characters which should be HH:MM
  const timeStr = timePart.substring(0, 5);

  // Return the formatted date
  return `${day}/${month}/${year} ${timeStr}`;
}
