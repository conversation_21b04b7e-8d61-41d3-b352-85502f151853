import { callsService } from '@/utils/firestore';
import { AgentLocationMappingService } from '@/lib/services/agent-location-mapping';
import { ClinicsRepository } from '@/lib/repositories/clinics-repository';
import { Clinic } from '@/models/Clinic';
import { Location } from '@/models/Location';
import logger from './logger';
import { ensureDbInitialized } from '@/lib/middleware/db-init';

/**
 * Get clinic information by session ID
 * @param sessionId The session ID to look up
 * @returns Clinic object or null if not found
 */
export async function getClinicBySessionId(sessionId: string): Promise<Clinic | null> {
  try {
    // Get call by session ID
    const calls = await callsService.getCallsBySessionId(sessionId);
    const call = calls.length > 0 ? calls[0] : null;

    if (!call) {
      logger.warn({ sessionId }, `Call not found for session ID ${sessionId}`);
      return null;
    }

    // Get agent ID from call
    const agentId = call.agentId;
    if (!agentId) {
      logger.warn({ sessionId, callId: call.id }, 'Agent ID not found for call');
      return null;
    }

    // Get location by agent ID
    const location = await AgentLocationMappingService.getLocationByAgentId(agentId);
    if (!location) {
      logger.warn({ sessionId, agentId }, `Location not found for agent ID ${agentId}`);
      return null;
    }

    // Get clinic by location's clinic ID
    await ensureDbInitialized();
    const clinicsRepository = new ClinicsRepository();
    const clinic = await clinicsRepository.findById(location.clinicId.toString());

    if (!clinic) {
      logger.warn(
        { sessionId, agentId, locationId: location.id, clinicId: location.clinicId },
        `Clinic not found for location ${location.id} with clinic ID ${location.clinicId}`,
      );
      return null;
    }

    logger.info(
      { sessionId, agentId, locationId: location.id, clinicId: clinic.id, clinicName: clinic.name },
      `Successfully retrieved clinic ${clinic.name} for session ${sessionId}`,
    );

    return clinic;
  } catch (error) {
    logger.error(
      { error: error instanceof Error ? error.message : String(error), sessionId },
      `Error getting clinic by session ID ${sessionId}`,
    );
    throw error;
  }
}

/**
 * Get location information by session ID
 * @param sessionId The session ID to look up
 * @returns Location object or null if not found
 */
export async function getLocationBySessionId(sessionId: string): Promise<Location | null> {
  try {
    // Get call by session ID
    const calls = await callsService.getCallsBySessionId(sessionId);
    const call = calls.length > 0 ? calls[0] : null;

    if (!call) {
      logger.warn({ sessionId }, `Call not found for session ID ${sessionId}`);
      return null;
    }

    // Get agent ID from call
    const agentId = call.agentId;
    if (!agentId) {
      logger.warn({ sessionId, callId: call.id }, 'Agent ID not found for call');
      return null;
    }

    // Get location by agent ID
    const location = await AgentLocationMappingService.getLocationByAgentId(agentId);
    if (!location) {
      logger.warn({ sessionId, agentId }, `Location not found for agent ID ${agentId}`);
      return null;
    }

    logger.info(
      { sessionId, agentId, locationId: location.id },
      `Successfully retrieved location ${location.id} for session ${sessionId}`,
    );

    return location;
  } catch (error) {
    logger.error(
      { error: error instanceof Error ? error.message : String(error), sessionId },
      `Error getting location by session ID ${sessionId}`,
    );
    throw error;
  }
}
