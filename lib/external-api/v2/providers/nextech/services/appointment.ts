import {
  Appointment,
  AppointmentStatus,
  AppointmentType,
  AvailableSlot,
  AppointmentPurpose,
} from '../../../models/types';
import { IAppointmentService } from '@/lib/external-api/v2';
import { CreateAppointmentDto, UpdateAppointmentDto } from '../../../models/dto';
import { ensureAppointmentTimezones } from '@/lib/external-api/v2';
import { NextechApiClient } from '../client';
import {
  NextechAppointmentPurposeResponse,
  mapNextechAppointmentPurposeToAppointmentPurpose,
  FHIRAppointment,
  FHIRBundle,
  FHIRSlot,
  mapFHIRAppointmentToAppointment,
  mapFHIRSlotToAvailableSlot,
  mapFHIRAppointmentTypeToAppointmentType,
} from './mappers';
import { PaginationParams, PaginatedResult, createPaginationLinks } from '@/lib/external-api/v2';
import logger from '../../../utils/logger';
import { getAppointmentTypeName, NextechAppointmentTypes } from '@/models/AppointmentTypes';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';

/**
 * NextechAppointmentService
 * Implements IAppointmentService for Nextech API
 */
export class NextechAppointmentService implements IAppointmentService {
  private readonly defaultLimit = 30;
  private readonly maxLimit = 50;

  constructor(private client: NextechApiClient) {}

  /**
   * Get all appointments with optional filtering
   * @param filters Optional filters for appointments
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of Appointment objects
   */
  async getAppointments(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    try {
      // Extract pagination parameters with defaults and ensure they're valid integers
      const limit = Math.min(
        Math.max(1, parseInt(String(pagination?.limit || this.defaultLimit))),
        this.maxLimit,
      );
      const offset = Math.max(0, parseInt(String(pagination?.offset || 0)));

      // Build query parameters from filters
      const queryParams = new URLSearchParams();

      // Apply filters if provided
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value));
          }
        });
      }

      // The appointment start date. When searching for appointments within a date range, the following prefixes may be used: lt, gt, eq, le, ge
      // replace in queryParams startDate and endDate with date if they exist and remove them from the query params
      if (queryParams.get('startDate')) {
        queryParams.set('date', `ge${queryParams.get('startDate')}`);
        queryParams.delete('startDate');
      }
      if (queryParams.get('endDate')) {
        queryParams.append('date', `lt${queryParams.get('endDate')}`);
        queryParams.delete('endDate');
      }

      // The appointment last updated date. When searching for appointments within a date range, the following prefixes may be used: lt, gt, eq, le, ge
      // replace in queryParams startDate and endDate with date if they exist and remove them from the query params
      if (queryParams.get('startLastUpdated')) {
        queryParams.set('_lastUpdated', `ge${queryParams.get('startLastUpdated')}`);
        queryParams.delete('startLastUpdated');
      }
      if (queryParams.get('endLastUpdated')) {
        queryParams.append('_lastUpdated', `lt${queryParams.get('endLastUpdated')}`);
        queryParams.delete('endLastUpdated');
      }

      // replace in queryParams patientId with patient if it exists and remove it from the query params
      if (queryParams.get('patientId')) {
        queryParams.set('patient', queryParams.get('patientId') as string);
        queryParams.delete('patientId');
      }

      // replace in queryParams id with identifier if it exists and remove it from the query params
      if (queryParams.get('id')) {
        queryParams.set('identifier', queryParams.get('id') as string);
        queryParams.delete('id');
      }

      // Add pagination parameters - FHIR uses _count and _getpagesoffset
      queryParams.set('_count', String(limit));
      queryParams.set('_getpagesoffset', String(offset));

      if (queryParams.get('locationId')) {
        queryParams.set('location.id', queryParams.get('locationId') as string);
        queryParams.delete('locationId');
      } else {
        // If neither patientId nor id is provided, set the default location to URMA_LOMBARD_LOCATION_ID
        if (
          !queryParams.get('patientId') &&
          !queryParams.get('patient') &&
          !queryParams.get('id')
        ) {
          queryParams.set('location.id', URMA_LOMBARD_LOCATION_ID);
        }
      }

      // Make API call
      const url = `/Appointment?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Map API response to common model
      const appointments =
        response.entry && Array.isArray(response.entry)
          ? response.entry
              .filter(entry => {
                // Check if it's an Appointment resource
                return entry.resource && entry.resource.resourceType === 'Appointment';
              })
              .map(entry => mapFHIRAppointmentToAppointment(entry.resource as FHIRAppointment))
          : [];

      // Get total count from response or use array length
      const totalCount = response.total || appointments.length;

      // Extract pagination links from response
      const links: Record<string, string> = {};
      if (response.link && Array.isArray(response.link)) {
        response.link.forEach(link => {
          if (link.relation === 'first') links.first = link.url;
          if (link.relation === 'next') links.next = link.url;
          if (link.relation === 'last') links.last = link.url;
          if (link.relation === 'prev') links.prev = link.url;
        });
      }

      // Return paginated result
      return {
        items: appointments,
        pagination: {
          totalCount,
          limit,
          offset,
          hasMore: offset + appointments.length < totalCount,
          links,
        },
      };
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Appointment Service - Get Appointments',
          error,
        },
        'Error fetching appointments from Nextech API',
      );

      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(
            Math.max(1, parseInt(String(pagination?.limit || this.defaultLimit))),
            this.maxLimit,
          ),
          offset: Math.max(0, parseInt(String(pagination?.offset || 0))),
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get an appointment by ID
   * @param id Appointment ID
   * @returns Promise with Appointment object or null if not found
   */
  async getAppointmentById(id: string): Promise<Appointment | null> {
    try {
      const appointment = await this.client.get<FHIRAppointment>(`/Appointment/${id}`);
      return mapFHIRAppointmentToAppointment(appointment);
    } catch (error) {
      // Return null if appointment not found (404)
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Create a new appointment
   * @param data Appointment data
   * @returns Promise with created Appointment
   */
  async createAppointment(data: CreateAppointmentDto): Promise<Appointment> {
    // Default values for required fields if not provided
    const defaultPractitionerId = '110'; // "Meena George"
    const defaultLocationId = URMA_LOMBARD_LOCATION_ID; // "URMA - Lombard"

    // Transform our DTO to FHIR format for Nextech
    // Create a request object that matches the Nextech API documentation example
    // Define the type to match the Nextech API documentation
    interface NextechAppointmentRequest {
      resourceType: string;
      status: string;
      start: string;
      end: string;
      participant: Array<{
        actor: {
          reference: string;
          display?: string;
        };
        status: string;
      }>;
      extension: Array<{
        url: string;
        valueReference?: {
          reference: string;
          display?: string;
        };
        valueString?: string;
      }>;
      reason?: Array<{ text: string }>;
      comment?: string;
    }

    // Ensure appointment times are in Chicago timezone // todo remove when use other locations
    const { startTime, endTime } = ensureAppointmentTimezones(data.startTime, data.endTime);

    const appointmentRequest: NextechAppointmentRequest = {
      resourceType: 'Appointment',
      status: 'proposed', // Using 'proposed' as per the Nextech example
      start: startTime,
      end: endTime,
      participant: [
        {
          actor: {
            reference: `Patient/${data.patientId}`,
          },
          status: 'accepted',
        },
        {
          actor: {
            reference: `Location/${data.locationId || defaultLocationId}`,
          },
          status: 'accepted',
        },
        {
          actor: {
            reference: `Practitioner/${data.practitionerId || defaultPractitionerId}`,
          },
          status: 'accepted',
        },
      ],
      extension: [],
    };

    // Add appointment type as an extension as per Nextech example
    appointmentRequest.extension.push({
      url: 'appointment-type',
      valueReference: {
        reference: `appointment-type/${data.type || NextechAppointmentTypes.FOLLOW_UP}`,
        display:
          data.type || getAppointmentTypeName(data.type || NextechAppointmentTypes.FOLLOW_UP),
      },
    });

    // Add clinic reference if provided, otherwise use default
    const clinicId = data.clinicId;
    if (clinicId) {
      appointmentRequest.extension.push({
        url: 'practice',
        valueReference: {
          reference: `Practice/${clinicId}`,
          display: 'Practice',
        },
      });
    }

    if (data.reason) {
      // Add reason as a property
      appointmentRequest.reason = [{ text: data.reason }];
    }

    if (data.notes) {
      // Add notes as a comment
      appointmentRequest.comment = data.notes;
    }

    // Cast to RequestData to satisfy TypeScript
    const createdAppointment = await this.client.post<FHIRAppointment>(
      '/Appointment',
      appointmentRequest as unknown as Record<string, unknown>,
    );

    return mapFHIRAppointmentToAppointment(createdAppointment);
  }

  /**
   * Update an existing appointment
   * @param id Appointment ID
   * @param data Updated appointment data
   * @returns Promise with updated Appointment
   * @description According to Nextech API documentation, only the appointment status can be updated
   */
  async updateAppointment(id: string, data: UpdateAppointmentDto): Promise<Appointment> {
    // Get current appointment to merge with updates
    const currentAppointment = await this.getAppointmentById(id);
    if (!currentAppointment) {
      throw new Error(`Appointment with ID ${id} not found`);
    }

    // According to Nextech API documentation, only the status can be updated
    if (!data.status) {
      throw new Error('Appointment status is required for updates');
    }

    // Transform our DTO to Nextech format
    // The request must follow the format specified in the Nextech API documentation
    const nextechAppointmentRequest = {
      resourceType: 'Appointment',
      status: this.mapAppointmentStatusToNextech(data.status),
      participant: [
        {
          status: 'accepted',
        },
      ],
      extension: [
        {
          url: 'modifiedBy',
          valueIdentifier: {
            id: 'modifiedBy',
            value: 'Practice+ Partner',
          },
        },
      ],
    };

    // Make the PATCH request to update the appointment status
    await this.client.patch<FHIRAppointment>(`/Appointment/${id}`, nextechAppointmentRequest);

    // Fetch the updated appointment to return
    const updatedAppointment = await this.getAppointmentById(id);
    if (!updatedAppointment) {
      throw new Error(`Failed to retrieve updated appointment with ID ${id}`);
    }

    return updatedAppointment;
  }

  /**
   * Cancel an appointment
   * @param id Appointment ID
   * @param reason Optional cancellation reason (note: Nextech API doesn't support updating reason during cancellation)
   * @returns Promise with boolean indicating success
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async cancelAppointment(id: string, reason?: string): Promise<boolean> {
    // Update appointment status to cancelled
    const data: UpdateAppointmentDto = {
      status: AppointmentStatus.CANCELLED,
    };

    // Note: According to Nextech API documentation, we can only update the status
    // The reason parameter is kept for interface compatibility but won't be used
    // in the actual API call

    try {
      await this.updateAppointment(id, data);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Confirm an appointment
   * @param id Appointment ID
   * @returns Promise with boolean indicating success
   */
  async confirmAppointment(id: string): Promise<boolean> {
    const data: UpdateAppointmentDto = {
      status: AppointmentStatus.BOOKED,
    };

    try {
      await this.updateAppointment(id, data);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Change appointment data by creating a new appointment with updated fields and canceling the original
   * @param id Original appointment ID
   * @param data Updated appointment data
   * @returns Promise with the newly created appointment
   */
  async changeAppointment(id: string, data: UpdateAppointmentDto): Promise<Appointment> {
    // Get the original appointment
    const originalAppointment = await this.getAppointmentById(id);
    if (!originalAppointment) {
      throw new Error(`Appointment with ID ${id} not found`);
    }

    // Create a new appointment DTO by merging the original appointment with the updated data
    const createDto: CreateAppointmentDto = {
      patientId: data.patientId || originalAppointment.patientId,
      practitionerId: data.practitionerId || originalAppointment.providerId,
      locationId: data.locationId || originalAppointment.locationId,
      clinicId: data.clinicId || originalAppointment.clinicId,
      startTime: data.startTime || originalAppointment.startTime,
      endTime: data.endTime || originalAppointment.endTime,
      type: data.type || originalAppointment.type || NextechAppointmentTypes.FOLLOW_UP,
      reason: data.reason || originalAppointment.reason,
      notes: data.notes || originalAppointment.notes,
    };

    try {
      // Create the new appointment
      const newAppointment = await this.createAppointment(createDto);

      // Cancel the original appointment
      const cancellationSuccess = await this.cancelAppointment(
        id,
        'Replaced by updated appointment',
      );
      if (!cancellationSuccess) {
        logger.warn(
          {
            provider: 'nextech',
            context: 'Appointment Service - Change Appointment',
            originalAppointmentId: id,
            newAppointmentId: newAppointment.id,
          },
          'Created new appointment but failed to cancel the original appointment',
        );
      }

      return newAppointment;
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Appointment Service - Change Appointment',
          originalAppointmentId: id,
          error,
        },
        'Error changing appointment',
      );
      throw error;
    }
  }

  /**
   * Get appointments by patient ID
   * @param patientId Patient ID
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByPatientId(
    patientId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    return this.getAppointments({ patientId }, pagination);
  }

  /**
   * Get appointments by clinic ID
   * @param clinicId Clinic ID
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByClinicId(
    clinicId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    return this.getAppointments({ practiceId: clinicId }, pagination);
  }

  /**
   * Get appointments by location ID
   * @param locationId Location ID
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByLocationId(
    locationId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    return this.getAppointments({ locationId }, pagination);
  }

  /**
   * Get appointments by date
   * @param date Date in YYYY-MM-DD format
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByDate(
    date: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    // Ensure we have a properly formatted date in YYYY-MM-DD format
    // Safely handle various date formats by using a regex to extract or format as YYYY-MM-DD
    const formattedDate = date.match(/^\d{4}-\d{2}-\d{2}$/)
      ? date
      : new Date(date).toISOString().split('T')[0];
    return this.getAppointments(
      {
        startDate: formattedDate,
        endDate: formattedDate,
      },
      pagination,
    );
  }

  /**
   * Formats a date string to YYYY-MM-DD format
   * Handles various input date formats while preserving the intended date
   * @param dateStr Date string in any format
   * @returns Date string in YYYY-MM-DD format
   * @private
   */
  private formatDateToYYYYMMDD(dateStr: string): string {
    // If already in YYYY-MM-DD format, return as-is
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateStr;
    }

    // Parse the date parts
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  /**
   * Get appointments by date range
   * @param startDate Start date in YYYY-MM-DD format
   * @param endDate End date in YYYY-MM-DD format
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByDateRange(
    startDate: string,
    endDate: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    // Ensure we have properly formatted dates in YYYY-MM-DD format
    // Safely handle various date formats by using a regex to extract or format as YYYY-MM-DD
    const formattedStartDate = startDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? startDate
      : this.formatDateToYYYYMMDD(startDate);
    const formattedEndDate = endDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? endDate
      : this.formatDateToYYYYMMDD(endDate);

    return this.getAppointments(
      {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      },
      pagination,
    );
  }

  /**
   * Get appointments by date range and location ID
   * @param startDate Start date in YYYY-MM-DD format
   * @param endDate End date in YYYY-MM-DD format
   * @param locationId Location ID
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByDateRangeAndLocationId(
    startDate: string,
    endDate: string,
    locationId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    // Ensure we have properly formatted dates in YYYY-MM-DD format
    // Safely handle various date formats by using a regex to extract or format as YYYY-MM-DD
    const formattedStartDate = startDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? startDate
      : this.formatDateToYYYYMMDD(startDate);
    const formattedEndDate = endDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? endDate
      : this.formatDateToYYYYMMDD(endDate);

    return this.getAppointments(
      {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        locationId,
      },
      pagination,
    );
  }

  async getAppointmentsByStatus(
    status: AppointmentStatus,
    params: {
      startDate?: string;
      endDate?: string;
      startLastUpdated?: string;
      endLastUpdated?: string;
      locationId?: string;
      patientId?: string;
      pagination?: PaginationParams;
    },
  ): Promise<PaginatedResult<Appointment>> {
    logger.info(
      {
        context: `NextechAppointmentService.getAppointmentsByStatus`,
        status,
        params,
      },
      'Getting appointments by status',
    );
    const {
      startDate,
      endDate,
      startLastUpdated,
      endLastUpdated,
      locationId,
      patientId,
      pagination,
    } = params;
    const finalStatus = this.mapAppointmentStatusToNextech(status);
    const result = await this.getAppointments(
      {
        status: finalStatus,
        startDate,
        endDate,
        startLastUpdated,
        endLastUpdated,
        locationId,
        patientId,
      },
      pagination,
    );
    logger.info(
      {
        context: `NextechAppointmentService.getAppointmentsByStatus`,
        pagination: result.pagination,
      },
      'Appointments by status',
    );

    return result;
  }

  async getAppointmentsByStatuses(params: {
    statuses: AppointmentStatus[];
    startDate?: string;
    endDate?: string;
    startLastUpdated?: string;
    endLastUpdated?: string;
    locationId?: string;
    pagination?: PaginationParams;
  }): Promise<PaginatedResult<Appointment>> {
    const {
      statuses,
      startDate,
      endDate,
      startLastUpdated,
      endLastUpdated,
      locationId,
      pagination,
    } = params;
    logger.info(
      {
        context: `NextechAppointmentService.getAppointmentsByStatuses`,
        statuses,
        params,
      },
      'Getting appointments by status',
    );
    const finalStatuses = statuses.map(status => this.mapAppointmentStatusToNextech(status));
    const result = await this.getAppointments(
      {
        status: finalStatuses.join(','),
        startDate,
        endDate,
        startLastUpdated,
        endLastUpdated,
        locationId,
      },
      pagination,
    );
    logger.info(
      {
        context: `NextechAppointmentService.getAppointmentsByStatuses`,
        pagination: result.pagination,
      },
      'Appointments by status',
    );

    return result;
  }

  /**
   * Get appointments by date range and clinic ID
   * @param startDate Start date in YYYY-MM-DD format
   * @param endDate End date in YYYY-MM-DD format
   * @param clinicId Clinic ID
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByDateRangeAndClinicId(
    startDate: string,
    endDate: string,
    clinicId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    // Ensure we have properly formatted dates in YYYY-MM-DD format
    // Safely handle various date formats by using a regex to extract or format as YYYY-MM-DD
    const formattedStartDate = startDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? startDate
      : this.formatDateToYYYYMMDD(startDate);
    const formattedEndDate = endDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? endDate
      : this.formatDateToYYYYMMDD(endDate);

    return this.getAppointments(
      {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        practiceId: clinicId,
      },
      pagination,
    );
  }

  /**
   * Get appointments by date range and provider ID
   * @param startDate Start date in YYYY-MM-DD format
   * @param endDate End date in YYYY-MM-DD format
   * @param providerId Provider ID
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByDateRangeAndProviderId(
    startDate: string,
    endDate: string,
    providerId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    // Ensure we have properly formatted dates in YYYY-MM-DD format
    // Safely handle various date formats by using a regex to extract or format as YYYY-MM-DD
    const formattedStartDate = startDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? startDate
      : this.formatDateToYYYYMMDD(startDate);
    const formattedEndDate = endDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? endDate
      : this.formatDateToYYYYMMDD(endDate);

    return this.getAppointments(
      {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        providerId,
      },
      pagination,
    );
  }

  /**
   * Get appointments by date range and patient ID
   * @param startDate Start date in YYYY-MM-DD format
   * @param endDate End date in YYYY-MM-DD format
   * @param patientId Patient ID
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentByDateRangeAndPatientId(
    startDate: string,
    endDate: string,
    patientId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>> {
    // Ensure we have properly formatted dates in YYYY-MM-DD format
    // Safely handle various date formats by using a regex to extract or format as YYYY-MM-DD
    const formattedStartDate = startDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? startDate
      : this.formatDateToYYYYMMDD(startDate);
    const formattedEndDate = endDate.match(/^\d{4}-\d{2}-\d{2}$/)
      ? endDate
      : this.formatDateToYYYYMMDD(endDate);

    return this.getAppointments(
      {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        patientId,
      },
      pagination,
    );
  }

  /**
   * Get all appointment types with optional filtering and pagination
   * @param filters Optional filters for appointment types (not used in FHIR implementation)
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointment types
   */
  async getAppointmentTypes(
    _filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<AppointmentType>> {
    try {
      // Note: filters parameter is not used in this FHIR implementation but kept for interface compatibility

      // Extract pagination parameters with defaults and ensure they're valid integers
      const limit = Math.min(
        Math.max(1, parseInt(String(pagination?.limit || this.defaultLimit))),
        this.maxLimit,
      );
      const offset = Math.max(0, parseInt(String(pagination?.offset || 0)));

      // Add pagination parameters - FHIR uses _count and _getpagesoffset
      const queryParams = new URLSearchParams();
      queryParams.set('_count', String(limit));
      queryParams.set('_getpagesoffset', String(offset));

      // Make API call to get FHIR-formatted appointment types
      const url = `/appointment-type?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Map API response to common model
      const appointmentTypes =
        response.entry && Array.isArray(response.entry)
          ? response.entry
              .filter(entry => {
                // Check if it's an appointment-type resource
                return entry.resource && entry.resource.resourceType === 'appointment-type';
              })
              .map(entry => mapFHIRAppointmentTypeToAppointmentType(entry.resource))
          : [];

      // Get total count from response or use array length
      const totalCount = response.total || appointmentTypes.length;

      // Extract pagination links from response
      const links: Record<string, string> = {};
      if (response.link && Array.isArray(response.link)) {
        response.link.forEach(link => {
          if (link.relation === 'first') links.first = link.url;
          if (link.relation === 'next') links.next = link.url;
          if (link.relation === 'last') links.last = link.url;
          if (link.relation === 'prev') links.prev = link.url;
        });
      }

      // Return paginated result
      return {
        items: appointmentTypes,
        pagination: {
          totalCount,
          limit,
          offset,
          hasMore: offset + appointmentTypes.length < totalCount,
          links,
        },
      };
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Appointment Service - Get Appointment Types',
          error,
        },
        'Error fetching appointment types from Nextech API',
      );
      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(pagination?.limit || this.defaultLimit, this.maxLimit),
          offset: pagination?.offset || 0,
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get appointment type by ID
   * @param id Appointment type ID
   * @returns Promise with AppointmentType object or null if not found
   */
  async getAppointmentTypeById(id: string): Promise<AppointmentType | null> {
    try {
      const resource = await this.client.get<{
        resourceType: string;
        id: string;
        extension?: Array<{
          url: string;
          valueString?: string;
        }>;
      }>(`/appointment-type/${id}`);

      return mapFHIRAppointmentTypeToAppointmentType(resource);
    } catch (error) {
      // Return null if appointment type not found (404)
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Get available appointment slots
   * @param filters Required filters for available slots
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of available slots
   */
  async getAvailableSlots(
    filters: {
      appointmentTypeId: string;
      startDate: string;
      endDate: string;
      practitionerId?: string;
      locationId?: string;
      locationIds?: string[];
    },
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<AvailableSlot>> {
    logger.info(
      {
        provider: 'nextech',
        context: 'Appointment Service - Get Available Slots',
        request: {
          filters,
          pagination,
        },
      },
      'Appointment Service - Get Available Slots - request',
    );

    try {
      // Extract pagination parameters with defaults and ensure they're valid integers
      const limit = Math.min(
        Math.max(1, parseInt(String(pagination?.limit || this.defaultLimit))),
        this.maxLimit,
      );
      const offset = Math.max(0, parseInt(String(pagination?.offset || 0)));

      // Validate required parameters
      if (!filters.appointmentTypeId) {
        throw new Error('appointmentTypeId is required');
      }

      // Format dates properly in YYYY-MM-DD format
      const formattedStartDate = filters.startDate.match(/^\d{4}-\d{2}-\d{2}$/)
        ? filters.startDate
        : new Date(filters.startDate).toISOString().split('T')[0];
      const formattedEndDate = filters.endDate.match(/^\d{4}-\d{2}-\d{2}$/)
        ? filters.endDate
        : new Date(filters.endDate).toISOString().split('T')[0];

      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add appointment-type filter
      queryParams.set('appointment-type', `appointment-type/${filters.appointmentTypeId}`);

      // Add date range filters using FHIR search parameters
      queryParams.set('start', `ge${formattedStartDate}`);
      queryParams.append('start', `le${formattedEndDate}`);
      //
      // // Add status filter - we only want free slots
      // queryParams.set("status", "free");

      // Add practitioner filter if specified
      if (filters.practitionerId) {
        // In FHIR, this would typically be a reference to a Schedule resource
        // that belongs to the provider
        queryParams.set('schedule.actor', `practitioner/${filters.practitionerId}`);
      }

      // Add location filter if specified
      if (filters.locationId && !filters.locationIds) {
        // In FHIR, this would typically be a reference to a Location resource
        queryParams.append('schedule.actor', `location/${filters.locationId}`);
      }

      // Add location filter if specified
      if (filters.locationIds) {
        // In FHIR, this would typically be a reference to a Location resource
        const locationReferences = filters.locationIds.map(id => `location/${id}`).join(', ');
        queryParams.append('schedule.actor', locationReferences);
      }

      // Add pagination parameters - FHIR uses _count and _offset
      queryParams.set('_count', String(limit));
      queryParams.set('_offset', String(offset));

      // Make API call using FHIR-compliant URL
      const url = `/slot?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Map API response to common model
      const slots =
        response.entry && Array.isArray(response.entry)
          ? response.entry
              .filter(entry => {
                // Check if it's a Slot resource
                return entry.resource && entry.resource.resourceType === 'Slot';
              })
              .map(entry => mapFHIRSlotToAvailableSlot(entry.resource as FHIRSlot))
          : [];

      // Get total count from response or use array length
      const totalCount = response.total || slots.length;

      // Extract pagination links from response
      const links: Record<string, string> = {};
      if (response.link && Array.isArray(response.link)) {
        response.link.forEach(link => {
          if (link.relation === 'first') links.first = link.url;
          if (link.relation === 'next') links.next = link.url;
          if (link.relation === 'last') links.last = link.url;
          if (link.relation === 'prev') links.prev = link.url;
        });
      }

      // Return paginated result
      return {
        items: slots,
        pagination: {
          totalCount,
          limit,
          offset,
          hasMore: offset + slots.length < totalCount,
          links,
        },
      };
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Appointment Service - Get Available Slots',
          error,
        },
        'Error fetching available slots from Nextech API',
      );
      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(
            Math.max(1, parseInt(String(pagination?.limit || this.defaultLimit))),
            this.maxLimit,
          ),
          offset: Math.max(0, parseInt(String(pagination?.offset || 0))),
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get all appointment purposes with optional filtering
   * @param filters Optional filters for appointment purposes
   * @param pagination Optional pagination parameters
   * @returns Promise with paginated result of appointment purposes
   */
  async getAppointmentPurposes(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<AppointmentPurpose>> {
    try {
      // Extract pagination parameters with defaults and ensure they're valid integers
      const limit = Math.min(
        Math.max(1, parseInt(String(pagination?.limit || this.defaultLimit))),
        this.maxLimit,
      );
      const offset = Math.max(0, parseInt(String(pagination?.offset || 0)));

      // Build parameters from filters
      const params = this.buildAppointmentPurposeQueryParams(filters);

      // Add pagination parameters
      params.limit = limit.toString();
      params.offset = offset.toString();

      // Make API call
      const response = await this.client.get<{
        items: NextechAppointmentPurposeResponse[];
        totalCount?: number;
      }>('/appointment-purposes', { params });

      // Map response to appointment purposes
      const purposes = response.items.map(mapNextechAppointmentPurposeToAppointmentPurpose);

      // Get total count from response or use array length
      const totalCount = response.totalCount || purposes.length;

      // Create pagination links
      const links = createPaginationLinks('/appointment-purposes', totalCount, limit, offset, {});

      // Return paginated result
      return {
        items: purposes,
        pagination: {
          totalCount,
          limit,
          offset,
          hasMore: offset + purposes.length < totalCount,
          links,
        },
      };
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Appointment Service - Get Appointment Purposes',
          error,
        },
        'Error fetching appointment purposes from Nextech API',
      );
      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(
            Math.max(1, parseInt(String(pagination?.limit || this.defaultLimit))),
            this.maxLimit,
          ),
          offset: Math.max(0, parseInt(String(pagination?.offset || 0))),
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get appointment purpose by ID
   * @param id Appointment purpose ID
   * @returns Promise with AppointmentPurpose object or null if not found
   */
  async getAppointmentPurposeById(id: string): Promise<AppointmentPurpose | null> {
    try {
      const appointmentPurpose = await this.client.get<NextechAppointmentPurposeResponse>(
        `/appointment-purposes/${id}`,
      );
      return mapNextechAppointmentPurposeToAppointmentPurpose(appointmentPurpose);
    } catch (error) {
      // Return null if appointment purpose not found (404)
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Build query parameters for appointment purpose filtering
   * @param filters Optional filters
   * @returns Formatted query parameters
   */
  private buildAppointmentPurposeQueryParams(
    filters?: Record<string, unknown>,
  ): Record<string, string> {
    const params: Record<string, string> = {};

    if (!filters) {
      return params;
    }

    // Map our filter keys to Nextech API parameters
    if (filters.name) {
      params.name = String(filters.name);
    }

    if (filters.isActive !== undefined) {
      params.active = String(filters.isActive);
    }

    return params;
  }

  /**
   * Map our AppointmentStatus to Nextech format
   * @param status AppointmentStatus from our enum
   * @returns Nextech status string
   */
  private mapAppointmentStatusToNextech(status: AppointmentStatus): string {
    // Map our enum values to Nextech status values
    switch (status) {
      case AppointmentStatus.PROPOSED:
        return 'pending';
      case AppointmentStatus.PENDING:
        return 'pending';
      case AppointmentStatus.BOOKED:
        return 'booked';
      case AppointmentStatus.ARRIVED:
        return 'arrived';
      case AppointmentStatus.FULFILLED:
        return 'fulfilled';
      case AppointmentStatus.CANCELLED:
        return 'cancelled';
      case AppointmentStatus.NOSHOW:
        return 'noshow';
      default:
        return 'booked';
    }
  }

  /**
   * Get the last fulfilled appointment for a patient
   * @param patientId Patient ID
   * @returns Promise with the most recent fulfilled appointment or null if none found
   */
  async getLastFulfilledAppointmentByPatientId(patientId: string): Promise<Appointment | null> {
    try {
      // Get appointments filtered by patient ID and fulfilled status
      const fulfilledAppointments = await this.getAppointments(
        {
          patientId,
          status: this.mapAppointmentStatusToNextech(AppointmentStatus.FULFILLED),
        },
        {
          limit: 1, // We only need the first (most recent) appointment
          offset: 0,
        },
      );

      // Return the first appointment if any exist, otherwise null
      return fulfilledAppointments.items.length > 0 ? fulfilledAppointments.items[0] : null;
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Appointment Service - Get Last Fulfilled Appointment By Patient ID',
          patientId,
          error,
        },
        'Error fetching last fulfilled appointment for patient from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get appointments for a patient with optional rescheduling logic
   * @param patientId Patient ID
   * @param isForRescheduling Whether to include no-show appointments for rescheduling
   * @returns Promise with paginated result of appointments
   */
  async getAppointmentsForRescheduling(
    patientId: string,
    isForRescheduling: boolean = false,
  ): Promise<PaginatedResult<Appointment>> {
    try {
      // Get current date in YYYY-MM-DD format
      const today = new Date();
      const formattedToday = this.formatDateToYYYYMMDD(today.toISOString());

      // First, get all future appointments for the patient (excluding cancelled)
      const futureAppointments = await this.getAppointments(
        {
          patientId,
          startDate: formattedToday,
        },
        {
          limit: 50, // select all future appointments to avoid edge case when patient has more than 30 future appointments
          offset: 0,
        },
      );

      // Filter out cancelled appointments
      const filteredFutureAppointments = {
        ...futureAppointments,
        items: futureAppointments.items.filter(
          appointment => appointment.status !== AppointmentStatus.CANCELLED,
        ),
      };

      // If we have future appointments or not looking for rescheduling, return the result
      if (filteredFutureAppointments.items.length > 0 || !isForRescheduling) {
        // Sort by date (oldest first)
        filteredFutureAppointments.items.sort((a, b) => {
          return new Date(a.startTime).getTime() - new Date(b.startTime).getTime();
        });
        return filteredFutureAppointments;
      }

      // If we're here, it means we're looking for rescheduling and there are no future appointments
      // Calculate the date 10 days ago
      const tenDaysAgo = new Date();
      tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);
      const formattedTenDaysAgo = this.formatDateToYYYYMMDD(tenDaysAgo.toISOString());

      // Get appointments from the last 10 days
      const recentAppointments = await this.getAppointments({
        patientId,
        startDate: formattedTenDaysAgo,
        endDate: formattedToday,
      });

      // Filter to only include no-show appointments
      const noShowAppointments = recentAppointments.items.filter(
        appointment => appointment.status === AppointmentStatus.NOSHOW,
      );

      // If there are no-show appointments, return the most recent one
      if (noShowAppointments.length > 0) {
        // Sort by date (most recent first)
        noShowAppointments.sort((a, b) => {
          return new Date(b.startTime).getTime() - new Date(a.startTime).getTime();
        });

        // Return only the most recent no-show appointment
        return {
          items: [noShowAppointments[0]],
          pagination: {
            totalCount: 1,
            limit: 1,
            offset: 0,
            hasMore: false,
            links: {},
          },
        };
      }

      // If we get here, there are no future appointments and no recent no-shows
      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: this.defaultLimit,
          offset: 0,
          hasMore: false,
          links: {},
        },
      };
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Appointment Service - Get Appointments For Rescheduling',
          patientId,
          isForRescheduling,
          error,
        },
        'Error fetching appointments for patient from Nextech API',
      );

      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: this.defaultLimit,
          offset: 0,
          hasMore: false,
          links: {},
        },
      };
    }
  }
}
