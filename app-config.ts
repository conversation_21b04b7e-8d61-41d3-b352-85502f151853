// Two factor authentication
export const TWO_FA_ENABLED = true;
/**
 * Set how often a user should go through the 2FA flow.
 */
export const TWO_FA_TTL_IN_SECONDS = 24 * 60 * 60 * 30; // 30 days
export const TWO_FA_RESEND_CODE_RATE_LIMIT_IN_SECONDS = 60; // 1 minute per OTP
export const TWO_FA_CODE_TTL_IN_SECONDS = 60 * 15; // 15 minutes
export const TWO_FA_CODE_LENGTH = 6;

// Global email template variables
export const SUPPORT_TEAM_EMAIL = '<EMAIL>';
export const PRIVACY_POLICY_URL =
  'https://storage.googleapis.com/fdk-frontdesk-doctor-bucket/assets/other/Plato%20Labs%2C%20Inc.%20Privacy%20Policy.pdf';
export const TERMS_OF_SERVICE_URL =
  'https://storage.googleapis.com/fdk-frontdesk-doctor-bucket/assets/other/Plato%20Labs%2C%20Inc.%20Terms%20of%20Service.pdf';

export const URMA_CLINIC_ID = 12;
export const URMA_LOMBARD_LOCATION_ID = '118';
export const NOT_SHOW_REMINDER_TIME_WINDOW_MINUTES = 10;
export const NOT_SHOW_REMINDER_SMS_DRU_RUN = true;
export const NOT_SHOW_REMINDER_CALL_DRU_RUN = false;
export const NOT_SHOW_REMINDER_CALL_RECORDING_ENABLED = false;
export const TRANSFER_TO_CLINIC_REDIRECT_NUMBER: string | undefined = '+18667596357'; // Twilio Toll-free number
export const NOT_SHOW_REMINDER_CALL_REDIRECT_NUMBER: string | undefined = '+19082740595'; // Google voice number
export const BULK_SMS_DRU_RUN = true;
export const URGENT_AFTER_HOURS_DOCTOR_CALL_DELAY_MINUTES = 30; // Delay in minutes before the call is made
export const URGENT_AFTER_HOURS_DOCTOR_CALL_DRU_RUN = false; // If true, will not make a call
export const URGENT_AFTER_HOURS_DOCTOR_CALL_SANDBOX_MODE = false; // If true, will use redirect numbers instead of the original numbers
export const URGENT_AFTER_HOURS_DOCTOR_CALL_DOCTOR_REDIRECT_NUMBER: string = '+19082740595'; // Google voice number
export const URGENT_AFTER_HOURS_DOCTOR_CALL_PATIENT_REDIRECT_NUMBER: string = '+18667596357'; // Twilio Toll-free number

// Office Hours & On-Call Doctors Feature Flags
export const OFFICE_HOURS_FEATURE_ENABLED = true; // Enable/disable office hours features
export const DEFAULT_TIMEZONE = 'America/Chicago'; // Default timezone for office hours calculations

// On-Call Notification Configuration
export const ON_CALL_NOTIFICATION_CONFIG = {
  enabled: process.env.ON_CALL_SMS_ENABLED === 'true',
  afterHoursEnabled: process.env.ON_CALL_SMS_AFTER_HOURS_ENABLED === 'true',
  delayMinutes: parseInt(process.env.ON_CALL_NOTIFICATION_DELAY_MINUTES || '0', 10),
  maxRetries: parseInt(process.env.ON_CALL_MAX_RETRIES || '3', 10),
  cooldownMinutes: parseInt(process.env.ON_CALL_COOLDOWN_MINUTES || '5', 10),
  allowedCallTypes: [3, 4, 0], // NEW_PATIENT_NEW_APPOINTMENT, NEW_APPOINTMENT_EXISTING_PATIENT, OTHER
};

// Default agent-location mappings (example structure)
export const AGENT_LOCATION_MAPPINGS_DEFAULT: Record<string, string> = {
  // agentId: locationId
  'edcb5e7c-94dc-4871-9a22-c109f6014f69': '118',
};

// RBAC Feature Flag
// Default ON in test environment to allow API tests to run without extra env setup
export const ENABLE_NEW_RBAC = process.env.NODE_ENV === 'test' || 'true';
