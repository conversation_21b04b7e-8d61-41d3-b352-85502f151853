/*
  RBAC schema migration
  - roles
  - role_permissions
  - user_roles
  - user_permission_overrides
  - role_audit_logs
  - staff_invite_codes: add assigned_role_id, assigned_location_ids
*/

exports.up = async function up(knex) {
  console.log('Starting RBAC schema migration...');

  // Helper to create table if not exists using raw SQL
  async function createTableIfNotExists(tableName, createSql) {
    const exists = await knex.schema.hasTable(tableName);
    if (exists) {
      console.log(`ℹ️  Table ${tableName} already exists, skipping`);
      return;
    }
    console.log(`Creating table: ${tableName}`);
    await knex.raw(createSql);
    console.log(`✅ Created table: ${tableName}`);
  }

  // roles (clinic-scoped or global templates)
  await createTableIfNotExists(
    'roles',
    `CREATE TABLE roles (
      id CHAR(36) PRIMARY KEY,
      clinic_id INT NULL,
      name VARCHAR(100) NOT NULL,
      description VARCHAR(255) NULL,
      is_system BOOLEAN NOT NULL DEFAULT FALSE,
      is_template BOOLEAN NOT NULL DEFAULT FALSE,
      created_by <PERSON><PERSON>(36) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_roles_clinic (clinic_id),
      UNIQUE KEY uniq_roles_clinic_name (clinic_id, name)
    ) ENGINE=InnoDB`,
  );

  // role_permissions (per-feature grant per role)
  await createTableIfNotExists(
    'role_permissions',
    `CREATE TABLE role_permissions (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      role_id CHAR(36) NOT NULL,
      feature ENUM('BILLING','ANSWERING_SERVICE','STAFF_MANAGEMENT','CALL_LOGS','DASHBOARD','NO_SHOW','BULK_SMS','PRACTICE_MANAGEMENT','USER_MANAGEMENT','ADMIN_TOOLS') NOT NULL,
      level ENUM('NONE','READ','WRITE','ADMIN') NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY uniq_role_feature (role_id, feature),
      INDEX idx_role_permissions_role (role_id),
      CONSTRAINT fk_role_permissions_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
    ) ENGINE=InnoDB`,
  );

  // user_roles (assign roles to users, optional location scope)
  await createTableIfNotExists(
    'user_roles',
    `CREATE TABLE user_roles (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      user_id CHAR(36) NOT NULL,
      role_id CHAR(36) NOT NULL,
      location_id CHAR(36) NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY uniq_user_role_scope (user_id, role_id, location_id),
      INDEX idx_user_roles_user (user_id),
      INDEX idx_user_roles_role (role_id),
      CONSTRAINT fk_user_roles_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
    ) ENGINE=InnoDB`,
  );

  // user_permission_overrides (rare explicit overrides)
  await createTableIfNotExists(
    'user_permission_overrides',
    `CREATE TABLE user_permission_overrides (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      user_id CHAR(36) NOT NULL,
      feature ENUM('BILLING','ANSWERING_SERVICE','STAFF_MANAGEMENT','CALL_LOGS','DASHBOARD','NO_SHOW','BULK_SMS','PRACTICE_MANAGEMENT','USER_MANAGEMENT','ADMIN_TOOLS') NOT NULL,
      level ENUM('NONE','READ','WRITE','ADMIN') NOT NULL,
      location_id CHAR(36) NULL,
      expires_at DATETIME NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY uniq_user_feature_scope (user_id, feature, location_id),
      INDEX idx_user_overrides_user (user_id),
      INDEX idx_user_overrides_expires_at (expires_at)
    ) ENGINE=InnoDB`,
  );

  // role_audit_logs (append-only)
  await createTableIfNotExists(
    'role_audit_logs',
    `CREATE TABLE role_audit_logs (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      actor_user_id CHAR(36) NOT NULL,
      action VARCHAR(50) NOT NULL,
      subject_id CHAR(36) NULL,
      payload JSON NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_role_audit_logs_actor (actor_user_id),
      INDEX idx_role_audit_logs_action (action),
      INDEX idx_role_audit_logs_created (created_at)
    ) ENGINE=InnoDB`,
  );

  // Extend staff_invite_codes
  async function ensureColumn(tableName, columnName, alterCallback) {
    const hasCol = await knex.schema.hasColumn(tableName, columnName);
    if (!hasCol) {
      console.log(`➕ Adding column ${tableName}.${columnName}`);
      await knex.schema.alterTable(tableName, alterCallback);
    }
  }

  await ensureColumn('staff_invite_codes', 'assigned_role_id', t => t.string('assigned_role_id', 36).nullable().index());
  await ensureColumn('staff_invite_codes', 'assigned_location_ids', t => t.json('assigned_location_ids'));

  console.log('🎉 RBAC schema migration completed');
};

exports.down = async function down(knex) {
  console.log('Reverting RBAC schema migration...');

  // Drop RBAC tables
  const tables = [
    'role_audit_logs',
    'user_permission_overrides',
    'user_roles',
    'role_permissions',
    'roles',
  ];

  for (const table of tables) {
    await knex.raw(`DROP TABLE IF EXISTS ${table}`);
  }

  // Remove added columns from staff_invite_codes if exist
  async function dropColumnIfExists(tableName, columnName) {
    const hasCol = await knex.schema.hasColumn(tableName, columnName);
    if (hasCol) {
      await knex.schema.alterTable(tableName, t => t.dropColumn(columnName));
    }
  }

  await dropColumnIfExists('staff_invite_codes', 'assigned_role_id');
  await dropColumnIfExists('staff_invite_codes', 'assigned_location_ids');

  console.log('✅ RBAC schema rollback complete');
};


