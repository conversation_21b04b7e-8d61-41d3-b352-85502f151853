/*
  Extend RBAC feature ENUMs to include platform-level features used by <PERSON><PERSON>ER_ADMIN
*/

exports.up = async function up(knex) {
  // Extend role_permissions.feature ENUM
  await knex.raw(`
    ALTER TABLE role_permissions
    MODIFY feature ENUM(
      'BILLING','ANSWERING_SERVICE','STAF<PERSON>_MANAGEMENT','CALL_LOGS','DASHBOARD','NO_SHOW','BULK_SMS','PRACTICE_MANAGEMENT','USER_MANAGEMENT','ADMIN_TOOLS',
      'PLATFORM_ADMIN','CLINIC_CREATE','LOCATION_CREATE','INVITE_ACCOUNT_OWNER'
    ) NOT NULL
  `);

  // Extend user_permission_overrides.feature ENUM
  await knex.raw(`
    ALTER TABLE user_permission_overrides
    MODIFY feature ENUM(
      'BILLING','ANSWERING_SERVICE','STAFF_MANAGEMENT','CALL_LOGS','DASHBOARD','NO_SHOW','B<PERSON><PERSON><PERSON>_SMS','PRACTICE_MANAGEMENT','US<PERSON>_MANAGEMENT','ADMIN_TOOLS',
      'PLATFORM_ADMIN','CLINIC_CREATE','LOCATION_CREATE','INVITE_ACCOUNT_OWNER'
    ) NOT NULL
  `);
};

exports.down = async function down(knex) {
  // Revert ENUMs to original set without platform-level features
  await knex.raw(`
    ALTER TABLE role_permissions
    MODIFY feature ENUM(
      'BILLING','ANSWERING_SERVICE','STAFF_MANAGEMENT','CALL_LOGS','DASHBOARD','NO_SHOW','BULK_SMS','PRACTICE_MANAGEMENT','USER_MANAGEMENT','ADMIN_TOOLS'
    ) NOT NULL
  `);

  await knex.raw(`
    ALTER TABLE user_permission_overrides
    MODIFY feature ENUM(
      'BILLING','ANSWERING_SERVICE','STAFF_MANAGEMENT','CALL_LOGS','DASHBOARD','NO_SHOW','BULK_SMS','PRACTICE_MANAGEMENT','USER_MANAGEMENT','ADMIN_TOOLS'
    ) NOT NULL
  `);
};


