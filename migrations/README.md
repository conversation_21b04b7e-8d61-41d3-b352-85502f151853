## Knex Migrations Guide

This project uses Knex.js migrations to manage the MySQL schema. Migrations live in the `migrations/` folder and are executed via package scripts and the Knex CLI configured by `knexfile.js`.

### Prerequisites

- Node.js and pnpm installed
- MySQL instance reachable
- `.env.local` with MySQL settings (used by `knexfile.js`):

```dotenv
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=frontdesk_ai
MYSQL_USER=root
MYSQL_PASSWORD=secret
```

### Environment configuration

- The Knex CLI loads configuration from `knexfile.js`, which in turn reads `.env.local`.
- If you need a different environment, export `MYSQL_*` variables before running commands or temporarily adjust `.env.local`.

## Commands (quick reference)

```bash
# Apply all pending migrations
pnpm db:migrate

# Roll back the last batch
pnpm db:rollback

# Reset DB: rollback all, then re-apply
pnpm db:reset

# Initialize database via custom script (optional)
pnpm db:init

# Create a new migration file
pnpm knex migrate:make <migration_name>

# Show migration status
pnpm knex migrate:status

# Apply only a specific migration file (by filename)
pnpm knex migrate:up 004_call_sessions_send_new_patient_form.js

# Roll back only a specific migration file (by filename)
pnpm knex migrate:down 004_call_sessions_send_new_patient_form.js
```

## Creating a migration

```bash
pnpm knex migrate:make add_example_column_to_table
```

This creates a timestamped file in `migrations/` like `YYYYMMDDHHmmss_add_example_column_to_table.js`.

### Template and example

Each migration exports `up` and `down` functions. Always write idempotent logic using `hasTable`/`hasColumn` checks so reruns are safe.

```js
// migrations/XXXXXXXXXXXXXX_add_example_column_to_table.js
exports.up = async function (knex) {
  const hasColumn = await knex.schema.hasColumn('some_table', 'example');
  if (!hasColumn) {
    await knex.schema.alterTable('some_table', (t) => {
      t.string('example', 255).nullable().index();
    });
  }
};

exports.down = async function (knex) {
  const hasColumn = await knex.schema.hasColumn('some_table', 'example');
  if (hasColumn) {
    await knex.schema.alterTable('some_table', (t) => {
      t.dropColumn('example');
    });
  }
};
```

## Running migrations

- Apply all pending migrations:

```bash
pnpm db:migrate
```

- Apply a single migration (example: `004_call_sessions_send_new_patient_form.js`):

```bash
pnpm knex migrate:up 004_call_sessions_send_new_patient_form.js
```

- Roll back last batch or a single file:

```bash
# last batch
pnpm db:rollback

# single file
pnpm knex migrate:down 004_call_sessions_send_new_patient_form.js
```

- Reset database schema (dangerous in shared environments):

```bash
pnpm db:reset
```

## Best practices

- Write idempotent `up` and `down` using `hasTable`/`hasColumn` to avoid errors if re-run.
- Keep operations transactional where possible (Knex wraps many schema changes in transactions depending on the dialect).
- Avoid destructive changes in `up` unless absolutely required; prefer additive changes, then clean up in a later migration.
- Keep migrations small, focused, and named clearly.

## Programmatic execution

If needed, you can run migrations programmatically using `MySQLService`:

```ts
// lib/database/mysql-service.ts
await MySQLService.getInstance().runMigrations();
```

## Troubleshooting

- Connection errors: verify `.env.local` MySQL values and DB availability.
- "Unknown database": create the database named in `MYSQL_DATABASE` or run a DB init script.
- "Duplicate column" or similar: ensure your `up`/`down` guards (`hasColumn`, `hasTable`) are present.
- Need to re-run a single migration: roll it back by filename, then apply it again.

## Related docs

- `knexfile.js` — Knex CLI configuration
- `docs/firestore-to-mysql-migration.md` — end-to-end Firestore ➜ MySQL guidance


