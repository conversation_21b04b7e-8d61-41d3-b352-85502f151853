'use client';

import { useState } from 'react';
import { FiChevronDown, FiMapPin } from 'react-icons/fi';
import { useLocationContext } from '@/components/LocationContext';

interface LocationSelectorProps {
  className?: string;
}

const LocationSelector: React.FC<LocationSelectorProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [switching, setSwitching] = useState(false);

  // Use location context
  const {
    currentLocation,
    availableLocations,
    allLocations,
    availablePractices,
    loading,
    switchLocation: contextSwitchLocation,
  } = useLocationContext();

  // Switch to a different location
  const switchLocation = async (locationId: string) => {
    try {
      setSwitching(true);
      await contextSwitchLocation(locationId);
      setIsOpen(false);
    } catch (error) {
      console.error('Error switching location:', error);
    } finally {
      setSwitching(false);
    }
  };

  // Get practice name for a location
  const getPracticeName = (practiceId: string): string => {
    const practice = availablePractices.find(p => p.id === practiceId);
    return practice?.name || 'Unknown Practice';
  };

  // Check if a location is available to the user
  const isLocationAvailable = (locationId: string): boolean => {
    return availableLocations.some(loc => String(loc.id) === String(locationId));
  };

  // Don't render if no locations at all or still loading
  if (loading || allLocations.length === 0) {
    // Show a helpful message if not loading but no locations found
    if (!loading && allLocations.length === 0) {
      return (
        <div className={`flex items-center text-white/90 ${className}`}>
          <FiMapPin className="h-4 w-4 mr-2" />
          <span className="text-sm font-medium">No locations available</span>
        </div>
      );
    }
    return null;
  }

  // Don't render if only one location available and it's the only location in the clinic
  if (allLocations.length === 1 && availableLocations.length === 1) {
    return (
      <div className={`flex items-center text-white/90 min-w-0 ${className}`}>
        <FiMapPin className="h-4 w-4 mr-2 flex-shrink-0" />
        <span
          className="text-sm font-medium truncate"
          title={currentLocation?.name || 'No Location'}
        >
          {currentLocation?.name || 'No Location'}
        </span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={switching}
        className="flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 bg-white/10 hover:bg-white/20 rounded-lg px-3 py-2 text-sm font-medium min-w-0"
        title={currentLocation?.name || 'Select Location'}
      >
        <FiMapPin className="h-4 w-4 flex-shrink-0" />
        <span className="truncate">{currentLocation?.name || 'Select Location'}</span>
        <FiChevronDown
          className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />

          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
            <div className="py-2 max-h-[70vh] overflow-y-auto">
              <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                All Locations
              </div>

              {allLocations.map(location => {
                const isAvailable = isLocationAvailable(location.id);
                const isCurrent = String(location.id) === String(currentLocation?.id || '');

                return (
                  <button
                    key={location.id}
                    onClick={() => isAvailable && switchLocation(location.id)}
                    disabled={switching || !isAvailable || isCurrent}
                    aria-disabled={switching || !isAvailable || isCurrent}
                    title={
                      isCurrent
                        ? 'Current location'
                        : isAvailable
                          ? `Switch to ${location.name}`
                          : 'You do not have access to this location'
                    }
                    className={`w-full text-left px-4 py-3 transition-colors duration-150 ${
                      isCurrent
                        ? 'bg-blue-50 text-blue-700'
                        : isAvailable
                          ? 'text-gray-700 hover:bg-gray-50'
                          : 'text-gray-400 bg-gray-50 pointer-events-none opacity-60'
                    } ${
                      switching || !isAvailable || isCurrent
                        ? 'cursor-not-allowed'
                        : 'cursor-pointer'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm flex items-center min-w-0">
                          <span className="truncate flex-1" title={location.name}>
                            {location.name}
                          </span>
                          {isCurrent && (
                            <span className="ml-2 text-xs text-blue-600 font-normal flex-shrink-0">
                              (Current)
                            </span>
                          )}
                          {!isAvailable && (
                            <span className="ml-2 text-xs text-gray-500 font-normal flex-shrink-0">
                              (Not Available)
                            </span>
                          )}
                        </div>
                        <div
                          className="text-xs text-gray-500 mt-1 truncate"
                          title={getPracticeName(location.practiceId)}
                        >
                          {getPracticeName(location.practiceId)}
                        </div>
                        {location.address && (
                          <div
                            className="text-xs text-gray-400 mt-1 truncate"
                            title={location.address}
                          >
                            {location.address}
                          </div>
                        )}
                        {!isAvailable && (
                          <div className="text-xs text-orange-600 mt-1 italic truncate">
                            Contact admin for access
                          </div>
                        )}
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LocationSelector;
